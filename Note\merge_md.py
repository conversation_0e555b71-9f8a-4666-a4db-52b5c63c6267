import os

# --- 配置项 ---
# 根目录 ('.' 表示当前目录)
ROOT_DIRECTORY = '.'
# 输出合并后的文件名
OUTPUT_FILENAME = 'merged_output.md'
# --- 配置结束 ---

def merge_markdown_files(root_dir, output_file):
    """
    遍历指定目录及其子目录, 将所有 .md 文件合并到一个输出文件中.
    
    :param root_dir: 要搜索的根目录.
    :param output_file: 合并后输出的文件名.
    """
    # 获取输出文件的绝对路径, 以便后续跳过
    output_filepath = os.path.abspath(os.path.join(root_dir, output_file))
    
    # 收集所有符合条件的 .md 文件路径
    md_files_to_merge = []
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            if filename.endswith('.md'):
                full_path = os.path.abspath(os.path.join(dirpath, filename))
                # 确保不会把输出文件自己也加进去
                if full_path != output_filepath:
                    md_files_to_merge.append(full_path)

    if not md_files_to_merge:
        print("在当前目录下未找到任何 .md 文件.")
        return

    # 按文件路径排序, 保证每次合并顺序一致
    md_files_to_merge.sort()

    # 以写入模式打开输出文件, 使用 utf-8 编码
    with open(output_file, 'w', encoding='utf-8') as outfile:
        print(f"开始合并, 将创建或覆盖文件: {output_file}\n")
        
        for file_path in md_files_to_merge:
            # 获取相对于根目录的路径, 用于标记
            relative_path = os.path.relpath(file_path, root_dir)
            # 统一路径分隔符为 '/', 更加美观
            relative_path = relative_path.replace('\\', '/')
            
            print(f"正在添加: {relative_path}")
            
            # --- 写入文件标记 ---
            # 使用 Markdown 的水平分割线和二级标题来标记文件来源
            outfile.write(f"\n\n---\n\n")
            outfile.write(f"## 文件来源: `{relative_path}`\n\n")
            
            # 以读取模式打开源文件, 使用 utf-8 编码
            try:
                with open(file_path, 'r', encoding='utf-8') as infile:
                    # 读取内容并写入
                    content = infile.read()
                    outfile.write(content)
            except Exception as e:
                # 如果某个文件读取失败, 打印错误并继续
                error_message = f"!!! 读取文件失败: {relative_path}, 错误: {e}\n"
                print(error_message)
                outfile.write(f"_{error_message}_")

    print(f"\n合并完成! 共 {len(md_files_to_merge)} 个文件已合并到 {output_file}")


if __name__ == '__main__':
    merge_markdown_files(ROOT_DIRECTORY, OUTPUT_FILENAME)