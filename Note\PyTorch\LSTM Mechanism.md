# LSTM Mechanism

---



### Basic LSTM

![img](https://cdn.img2ipfs.com/ipfs/Qmf8Y2CVCK8WygETNVkGcMSBbHg7wt44PziHCu2YhhUkSN?filename=Qmf8Y2CVCK8WygETNVkGcMSBbHg7wt44PziHCu2YhhUkSN)

> https://stackoverflow.com/a/48305882



input: 特征的 Embedding, 大小为 (N, T, C), 即 (batch_size, 时间步长度, 特征维度)

hidden_state: LSTM 的**最后一时间步**输出的隐藏状态, (W, N, hidden_size), 即(LSTM层数, batch_size, 隐藏状态数)

cell_state: LSTM 的**最后一时间步**输出的细胞状态, (W, N, hidden_size), 即 (LSTM层数, batch_size, 隐藏状态数)

output: LSTM 的所有**历史,最后一LSTM层**的隐藏状态输出, (N, T, C), 即 (batch_size, 时间步长度, 特征维度)



**单个LSTM元**

**内部机制**

- 输入门(Input Gate)
- 遗忘门(Forget Gate)
- 输出门(Output Gate)

![](https://cdn.img2ipfs.com/ipfs/QmR9iu6TEZnpNSH2fjs4SsaEG3yKP1iWyenVUdDt4WtHqr?filename=QmR9iu6TEZnpNSH2fjs4SsaEG3yKP1iWyenVUdDt4WtHqr)

> https://d2l.ai/chapter_recurrent-modern/lstm.html

 一个示例的使用代码如下：

```python
input_dim = 10
hidden_dim = 5

with torch.no_grad():
    rnn = nn.LSTM(input_dim, hidden_dim, num_layers=3, batch_first=True)
    input = torch.randn((1, 1, input_dim)) # batch_size=1, time_step=1
    output, (hidden, cell) = rnn(input)
```

输出结果如下:

```python
torch.Size([1, 1, 5]) # output
tensor([[[-0.1225, -0.0975, -0.1906, -0.0764, -0.0833]]])

torch.Size([3, 1, 5]) # hidden_state
tensor([[[-0.0058,  0.2086,  0.2167,  0.1651,  0.0763]],

        [[ 0.0567,  0.0160,  0.0928,  0.0125, -0.1650]],

        [[-0.1225, -0.0975, -0.1906, -0.0764, -0.0833]]])
        
torch.Size([3, 1, 5]) # cell_state
tensor([[[-0.0610,  0.7061,  0.5079,  0.4378,  0.1176]],

        [[ 0.1030,  0.0482,  0.1379,  0.0240, -0.3030]],

        [[-0.2639, -0.1842, -0.3453, -0.1486, -0.1636]]])
```

可以看到, `hidden` 的最上层的输出即为 `output` 的输出.

- output 仅包含了最上层的 LSTM 在时间步上的每一步的输出;
- hidden_state 包含了所有层在最后一个时间步的输出;







### Seq2Seq With Attention

```python
import torch
import torch.nn as nn
import torch.nn.functional as F

class Attention(nn.Module):
    def __init__(self, hidden_size):
        super(Attention, self).__init__()
        # 仅在 Bahdanau Attention 中有使用
        # self.attn = nn.Linear(hidden_size * 2, hidden_size)
        # self.v = nn.Parameter(torch.rand(hidden_size))

    def forward(self, hidden, encoder_outputs):
        """
        :param hidden: 当前解码器的隐藏状态 (batch_size, hidden_size)
        :param encoder_outputs: 编码器的所有输出 (batch_size, seq_len, hidden_size)
        :return: context (batch_size, hidden_size), attn_weights (batch_size, seq_len)
        """
        # 计算注意力分数，通过点积计算相似度
        attn_scores = torch.matmul(encoder_outputs, hidden.unsqueeze(2)).squeeze(2)  # (batch_size, seq_len)
        attn_weights = F.softmax(attn_scores, dim=1)  # (batch_size, seq_len)
        
        # 计算加权的上下文向量
        context = torch.bmm(attn_weights.unsqueeze(1), encoder_outputs)  # (batch_size, 1, hidden_size)
        context = context.squeeze(1)  # (batch_size, hidden_size)
        
        return context, attn_weights

class Seq2SeqWithAttention(nn.Module):
    def __init__(self, input_size, output_size, hidden_size):
        super(Seq2SeqWithAttention, self).__init__()
        self.encoder = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.decoder = nn.LSTM(output_size + hidden_size, hidden_size, batch_first=True)  # decoder输入是目标 + context
        self.attention = Attention(hidden_size)
        self.fc_out = nn.Linear(hidden_size, output_size)

    def forward(self, src, tgt):
        """
        :param src: 输入序列 (batch_size, seq_len, input_size)
        :param tgt: 目标序列 (batch_size, seq_len, output_size)
        :return: 输出序列 (batch_size, seq_len, output_size)
        """
        # 编码器部分
        encoder_outputs, (hidden, cell) = self.encoder(src)  # encoder_outputs: (batch_size, seq_len, hidden_size)
        
        # 解码器部分
        outputs = []  # 存储所有时间步的输出
        for t in range(tgt.size(1)):  # tgt.size(1) 是目标序列的长度
            # 获取当前的上下文向量 (batch_size, hidden_size)
            context, _ = self.attention(hidden[-1], encoder_outputs)
            
            # decoder_input 是目标序列的当前时刻的输入 (batch_size, 1, output_size)
            decoder_input = tgt[:, t, :].unsqueeze(1)  # (batch_size, 1, output_size)
            
            # 将目标输入和上下文向量拼接 (batch_size, 1, output_size + hidden_size)
            decoder_input = torch.cat([decoder_input, context.unsqueeze(1)], dim=-1)  
            
            # 通过LSTM解码器 (batch_size, 1, hidden_size)
            decoder_output, (hidden, cell) = self.decoder(decoder_input, (hidden, cell))  # decoder_output: (batch_size, 1, hidden_size)
            
            # 输出层 (batch_size, output_size)
            output = self.fc_out(decoder_output.squeeze(1))  # (batch_size, output_size)
            
            # 将结果添加到输出列表
            outputs.append(output)

        return torch.stack(outputs, dim=1)  # (batch_size, seq_len, output_size)

```



**Attention 的实现有三种**:

- **Bahdanau Attention**（加性注意力）：最早的注意力机制，基于加法计算相似度。
- **Luong Attention**（点积注意力）：基于点积计算相似度，相比Bahdanau更为高效。
- **Self-Attention**（自注意力）：通常用于Transformer架构，解码器的每个位置不仅可以关注输入的不同部分，还可以关注自己序列中的其他位置。



### Luong Attention

使用当前的解码器状态, 与编码器中的所有时间步的隐藏(?状态?)进行点积, 获取注意力得分. 表示获取与当前解码状态最有关的几个激活值; 并由此获取 Context





















