# AFL 使用说明

---



> https://lcamtuf.coredump.cx/afl/
>
> https://github.com/google/AFL
>
> https://afl-1.readthedocs.io/en/latest/user_guide.html



[AFL操作白皮书](https://lcamtuf.coredump.cx/afl/technical_details.txt)

https://lcamtuf.coredump.cx/afl/technical_details.txt

### 1. 基础使用命令

```shell
afl-fuzz -Q -m 4096M -i ../AFL_test/input -o ../AFL_test/output -- gzip @@
```



### 2. 常用参数说明

```markdown
必要参数:
	-i: 输入目录, 目录中存放输入的测试用例文件
	-o: 输出目录, AFL 会在此目录中存储所有的(中间以及最终)结果

控制参数
    -M: Master Fuzzer, 同时多个核心 fuzzing 时使用
    -S: Slave Fuzzer, 多核心 fuzzing 时设置从属 fuzzer
    -f: ?输入测试用例的内容, 会重定向到 afl_test 的 stdin 中
    -x: ?设置用户提供的 tokens, 在 dictionary 编译阶段会使用到用户提供的 token
    -t: 设置测试的目标程序执行的超时时间, 单位为毫秒(ms)
    -m: 设置分配的内存空间, 可以是合法的内存大小字符"\d+[TGkM]", 如"50M"
    -b: 高版本选项, 用于将 fuzzing 进程绑定到指定的 CPU 核心, 接受一个整数参数"%u"

fuzzing 偏好设置
    -d: 开启快速模式(脏模式), 跳过一部分确定性阶段
    -B: 无文档选项, 功能为加载 bitmap, 用于直接加载特定的测试用例进行变异(而不重新探索任何先前发现的用例)
    -C: 崩溃探索模式
    -V: 高版本选项, 用于显示 AFL 的版本号, 并且退出进程
    -n: 沉默模式(dump mode), 即不插桩的 fuzz (黑盒模式)
    -T: 指定屏幕中显示的文本横幅文本内容
    -Q: 启用 QEMU User Mode 二进制插桩

确定阶段 deterministic fuzzing:
    *   bitflip     -- 位反转
    *   arithmetic  -- 算数变异, 分别对 byte, word, dword 进行整数加减变异
    *   interest    -- 内容替换, 将"有意思"的内容(主要是边界值)替换到输入文件中, 分别对 byte, word, dword 进行替换
    *   dictionary  -- 字典, 会将字典值分别[替换]或[插入]到输入文件中, 用户提供的 token 可以使用-x 提供
    * 非确定阶段
    *   havoc       -- 大破坏, 为随机变异策略的一部分, 会对上述的多种变异进行组合
    *   splice      -- 文件拼接, 会将候选队列中的文件随机选择一个, 二者分别切半并拼接
    *   cycle       -- 循环往复, 即右上角的 cycles done, 表示队列循环数. 只有第一次 cycle 才会进行确定性 fuzzing
    */
```



### 3. 种子变异策略

`AFL` 的种子变异策略, 主要包括**确定阶段(deterministic)**与**非确定阶段(non-deterministic)**.

其中, **确定阶段**包含以下几种基本策略:

   *   `bitflip`: 位反转, 即按照一定的间隔挑选比特进行位反转
   *   `arithmetic`: 算数变异, 分别对 `byte`, `word`, `dword` 进行整数加减变异
   *   `interest`: 内容替换, 将"有意思"的内容(主要是边界值)替换到输入文件中, 分别对 `byte`, `word`, `dword` 进行替换
   *   `dictionary`: 字典, 会将字典值分别[替换]或[插入]到输入文件中, 用户提供的 `token` 可以使用 `-x` 选项提供

**非确定阶段**包含以下几种策略

*   `havoc`: 大破坏, 为随机变异策略的一部分, 会对上述的多种变异进行组合
*   `splice`: 文件拼接, 会将候选队列中的文件随机选择一个, 二者分别切半并拼接
*   `cycle`: 循环往复, 即右上角的 *cycles done*, 表示队列循环数. 只有第一次 *cycle* 才会进行deterministic fuzzing



### 4. 关闭 core_pattern (Apport 服务)

通常而言 `/proc/sys/kernel/core_pattern` 处于开启状态, AFL 会提示可以通过以下命令进行配置(ROOT 权限下):

```sh
echo core >/proc/sys/kernel/core_pattern
```

每次重启后都要重新执行此命令, 可以通过以下配置进行持久化设置:

```
# 1. 设置 /etc/default/apport 文件, 修改为以下内容:
enabled=0
# 2. 在 /etc/sysctl.d 目录中创建一个类似于 60-core-pattern.conf 的文件, 并写入以下内容:
kernel.core_pattern = core
```





### 5.  CPU 频率缩放

> https://whoisnian.com/2023/08/25/Ubuntu解锁睿频限制/

为了达到更好的性能, AFL 对 CPU 的频率有要求. 具体而言, AFL 在启动前会进行 CPU 频率检查, 可以通过 `AFL_SKIP_CPUFREQ` 环境变量关闭此检查. 如果当前的 CPU 不满足要求, 会出现如下提示:

```
[-] Whoops, your system uses on-demand CPU frequency scaling, adjusted
    between 781 and 5371 MHz. Unfortunately, the scaling algorithm in the
    kernel is imperfect and can miss the short-lived processes spawned by
    afl-fuzz. To keep things moving, run these commands as root:

    cd /sys/devices/system/cpu
    echo performance | tee cpu*/cpufreq/scaling_governor

    You can later go back to the original state by replacing 'performance'
    with 'ondemand' or 'powersave'. If you don't want to change the settings,
    set AFL_SKIP_CPUFREQ to make afl-fuzz skip this check - but expect some
    performance drop.

[-] PROGRAM ABORT : Suboptimal CPU scaling governor
         Location : check_cpu_governor(), src/afl-fuzz-init.c:2572
```



在 root 权限下执行如下命令:

```shell
cd /sys/devices/system/cpu
echo performance | tee cpu*/cpufreq/scaling_governor
```













































