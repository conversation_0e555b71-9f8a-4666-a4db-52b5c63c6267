# JMP 指令详解

## 概述

<GeminiOptimizationFrom> Note/AssemblyCode/JMP.md </GeminiOptimizationFrom>

JMP 指令是 x86 汇编语言中的无条件跳转指令，用于改变程序的执行流程。理解 JMP 指令的各种形式对于汇编编程、逆向工程和系统级调试至关重要。

## 参考资料

> 官方文档：https://www.felixcloutier.com/x86/jmp

## 指令格式表

| Opcode      | Instruction  | Op/En | 64-Bit Mode | Compat/Leg Mode | Description                                                  |
| ----------- | ------------ | ----- | ----------- | --------------- | ------------------------------------------------------------ |
| EB cb       | JMP rel8     | D     | Valid       | Valid           | Jump short, RIP = RIP + 8-bit displacement sign extended to 64-bits. |
| E9 cw       | JMP rel16    | D     | N.S.        | Valid           | Jump near, relative, displacement relative to next instruction. Not supported in 64-bit mode. |
| E9 cd       | JMP rel32    | D     | Valid       | Valid           | Jump near, relative, RIP = RIP + 32-bit displacement sign extended to 64-bits. |
| FF /4       | JMP r/m16    | M     | N.S.        | Valid           | Jump near, absolute indirect, address = zero-extended r/m16. Not supported in 64-bit mode. |
| FF /4       | JMP r/m32    | M     | N.S.        | Valid           | Jump near, absolute indirect, address given in r/m32. Not supported in 64-bit mode. |
| FF /4       | JMP r/m64    | M     | Valid       | N.E.            | Jump near, absolute indirect, RIP = 64-Bit offset from register or memory. |
| EA cd       | JMP ptr16:16 | S     | Inv.        | Valid           | Jump far, absolute, address given in operand.                |
| EA cp       | JMP ptr16:32 | S     | Inv.        | Valid           | Jump far, absolute, address given in operand.                |
| FF /5       | JMP m16:16   | M     | Valid       | Valid           | Jump far, absolute indirect, address given in m16:16.        |
| FF /5       | JMP m16:32   | M     | Valid       | Valid           | Jump far, absolute indirect, address given in m16:32.        |
| REX.W FF /5 | JMP m16:64   | M     | Valid       | N.E.            | Jump far, absolute indirect, address given in m16:64.        |

## 操作码字段说明

关于指令操作表中的 Opcode Column 定义在 `******* Opcode Column in the Instruction Summary Table (Instructions without VEX Prefix)` 章节中。

> 参考：https://xem.github.io/minix86/manual/intel-x86-and-64-manual-vol2/o_b5573232dd8f1481-130.html

### 操作码符号含义

- **cb**: Code byte，表示一个 8 位（1 字节）的值，通常是相对偏移量或立即数
- **cw**: Code word，表示一个 16 位（2 字节）的值，通常是相对偏移量或立即数
- **cd**: Code double-word，表示一个 32 位（4 字节）的值，通常是相对偏移量或立即数
- **cp**: Code pointer，表示一个段:偏移（16 位段 + 16 位偏移）指针，用于特定的远跳转或调用指令
- **/4**: 斜杠后的数字表示操作码的扩展字段 (ModR/M 字节中的 reg/mem 字段)，用于指定指令变体。例如，`/4` 可能指定了某个指令使用特定的寄存器组合
- **cd/q**: 表示指令可能使用 32 位（双字）或 64 位（四字）操作数，这取决于操作数大小

## 跳转类型分析

### 短跳转 (Short Jump)
- **EB cb**: 8 位相对偏移，范围 -128 到 +127 字节
- 最常用的跳转形式，适用于近距离跳转

### 近跳转 (Near Jump)
- **E9 cd**: 32 位相对偏移，在 64 位模式下符号扩展
- 适用于同一代码段内的远距离跳转

### 间接跳转 (Indirect Jump)
- **FF /4**: 通过寄存器或内存地址进行跳转
- 目标地址在运行时确定

### 远跳转 (Far Jump)
- **EA** 系列和 **FF /5** 系列
- 跨段跳转，改变代码段选择器

## 实际应用

JMP 指令在以下场景中广泛使用：

1. **控制流程**: 实现循环、条件分支的无条件部分
2. **函数调用**: 尾调用优化
3. **异常处理**: 跳转到异常处理程序
4. **代码混淆**: 在恶意软件分析中常见

## 相关主题

- [[ELF_文件格式]] - 可执行文件格式
- [[编译工具链]] - 汇编器和链接器
- [[内核内存初始化]] - 系统级汇编应用
