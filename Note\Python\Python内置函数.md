# Python Built-in Functions

---



> https://docs.python.org/3/library/functions.html



### all

**功能**

判断一个 `可迭代对象` 中的元素**是否全部为`True`**

```python
>>> all([1,2,3])
True
>>> all([None, 1, 2])
False
```



### any

**功能**

判断一个 `可迭代对象` 中的元素**是否存在至少一个`True`**

```python
>>> any([0,False,1])
True
>>> all([None, False, 0])
False
```



### divmod

**功能**

同时计算两个数的`商`和`余数`, 等价于 `(a//b, a%b)`

```python
>>> divmod(14, 10)
(1, 4)
```



### filter

`filter(callable, iterable)`

**功能**

生成一个迭代器, 仅保留计算结果为`True`的项目

```python
>>> list(filter(None, range(5)))
[1, 2, 3, 4]
>>> list(filter(lambda x:x>2, range(5)))
[3, 4]
```



### format

**功能**

返回由 `formatted representation` 字符串控制的格式化表示, 参考: 

> https://docs.python.org/3/library/string.html#formatspec
>
> https://www.programiz.com/python-programming/methods/built-in/format
>
> https://web.archive.org/web/20231113082556/https://www.programiz.com/python-programming/methods/built-in/format

```python
>>> format(63, "b")
'111111'
>>> format(23, "b")
'10111'
>>> format(1234, "*>+10,d")
'****+1,234'
>>> format(123.4567, "^-09.3f")
'0123.4570'
```



### memoryview

**功能**

允许 Python 代码直接访问一个**支持`Buffer Protocol`的对象**的内部数据, 而不通过内存拷贝

```python
>>> v = memoryview(b'abcefg')
>>> v[1]
98
>>> v[-1]
103
>>> v[1:4]
<memory at 0x7f3ddc9f4350>
>>> bytes(v[1:4])
b'bce'
```

除了 bytes 类型以外, 还可以访问其它的类型:

```python
>>> import array
>>> a = array.array('l', [-11111111, 22222222, -33333333, 44444444])
>>> m = memoryview(a)
>>> m[0]
-11111111
>>> m[-1]
44444444
>>> m[::2].tolist()
[-11111111, -33333333]
```

甚至可以直接改变一个可写对象的内容, 而不改变其 `id`:

```python
>>> data = bytearray(b'abcefg')
>>> id(data)
1769497848560
>>> v = memoryview(data)
>>> v.readonly
False
>>> v[0] = ord(b'z')
>>> data
bytearray(b'zbcefg')
>>> id(data)
1769497848560
>>> v[1:4] = b'123'
>>> data
bytearray(b'z123fg')
```





### slice

**功能**

返回一个切片对象, 用于序列的切片

```python
>>> "1234567890"[slice(0,-1,2)]
'13579'
>>> "1234567890"[0:-1:2]
'13579'
```





### zip

**功能**

将多个 `可迭代对象` 的数据分组合并, 以其中最短的项目为止

```python
>>> list(zip(range(6), "abcdef", ["A", "B", "C", "D", "E"]))
[(0, 'a', 'A'), (1, 'b', 'B'), (2, 'c', 'C'), (3, 'd', 'D'), (4, 'e', 'E')]
```

**技巧**

可以讲一个 `可迭代对象` 中的元素分组

`zip` 函数确保`可迭代对象`**从左到右**的调用顺序, 可以通过类似 `zip(*[iter]*n)` 的调用方式, 将输入划分为 `n` 块

```python
>>> list(zip(*[map(lambda x:int(x), "123456789")]*3))
[(1, 2, 3), (4, 5, 6), (7, 8, 9)]
>>> list(zip(*zip(*[map(lambda x:int(x), "123456789")]*3)))
[(1, 4, 7), (2, 5, 8), (3, 6, 9)]
```

**逆运算**

可以将 `zip` 与 `*` 运算符结合, 用于 `unzip` 运算

```python
>>> x = [1, 2, 3]
y = [4, 5, 6]
>>> list(zip(x, y))
[(1, 4), (2, 5), (3, 6)]
>>> x2, y2 = zip(*zip(x, y))
>>> x == list(x2) and y == list(y2)
True
```





































































