# Git LFS

---



```shell
# 1. 初始化 Hugging Face 的密钥
mkdir -p ~/.ssh
echo "$HUGGINGFACE_RSA_PRIVATE_KEY" > ~/.ssh/id_rsa
echo "$HUGGINGFACE_RSA_PUBLIC_KEY" > ~/.ssh/id_rsa.pub
cat << EOF > ~/.ssh/id_rsa
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOF
cat << EOF > ~/.ssh/id_rsa.pub
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDTDbN1vTwiad2nkVO+yYN/dFNWWiaFJwamsncQ2XBZ5YIY8x70LhAFYrydtzSMuYZ5IsoM+rR6X3UnxhV2LU7XXA9uqrXzXfwDljCiZwmyYybnwsq1WoH2jsiA6aG7F1dRyTc0z5CuvQuHOzYA+QI+3UA+YB5pM2vRddLPzQyzsQlnJdMxMW6pNpl1YsVQgGBomcrhoOJrJ/o1xagaoRfYtZoHvSzl2EsLJmngMKmEABPfyjeqF1foVzgkh2H++bTP2EZkM+GGfpp2ayMlntIgOz91srO5EuL7IExS9nrmNDvB67hj7ARACIlm7fSdPW+WzoeoDNm7k/HBdfrF+M/kGwqTBsvtlHibYjnC/JWeOvZhlxRSa8YnMWssaGdXy9Zvv3/WCsa+iofwhsHmighjRblPcPyp7Yaic19io7Q+qrdNe7+knv2rJuaZbJa2SJn3g4fvr+c/rofuWFgCKhcWjlrfriJ29pqI6zCn6lwc4Dvd3QwTtmtTm4TLzRGVAzw7jpHUEnbIBvnlyqNuP57tS/JpVXi6ftAI+Ku5LFl3KBFAYDLF6UaadwPlHm/q/3KuoZctsCwBf9lFsgkFns/di+ITtx+aRl5L1y3BAf51oOkNxFkpa75IeQYvtsptroyiKFbc5wAecMSW/PMhaxTNlCg0iRAWpS8KXxjzSz8Z2w== <EMAIL>
EOF
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub

```



```shell
apt-get update
apt-get install git git-lfs -y

git config --global user.email "<EMAIL>"
git config --global user.name "tylzh97_codespace"

ssh-keyscan -H hf.co >> ~/.ssh/known_hosts 2>/dev/null
ssh -T *********

# 2. 拉取 Hugging Face 的 Dataset 仓库
GIT_LFS_SKIP_SMUDGE=1 <NAME_EMAIL>:datasets/megatrump/test.git hf-repo

cd hf-repo
cp {file} ./
git lfs track {file}
git add {file}
git commit -m "Add large file with Git LFS"
git push -u origin
```







---





```bash
apt-get install git-lfs -y
git config --global user.email "<EMAIL>"
git config --global user.name "tylzh97_codespace"

# 创建一个新的 Git 仓库
git init my-repo
cd my-repo

# 初始化 Git LFS
git lfs install

# 跟踪 .zip 文件类型
git lfs track "*.zip"

# 创建一个示例文件
echo "This is a large file" > largefile.zip

# 添加文件并提交
git add .gitattributes
git add largefile.zip
git commit -m "Add large zip file with Git LFS"

# 推送到远程仓库
git remote add origin https://github.com/your-username/your-repo.git
git push -u origin main
```





推荐使用 hugging face database 存储. 