# 开发工具详细指南

> 详细的开发工具使用指南和配置说明

## 📋 目录

- [[#多媒体工具]]
- [[#下载工具]]
- [[#逆向工程工具]]
- [[#数据可视化]]
- [[#API 开发]]

---

## 多媒体工具

### FFmpeg 视频处理

> 参考：[[FFmpeg 使用]]

#### 基本视频转码

**CUDA 硬件加速转码**：
```bash
# 使用 NVIDIA GPU 进行 HEVC 编码
ffmpeg -i input.mp4 -c:v hevc_nvenc -rc vbr -b:v 2M -maxrate:v 5M -profile:v main -c:a copy output.mp4

# 参数说明：
# -c:v hevc_nvenc: 使用 NVIDIA HEVC 编码器
# -rc vbr: 可变比特率模式
# -b:v 2M: 目标比特率 2Mbps
# -maxrate:v 5M: 最大比特率 5Mbps
# -profile:v main: 使用 main profile
# -c:a copy: 音频直接复制，不重新编码
```

**常用转码命令**：
```bash
# 基本格式转换
ffmpeg -i input.avi output.mp4

# 调整分辨率
ffmpeg -i input.mp4 -vf scale=1280:720 output.mp4

# 提取音频
ffmpeg -i input.mp4 -vn -acodec copy output.aac

# 提取视频（无音频）
ffmpeg -i input.mp4 -an -vcodec copy output.mp4

# 合并音视频
ffmpeg -i video.mp4 -i audio.aac -c copy output.mp4

# 截取视频片段
ffmpeg -i input.mp4 -ss 00:01:00 -t 00:02:00 -c copy output.mp4

# 生成 GIF
ffmpeg -i input.mp4 -vf "fps=10,scale=320:-1:flags=lanczos" output.gif
```

**批量处理脚本**：
```bash
#!/bin/bash
# 批量转码脚本

INPUT_DIR="./input"
OUTPUT_DIR="./output"
QUALITY="2M"

mkdir -p "$OUTPUT_DIR"

for file in "$INPUT_DIR"/*.{mp4,avi,mkv,mov}; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        name="${filename%.*}"
        echo "Processing: $filename"
        
        ffmpeg -i "$file" \
               -c:v hevc_nvenc \
               -rc vbr \
               -b:v "$QUALITY" \
               -c:a aac \
               -b:a 128k \
               "$OUTPUT_DIR/${name}_compressed.mp4"
    fi
done

echo "Batch processing completed!"
```

---

## 下载工具

### Aria2 高速下载

> 参考：[[Aria2 配置]]

#### Docker 部署

**基本部署**：
```bash
docker run -d \
    --name aria2-pro \
    --restart unless-stopped \
    --log-opt max-size=1m \
    -e PUID=$UID \
    -e PGID=$GID \
    -e UMASK_SET=022 \
    -e RPC_SECRET=qweqwe123 \
    -e RPC_PORT=6800 \
    -p 6800:6800 \
    -e LISTEN_PORT=6888 \
    -p 6888:6888 \
    -p 6888:6888/udp \
    -v $PWD/aria2-config:/config \
    -v $PWD/aria2-downloads:/downloads \
    p3terx/aria2-pro
```

**配置文件优化**：
```ini
# aria2.conf
# 基本设置
dir=/downloads
file-allocation=falloc
continue=true
max-concurrent-downloads=5
max-connection-per-server=16
min-split-size=1M
split=16

# HTTP/HTTPS 设置
user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
referer=*
enable-http-keep-alive=true
enable-http-pipelining=true

# BT 设置
enable-dht=true
enable-peer-exchange=true
bt-enable-lpd=true
bt-max-peers=50
bt-request-peer-speed-limit=50K
bt-max-open-files=100
bt-tracker-connect-timeout=10
bt-tracker-timeout=10

# RPC 设置
enable-rpc=true
rpc-listen-all=true
rpc-listen-port=6800
rpc-secret=your_secret_here
rpc-allow-origin-all=true
```

#### 命令行使用

```bash
# 下载单个文件
aria2c "https://example.com/file.zip"

# 多线程下载
aria2c -x 16 -s 16 "https://example.com/largefile.zip"

# 下载 BT 种子
aria2c "magnet:?xt=urn:btih:..."

# 从文件列表下载
aria2c -i download_list.txt

# 限速下载
aria2c --max-download-limit=1M "https://example.com/file.zip"
```

**Web UI 访问**：
- AriaNg: https://ariang.mayswind.net/latest/
- 本地访问: http://localhost:6800

---

## 逆向工程工具

### IDA Pro 资源

> 参考：[[IDA Pro Downloads]]、[[超全的逆向破解资源下载]]

#### 下载资源

**官方和镜像站点**：
- 吾爱破解工具站: https://down.52pojie.cn/Tools/Disassemblers/
- 看雪论坛资源: https://bbs.kanxue.com/thread-277984.htm

#### IDA Python 脚本

> 参考：[[IDA-Python 使用]]

**基础脚本模板**：
```python
import ida_auto
import ida_bytes
import ida_funcs
import ida_name
import ida_search
import idautils
import idc

def analyze_functions():
    """分析所有函数"""
    print("Function Analysis Report")
    print("=" * 50)
    
    for func_ea in idautils.Functions():
        func_name = ida_funcs.get_func_name(func_ea)
        func_size = ida_funcs.get_func_size(func_ea)
        
        print(f"Function: {func_name}")
        print(f"Address: 0x{func_ea:08X}")
        print(f"Size: {func_size} bytes")
        print("-" * 30)

def find_strings():
    """查找所有字符串"""
    print("String Analysis")
    print("=" * 50)
    
    strings = idautils.Strings()
    for string in strings:
        print(f"0x{string.ea:08X}: {str(string)}")

def patch_bytes(address, new_bytes):
    """修改字节"""
    for i, byte in enumerate(new_bytes):
        ida_bytes.patch_byte(address + i, byte)
    print(f"Patched {len(new_bytes)} bytes at 0x{address:08X}")

# 主函数
def main():
    print("IDA Python Analysis Script")
    print("Waiting for auto-analysis to complete...")
    ida_auto.auto_wait()
    
    analyze_functions()
    find_strings()

if __name__ == "__main__":
    main()
```

#### 修改基地址

> 参考：[[IDA 修改基地址]]

**重新设置基地址**：
```python
import ida_segment

def rebase_program(new_base):
    """重新设置程序基地址"""
    # 获取当前基地址
    current_base = ida_nalt.get_imagebase()
    
    # 计算偏移量
    delta = new_base - current_base
    
    # 重新设置基地址
    ida_segment.rebase_program(delta, ida_segment.MSF_FIXONCE)
    
    print(f"Rebased from 0x{current_base:08X} to 0x{new_base:08X}")

# 使用示例
rebase_program(0x400000)
```

---

## 数据可视化

### Matplotlib 详细使用

> 参考：[[Matplotlib 使用说明]]

#### 基础配置

```python
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

# 设置图片样式
plt.style.use('ggplot')

# 设置图像属性
plt.figure(num=1, figsize=(16, 9), dpi=100, facecolor="w")
```

#### 多子图布局

```python
import numpy as np
import matplotlib.pyplot as plt

# 创建多子图
fig, ax = plt.subplots(nrows=2, ncols=3, figsize=(15, 10))

# 折线图
x = np.arange(1, 100)
ax[0][0].plot(x, x*x)
ax[0][0].set_title("折线图")

# 散点图
ax[0][1].scatter(np.arange(0, 10), np.random.rand(10))
ax[0][1].set_title("散点图")

# 柱状图
categories = ['A', 'B', 'C', 'D']
values = [25, 15, 35, 30]
ax[0][2].bar(categories, values, color='skyblue')
ax[0][2].set_title("柱状图")

# 饼图
ax[1][0].pie(x=[15, 30, 45, 10], labels=list('ABCD'), 
             autopct='%.1f%%', explode=[0, 0.05, 0, 0])
ax[1][0].set_title("饼图")

# 直方图
data = np.random.normal(100, 15, 1000)
ax[1][1].hist(data, bins=30, alpha=0.7, color='green')
ax[1][1].set_title("直方图")

# 热力图
data_2d = np.random.rand(10, 10)
im = ax[1][2].imshow(data_2d, cmap='viridis')
ax[1][2].set_title("热力图")
plt.colorbar(im, ax=ax[1][2])

# 调整布局
plt.tight_layout()
plt.show()
```

#### 高级图表

**分组柱状图**：
```python
import numpy as np
import matplotlib.pyplot as plt

# 数据
x = np.array([2020, 2021, 2022, 2023, 2024])
y_beijing = np.array([156, 189, 273, 534, 299])
y_shanghai = np.array([183, 266, 142, 503, 366])

width = 0.35
x_pos = np.arange(len(x))

# 创建分组柱状图
fig, ax = plt.subplots(figsize=(10, 6))

bars1 = ax.bar(x_pos - width/2, y_beijing, width, label='北京', alpha=0.8)
bars2 = ax.bar(x_pos + width/2, y_shanghai, width, label='上海', alpha=0.8)

# 添加数值标签
for bar in bars1:
    height = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2., height + 5,
            f'{height}', ha='center', va='bottom')

for bar in bars2:
    height = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2., height + 5,
            f'{height}', ha='center', va='bottom')

# 设置标签和标题
ax.set_xlabel('年份')
ax.set_ylabel('GDP (万亿元)')
ax.set_title('北京与上海GDP对比')
ax.set_xticks(x_pos)
ax.set_xticklabels(x)
ax.legend()

plt.tight_layout()
plt.show()
```

---

## API 开发

### GitHub API 使用

> 参考：[[Python Github API]]

#### 基础配置

```python
import urllib
import urllib3
import json
import os

class GitHubAPI:
    def __init__(self, token=None):
        self.token = token or os.getenv('GITHUB_TOKEN')
        self.base_url = "https://api.github.com"
        
        # 获取系统代理配置
        proxies = urllib.request.getproxies()
        
        # 创建连接池
        if proxies.get('http'):
            self.http = urllib3.ProxyManager(proxies["http"])
        else:
            self.http = urllib3.PoolManager()
        
        # 设置请求头
        self.headers = {
            "Accept": "application/vnd.github+json",
            "Authorization": f"Bearer {self.token}",
            "User-Agent": "Python-GitHub-Client/1.0"
        }
        
        self.http.headers = self.headers
    
    def get_user_info(self, username=None):
        """获取用户信息"""
        if username:
            url = f"{self.base_url}/users/{username}"
        else:
            url = f"{self.base_url}/user"
        
        resp = self.http.request("GET", url)
        return json.loads(resp.data.decode('utf-8'))
    
    def list_repositories(self, username=None, per_page=30):
        """列出仓库"""
        if username:
            url = f"{self.base_url}/users/{username}/repos"
        else:
            url = f"{self.base_url}/user/repos"
        
        url += f"?per_page={per_page}&sort=updated"
        
        resp = self.http.request("GET", url)
        return json.loads(resp.data.decode('utf-8'))
    
    def create_repository(self, name, description="", private=False):
        """创建仓库"""
        url = f"{self.base_url}/user/repos"
        
        data = {
            "name": name,
            "description": description,
            "private": private,
            "auto_init": True
        }
        
        resp = self.http.request("POST", url, 
                                body=json.dumps(data),
                                headers={'Content-Type': 'application/json'})
        return json.loads(resp.data.decode('utf-8'))
    
    def get_repository_contents(self, owner, repo, path=""):
        """获取仓库内容"""
        url = f"{self.base_url}/repos/{owner}/{repo}/contents/{path}"
        
        resp = self.http.request("GET", url)
        return json.loads(resp.data.decode('utf-8'))

# 使用示例
if __name__ == "__main__":
    # 初始化 API 客户端
    github = GitHubAPI()
    
    # 获取当前用户信息
    user_info = github.get_user_info()
    print(f"用户: {user_info['login']}")
    print(f"公开仓库数: {user_info['public_repos']}")
    
    # 列出仓库
    repos = github.list_repositories()
    print("\n最近更新的仓库:")
    for repo in repos[:5]:
        print(f"- {repo['name']}: {repo['description']}")
```

---

## 🔗 相关链接

- [[工具索引]]
- [[编程语言与框架]]
- [[AI 辅助开发]]
- [[调试工具集合]]

---

*最后更新：2025-06-16*
