# 调试工具集合

> 系统调试、程序分析与性能优化工具大全

## 📋 目录

- [[#系统级调试]]
- [[#程序分析工具]]
- [[#性能分析]]
- [[#内存分析]]
- [[#网络调试]]

---

## 系统级调试

### GDB 调试器

> 参考：[[GDB 命令速查]]

#### 基本调试命令

```bash
# 启动调试
gdb ./program
gdb --args ./program arg1 arg2

# 附加到运行中的进程
gdb -p PID
gdb -ex "set confirm off" attach $(pidof program_name)

# 自动附加到特定进程
gdb -ex "set confirm off" attach $(ps aux | grep 'target_process' | awk 'NR==1{print $2}')
```

#### 断点和执行控制

```bash
# 设置断点
break main
break *0x400000
break filename.c:123
break function_name

# 条件断点
break main if argc > 1
break *0x400000 if $rax == 0

# 查看和管理断点
info breakpoints
delete 1
disable 1
enable 1

# 执行控制
run
continue
step      # 单步执行（进入函数）
next      # 单步执行（跳过函数）
finish    # 执行到函数返回
```

#### 内存和寄存器查看

```bash
# 查看寄存器
info registers
info registers rax rbx rcx

# 查看内存
x/10i $pc          # 查看当前指令
x/16xb 0x400000    # 查看内存内容（16字节，十六进制）
x/4xw 0x400000     # 查看内存内容（4个字，十六进制）
x/s 0x400000       # 查看字符串

# 查看栈
bt                 # 查看调用栈
frame 0            # 切换到指定栈帧
info frame         # 查看当前栈帧信息

# 查看变量
print variable_name
print *pointer
print array[0]@10  # 打印数组前10个元素
```

#### 高级调试技巧

```bash
# 设置环境
set environment VAR=value
unset environment VAR

# 修改内存和寄存器
set $rax = 0x1234
set {int}0x400000 = 0x90909090

# 调用函数
call function_name(arg1, arg2)
print function_name(arg1, arg2)

# 脚本化调试
source debug_script.gdb

# 远程调试
target remote localhost:1234
```

### 系统调用追踪

#### Strace 工具

> 参考：[[Strace 使用]]

```bash
# 基本追踪
strace ./program
strace -p PID

# 追踪特定系统调用
strace -e trace=open,read,write ./program
strace -e trace=file ./program          # 文件相关
strace -e trace=process ./program       # 进程相关
strace -e trace=network ./program       # 网络相关

# 输出控制
strace -o trace.log ./program           # 输出到文件
strace -f ./program                     # 追踪子进程
strace -c ./program                     # 统计系统调用

# 高级选项
strace -T ./program                     # 显示系统调用耗时
strace -r ./program                     # 显示相对时间戳
strace -tt ./program                    # 显示绝对时间戳
strace -v ./program                     # 详细输出
```

#### Python Strace 集成

> 参考：[[Python-Strace]]

```python
import subprocess
import re
import json
from collections import defaultdict

class StraceAnalyzer:
    def __init__(self):
        self.syscall_stats = defaultdict(int)
        self.file_operations = []
        self.network_operations = []
    
    def trace_program(self, program_args, output_file=None):
        """追踪程序执行"""
        cmd = ['strace', '-f', '-T', '-tt'] + program_args
        if output_file:
            cmd.extend(['-o', output_file])
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if output_file:
                with open(output_file, 'r') as f:
                    return f.read()
            else:
                return result.stderr
        except Exception as e:
            print(f"Error running strace: {e}")
            return None
    
    def parse_trace(self, trace_output):
        """解析 strace 输出"""
        lines = trace_output.split('\n')
        
        for line in lines:
            if not line.strip():
                continue
            
            # 解析系统调用
            syscall_match = re.match(r'.*?(\w+)\(.*?\)\s*=\s*(-?\d+)', line)
            if syscall_match:
                syscall_name = syscall_match.group(1)
                return_value = int(syscall_match.group(2))
                self.syscall_stats[syscall_name] += 1
                
                # 分析文件操作
                if syscall_name in ['open', 'openat', 'read', 'write']:
                    self.analyze_file_operation(line, syscall_name)
                
                # 分析网络操作
                elif syscall_name in ['socket', 'connect', 'bind', 'listen']:
                    self.analyze_network_operation(line, syscall_name)
    
    def analyze_file_operation(self, line, syscall):
        """分析文件操作"""
        if syscall in ['open', 'openat']:
            # 提取文件名
            filename_match = re.search(r'"([^"]+)"', line)
            if filename_match:
                filename = filename_match.group(1)
                self.file_operations.append({
                    'operation': syscall,
                    'filename': filename,
                    'line': line
                })
    
    def analyze_network_operation(self, line, syscall):
        """分析网络操作"""
        self.network_operations.append({
            'operation': syscall,
            'line': line
        })
    
    def generate_report(self):
        """生成分析报告"""
        report = {
            'syscall_statistics': dict(self.syscall_stats),
            'file_operations': self.file_operations,
            'network_operations': self.network_operations,
            'summary': {
                'total_syscalls': sum(self.syscall_stats.values()),
                'unique_syscalls': len(self.syscall_stats),
                'file_ops_count': len(self.file_operations),
                'network_ops_count': len(self.network_operations)
            }
        }
        return report

# 使用示例
analyzer = StraceAnalyzer()
trace_output = analyzer.trace_program(['./my_program', 'arg1'])
analyzer.parse_trace(trace_output)
report = analyzer.generate_report()
print(json.dumps(report, indent=2))
```

---

## 程序分析工具

### 静态分析

#### ASM 分析

> 参考：[[ASM 调试]]

```bash
# 反汇编工具
objdump -d ./binary > assembly.s
objdump -S ./binary > assembly_with_source.s
objdump -M intel -d ./binary  # Intel 语法

# 查看特定段
objdump -s -j .text ./binary
objdump -s -j .data ./binary
objdump -s -j .rodata ./binary

# 查看符号表
objdump -t ./binary
nm ./binary
readelf -s ./binary

# 查看动态链接
ldd ./binary
objdump -R ./binary  # 重定位表
```

#### 代码分析工具

**Capstone 反汇编引擎**：
> 参考：[[Capstone 使用]]

```python
from capstone import *

class DisassemblyAnalyzer:
    def __init__(self, arch=CS_ARCH_X86, mode=CS_MODE_64):
        self.md = Cs(arch, mode)
        self.md.detail = True
    
    def disassemble_bytes(self, code_bytes, base_address=0x1000):
        """反汇编字节码"""
        instructions = []
        for insn in self.md.disasm(code_bytes, base_address):
            instruction_info = {
                'address': hex(insn.address),
                'mnemonic': insn.mnemonic,
                'op_str': insn.op_str,
                'bytes': insn.bytes.hex(),
                'size': insn.size
            }
            
            # 分析操作数
            if insn.operands:
                instruction_info['operands'] = []
                for op in insn.operands:
                    op_info = self.analyze_operand(op)
                    instruction_info['operands'].append(op_info)
            
            instructions.append(instruction_info)
        
        return instructions
    
    def analyze_operand(self, operand):
        """分析操作数"""
        if operand.type == CS_OP_REG:
            return {'type': 'register', 'value': operand.reg}
        elif operand.type == CS_OP_IMM:
            return {'type': 'immediate', 'value': hex(operand.imm)}
        elif operand.type == CS_OP_MEM:
            return {
                'type': 'memory',
                'base': operand.mem.base,
                'index': operand.mem.index,
                'disp': operand.mem.disp
            }
        return {'type': 'unknown'}
    
    def find_function_calls(self, code_bytes, base_address=0x1000):
        """查找函数调用"""
        calls = []
        for insn in self.md.disasm(code_bytes, base_address):
            if insn.mnemonic == 'call':
                calls.append({
                    'address': hex(insn.address),
                    'target': insn.op_str,
                    'instruction': f"{insn.mnemonic} {insn.op_str}"
                })
        return calls

# 使用示例
analyzer = DisassemblyAnalyzer()
code = b"\x55\x48\x89\xe5\x48\x83\xec\x10"  # 示例机器码
instructions = analyzer.disassemble_bytes(code)
for insn in instructions:
    print(f"{insn['address']}: {insn['mnemonic']} {insn['op_str']}")
```

### 动态分析

#### Intel Pin 插桩

> 参考：[[Intel Pin 使用]]

```cpp
#include "pin.H"
#include <iostream>
#include <fstream>

// 全局变量
std::ofstream TraceFile;
UINT64 InstructionCount = 0;

// 指令计数回调
VOID CountInstruction() {
    InstructionCount++;
}

// 内存访问回调
VOID RecordMemRead(VOID* ip, VOID* addr, UINT32 size) {
    TraceFile << "R " << ip << " " << addr << " " << size << std::endl;
}

VOID RecordMemWrite(VOID* ip, VOID* addr, UINT32 size) {
    TraceFile << "W " << ip << " " << addr << " " << size << std::endl;
}

// 指令插桩回调
VOID Instruction(INS ins, VOID* v) {
    // 插入指令计数
    INS_InsertCall(ins, IPOINT_BEFORE, (AFUNPTR)CountInstruction, IARG_END);
    
    // 插入内存访问追踪
    if (INS_IsMemoryRead(ins)) {
        INS_InsertCall(ins, IPOINT_BEFORE, (AFUNPTR)RecordMemRead,
                      IARG_INST_PTR, IARG_MEMORYREAD_EA, IARG_MEMORYREAD_SIZE,
                      IARG_END);
    }
    
    if (INS_IsMemoryWrite(ins)) {
        INS_InsertCall(ins, IPOINT_BEFORE, (AFUNPTR)RecordMemWrite,
                      IARG_INST_PTR, IARG_MEMORYWRITE_EA, IARG_MEMORYWRITE_SIZE,
                      IARG_END);
    }
}

// 程序结束回调
VOID Fini(INT32 code, VOID* v) {
    std::cout << "Total instructions: " << InstructionCount << std::endl;
    TraceFile.close();
}

int main(int argc, char* argv[]) {
    PIN_Init(argc, argv);
    
    TraceFile.open("memory_trace.out");
    
    INS_AddInstrumentFunction(Instruction, 0);
    PIN_AddFiniFunction(Fini, 0);
    
    PIN_StartProgram();
    return 0;
}
```

---

## 性能分析

### Sanitizer 工具

> 参考：[[Sanitizer 工具]]

#### AddressSanitizer

```bash
# 编译时启用
gcc -fsanitize=address -g -o program source.c
clang -fsanitize=address -g -o program source.c

# 运行时选项
export ASAN_OPTIONS=detect_leaks=1:abort_on_error=1:symbolize=1
./program
```

#### ThreadSanitizer

> 参考：[[ThreadSanitizer]]

```bash
# 编译启用
gcc -fsanitize=thread -g -o program source.c

# 检测数据竞争
export TSAN_OPTIONS=detect_thread_leaks=1:report_bugs=1
./program
```

#### 竞态条件检测

> 参考：[[Races 检测]]

```c
#include <pthread.h>
#include <stdio.h>

int shared_variable = 0;
pthread_mutex_t mutex = PTHREAD_MUTEX_INITIALIZER;

void* thread_function(void* arg) {
    for (int i = 0; i < 1000000; i++) {
        // 不安全的访问（会被 ThreadSanitizer 检测到）
        shared_variable++;
        
        // 安全的访问
        // pthread_mutex_lock(&mutex);
        // shared_variable++;
        // pthread_mutex_unlock(&mutex);
    }
    return NULL;
}

int main() {
    pthread_t thread1, thread2;
    
    pthread_create(&thread1, NULL, thread_function, NULL);
    pthread_create(&thread2, NULL, thread_function, NULL);
    
    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);
    
    printf("Final value: %d\n", shared_variable);
    return 0;
}
```

### 性能分析工具

```bash
# perf 工具
perf record ./program
perf report
perf stat ./program

# valgrind 内存分析
valgrind --tool=memcheck ./program
valgrind --tool=callgrind ./program
valgrind --tool=massif ./program

# 时间分析
time ./program
/usr/bin/time -v ./program  # 详细信息
```

---

## 内存分析

### 内存映射分析

```bash
# 查看进程内存映射
pmap -X $(pidof program)
cat /proc/$(pidof program)/maps
cat /proc/$(pidof program)/smaps

# 内存使用统计
cat /proc/$(pidof program)/status | grep -E "Vm|Rss"
```

### Python 内存分析

```python
import psutil
import os

class MemoryAnalyzer:
    def __init__(self, pid=None):
        self.pid = pid or os.getpid()
        self.process = psutil.Process(self.pid)
    
    def get_memory_info(self):
        """获取内存信息"""
        memory_info = self.process.memory_info()
        memory_percent = self.process.memory_percent()
        
        return {
            'rss': memory_info.rss,  # 物理内存
            'vms': memory_info.vms,  # 虚拟内存
            'percent': memory_percent,
            'available': psutil.virtual_memory().available,
            'total': psutil.virtual_memory().total
        }
    
    def get_memory_maps(self):
        """获取内存映射"""
        try:
            maps = self.process.memory_maps()
            return [{'path': m.path, 'rss': m.rss, 'size': m.size} for m in maps]
        except psutil.AccessDenied:
            return None
    
    def monitor_memory(self, interval=1, duration=60):
        """监控内存使用"""
        import time
        
        start_time = time.time()
        memory_history = []
        
        while time.time() - start_time < duration:
            memory_info = self.get_memory_info()
            memory_info['timestamp'] = time.time()
            memory_history.append(memory_info)
            time.sleep(interval)
        
        return memory_history

# 使用示例
analyzer = MemoryAnalyzer()
memory_info = analyzer.get_memory_info()
print(f"RSS: {memory_info['rss'] / 1024 / 1024:.2f} MB")
print(f"VMS: {memory_info['vms'] / 1024 / 1024:.2f} MB")
print(f"Memory %: {memory_info['percent']:.2f}%")
```

---

## 网络调试

### 网络连接分析

```bash
# 查看网络连接
ss -tulpn
netstat -tulpn
lsof -i

# 查看特定端口
ss -tulpn | grep :80
lsof -i :80

# 网络统计
ss -s
netstat -s
```

### 抓包分析

```bash
# tcpdump 抓包
sudo tcpdump -i eth0 -w capture.pcap
sudo tcpdump -i eth0 host ***********
sudo tcpdump -i eth0 port 80
sudo tcpdump -i eth0 -A  # ASCII 输出

# 分析抓包文件
tcpdump -r capture.pcap
tcpdump -r capture.pcap -A
tcpdump -r capture.pcap host ***********
```

---

## 🔗 相关链接

- [[二进制安全与逆向工程]]
- [[系统虚拟化与仿真]]
- [[编程语言与框架]]
- [[系统管理与运维]]

---

*最后更新：2025-06-16*
