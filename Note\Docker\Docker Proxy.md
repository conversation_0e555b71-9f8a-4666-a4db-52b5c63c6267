# Docker Proxy

---



> https://blog.csdn.net/Wrm244/article/details/128567900
>
> https://hub.docker.com/r/wrm244/sharelatex



```shell
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<-'EOF'
{
    "registry-mirrors": [
        "https://docker.1panel.live",
        "https://hub.rat.dev"
    ]
}
EOF
sudo systemctl daemon-reload
sudo systemctl restart docker
```



镜像地址:

```
docker.1panel.live -- 1Panel 面板提供（推荐）
hub.rat.dev -- 耗子面板提供
docker.m.daocloud.io -- DaoCloud 提供（限速+排队）
ccr.ccs.tencentyun.com -- 腾讯云提供（版本较旧）
ustc-edu-cn.mirror.aliyuncs.com -- 阿里云提供（版本较旧）
```



直接使用镜像 pull

```shell
$ docker pull docker pull docker.1panel.live/p3terx/aria2-pro:latest
# 使用 library 前缀下载官方镜像
$ docker pull docker.1panel.live/library/ubuntu:24.04
# 下载后的镜像有源的域名前缀
$ docker images
REPOSITORY                            TAG       IMAGE ID       CREATED         SIZE
docker.1panel.live/library/ubuntu     24.04     35a88802559d   2 weeks ago     78MB
```





> 为指定用户添加 Feature？ https://github.com/overleaf/overleaf/blob/0b6a8dc0936ab7d204681bfac4ce55f059737cd3/services/web/scripts/add_feature_override.js#L13



> 魔改前端函数实现 Premium 的注册功能： https://blog.sparktour.me/posts/2021/04/02/self-host-overleaf/

