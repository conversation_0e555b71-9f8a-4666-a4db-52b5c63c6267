# 系统管理与运维

> Linux 系统管理、网络配置、安全工具与运维自动化

## 📋 目录

- [[#Linux 系统管理]]
- [[#网络与安全]]
- [[#包管理系统]]
- [[#系统监控]]
- [[#自动化运维]]

---

## Linux 系统管理

### 命令行基础

> 参考：[[Linux 命令速查]]

#### 用户管理

**创建和管理用户**：
```bash
# 创建新用户
sudo useradd username
sudo passwd username

# 更友好的用户创建方式
sudo adduser username

# 修改用户信息
sudo usermod -aG groupname username  # 添加到用户组
sudo usermod -s /bin/bash username   # 修改默认 shell

# 查看用户信息
id username
getent passwd username

# 文件权限管理
chown user:group /path/to/file
chmod 755 /path/to/file
```

#### SSH 密钥管理

```bash
# 生成 SSH 密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 设置正确的权限
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub

# 复制公钥到远程服务器
ssh-copy-id user@remote_host

# 配置 SSH 客户端
cat >> ~/.ssh/config << EOF
Host myserver
    HostName remote_host
    User username
    Port 22
    IdentityFile ~/.ssh/id_rsa
EOF
```

#### 进程管理

**进程控制**：
```bash
# 查看进程
ps aux | grep process_name
pgrep -f process_name
pidof process_name

# 杀死进程
kill -9 PID
killall process_name
pkill -9 -f process_name

# 杀死特定用户的所有进程
killall -u username

# 后台任务管理
nohup command &
jobs
fg %1
bg %1
disown %1
```

**进程优先级**：
```bash
# 启动时设置优先级
nice -n -10 ./my_program

# 修改运行中进程的优先级
renice -n -10 PID
renice -n -10 -u username  # 修改用户所有进程
```

#### 文件系统管理

**磁盘操作**：
```bash
# 查看磁盘使用情况
df -h
du -sh /path/to/directory
du -sm /path/to/directory  # 查看文件数量

# 查看磁盘 I/O
iostat -d -x -k 1 10

# 磁盘扩容（虚拟机环境）
sudo fdisk -l
sudo parted /dev/sda
(parted) p
(parted) resizepart <partition_number>
(parted) q
sudo resize2fs /dev/sda1
```

**压缩与解压**：
```bash
# tar 压缩
tar -zcvf archive.tar.gz directory/
tar -zxvf archive.tar.gz

# 相对路径压缩
tar -czf archive.tar.gz -C /target/dir target_name

# 并行压缩（需要 pigz）
tar --use-compress-program="pigz -k -p8" -cvf archive.tgz directory/

# deb 包处理
sudo dpkg -i package.deb
dpkg -x package.deb extract_dir/
dpkg -e package.deb control_dir/

# 批量解压 deb 包
for file in *.deb; do dpkg-deb -x $file ${file%%.deb}; done
```

#### 内存管理

**虚拟内存配置**：
```bash
# 创建 Swap 文件
sudo mkdir /swapfile && cd /swapfile/
sudo dd if=/dev/zero of=swapfile bs=1024 count=30000000  # 30GB
sudo mkswap -f swapfile
sudo swapon swapfile

# 查看交换空间
swapon -s
free -h

# 取消挂载
sudo swapoff swapfile

# RAM 磁盘
mkdir /mnt/ramdisk
mount -t tmpfs -o size=256M tmpfs /mnt/ramdisk
# 取消挂载
sudo umount /mnt/ramdisk
```

**自动挂载配置**：
```bash
# 编辑 /etc/fstab
sudo vi /etc/fstab
# 添加：tmpfs /mnt/ramdisk tmpfs defaults,size=5G 0 0

# 创建目录并设置权限
mkdir -p /mnt/ramdisk
chmod 777 /mnt/ramdisk/
chattr +i /mnt/ramdisk/  # 防止目录被修改

# 挂载并验证
mount -a
df -h
```

#### 网络配置

**网桥和虚拟网卡**：
```bash
#!/bin/bash
# 添加虚拟网卡与配置网桥
sudo modprobe tun tap
sudo ip link add br0 type bridge
sudo ip tuntap add dev tap0 mode tap
sudo ip tuntap add dev tap1 mode tap
sudo ip link set dev tap0 master br0
sudo ip link set dev tap1 master br0
sudo ip link set dev eno2 master br0
sudo ip link set dev br0 up

# 配置 IP 地址
sudo ip address delete *************/24 dev eno2
sudo ip address add *************/24 dev br0
sudo ifconfig br0 ***************/24
sudo ifconfig tap0 up
sudo ifconfig tap1 up
sudo ip link set dev tap0 up
sudo ip link set dev tap1 up
```

### 系统监控

**资源监控**：
```bash
# 内存使用
free -h

# CPU 使用率最高的进程
ps auxw | head -1; ps auxw | sort -rn -k3 | head -10

# 系统资源监控
htop  # 需要安装：sudo apt install htop

# 网络连接
sudo ss -tunap
netstat -tulpn

# 进程内存映射
pmap -X $(pidof process_name)
cat /proc/$(pidof process_name)/maps
```

**日志管理**：
```bash
# 系统日志
journalctl -f  # 实时查看
journalctl -u service_name  # 查看特定服务
journalctl --since "2023-01-01" --until "2023-01-02"

# 传统日志
tail -f /var/log/syslog
tail -f /var/log/auth.log
```

---

## 网络与安全

### 证书管理

#### SSL/TLS 证书

> 参考：[[自签名证书]]、[[OpenSSL 使用]]

**自签名证书生成**：
```bash
# 生成私钥
openssl genrsa -out server.key 2048

# 生成证书签名请求
openssl req -new -key server.key -out server.csr

# 生成自签名证书
openssl x509 -req -days 365 -in server.csr -signkey server.key -out server.crt

# 一步生成自签名证书
openssl req -x509 -newkey rsa:2048 -keyout key.pem -out cert.pem -days 365 -nodes
```

#### ACME 证书自动化

> 参考：[[ACME 证书管理]]

```bash
# 安装 acme.sh
curl https://get.acme.sh | sh

# 申请证书
acme.sh --issue -d example.com -w /var/www/html

# 安装证书
acme.sh --install-cert -d example.com \
  --key-file /etc/nginx/ssl/example.com.key \
  --fullchain-file /etc/nginx/ssl/example.com.crt \
  --reloadcmd "systemctl reload nginx"

# 自动续期
acme.sh --cron
```

### 代理与网络工具

#### 网络代理配置

> 参考：[[ClashForWindows]]、[[Charles 证书]]

**Clash 配置示例**：
```yaml
# config.yaml
port: 7890
socks-port: 7891
allow-lan: true
mode: Rule
log-level: info

proxies:
  - name: "proxy1"
    type: ss
    server: server.example.com
    port: 443
    cipher: aes-256-gcm
    password: password

proxy-groups:
  - name: "PROXY"
    type: select
    proxies:
      - proxy1
      - DIRECT

rules:
  - DOMAIN-SUFFIX,google.com,PROXY
  - DOMAIN-SUFFIX,github.com,PROXY
  - GEOIP,CN,DIRECT
  - MATCH,PROXY
```

#### 网络分析工具

**抓包和分析**：
```bash
# tcpdump 抓包
sudo tcpdump -i eth0 -w capture.pcap
sudo tcpdump -i eth0 host ***********
sudo tcpdump -i eth0 port 80

# 网络连接分析
ss -tulpn | grep :80
lsof -i :80
netstat -tulpn | grep :80
```

---

## 包管理系统

### NixOS 包管理

> 参考：[[NixOS 全局配置]]、[[Nix Flakes]]

#### Nix Flakes 详细配置

**基本语法结构**：
在 Git 项目根目录中创建 `flake.nix` 文件，定义项目的开发环境、运行环境和编译过程：

```nix
{
  description = "一个简单的 flake 示例";
  inputs = {
    # 依赖声明
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };
  outputs = { self, nixpkgs, ... }: {
    # 输出声明
    # ...
  };
}
```

**Inputs 配置**：
```nix
inputs = {
  nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
  home-manager = {
    url = "github:nix-community/home-manager";
    inputs.nixpkgs.follows = "nixpkgs"; # 依赖跟随
  };
  flake-utils.url = "github:numtide/flake-utils";
};
```

**支持的 URL 格式**：
- **GitHub**：`github:owner/repo/ref`
- **GitLab**：`gitlab:owner/repo/ref`
- **本地路径**：`path:/absolute/path` 或 `path:./relative/path`
- **Git**：`git+https://example.com/repo.git`
- **Tarball**：`https://example.com/archive.tar.gz`
- **Flake 注册表**：`flake:name`

#### Flakes 约定输出属性

| 输出属性 | 用途 | 结构 | 主要命令 | 默认属性 |
|----------|------|------|----------|----------|
| `packages` | 可构建和安装的软件包 | `<s>.<n>` | `nix build .#<n>` | 是 |
| `apps` | 可直接运行的应用程序 | `<s>.<n>` | `nix run .#<n>` | 是 |
| `devShells` | 开发环境 | `<s>.<n>` | `nix develop .#<n>` | 是 |
| `lib` | 可重用的 Nix 函数库 | `<n>` | 在代码中引用 | 可选 |
| `overlays` | Nixpkgs 扩展 | `<n>` | 导入时应用 | 是 |
| `templates` | 项目模板 | `<n>` | `nix flake init -t .#<n>` | 是 |

**完整的开发环境模板**：
```nix
{
  description = "myapp -- Nix Flakes Template";

  inputs = {
    nixpkgs.url = "github:nixos/nixpkgs/release-22.11";
    flake-utils.url = "github:numtide/flake-utils";
    self.submodules = true;
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pname = "myapp";
        version = "0.0.1";

        pkgs = import nixpkgs {
          inherit system;
          config = {
            allowUnfree = false;
            permittedInsecurePackages = [
              "python-********"
            ];
          };
        };

        buildTools = with pkgs; [
          gcc
          bash
          gnumake
          autoconf
          pkg-config
        ];

        debugTools = with pkgs; [
          gdb
          git
          bear
          strace
        ];

        pythonEnv = pkgs.python39.withPackages (ps: with ps; [
          pip
          numpy
          pillow
        ]);

        coreDeps = with pkgs; [
          zlib
          glib
          libxml2
        ];
      in
      {
        # nix build
        packages.default = pkgs.stdenv.mkDerivation {
          name = "${pname}-${version}";
          src = ./.;

          # 构建时依赖 (build platform tools)
          nativeBuildInputs = [ pkgs.breakpointHook ] ++ buildTools ++ [ pythonEnv ];
          # 运行时依赖 (host tools)
          buildInputs = coreDeps;

          patchPhase = ''
            patchShebangs .
          '';
          configurePhase = ''
            ./configure --prefix=$out
          '';
          buildPhase = ''
            make V=1 -j
          '';
          installPhase = ''
            make install
          '';

          meta = with pkgs.lib; {
            description = "My awesome application";
            homepage = "https://example.com/myapp";
            license = licenses.mit;
            maintainers = [ maintainers.tylzh97 ];
            platforms = platforms.linux;
          };
        };

        # nix develop
        devShells.default = pkgs.mkShell {
          packages = debugTools
            ++ self.packages.${system}.default.nativeBuildInputs
            ++ self.packages.${system}.default.buildInputs;

          shellHook = ''
            NIX_PATH_ONLY=$(echo $PATH | tr ':' '\n' | grep -E '/nix/store' | tr '\n' ':')
            export PATH=$NIX_PATH_ONLY
            export PATH=$PATH:${pkgs.coreutils}/bin:${pkgs.findutils}/bin:${pkgs.gnugrep}/bin
            PS1='\n\[\033[1;32m\][nix-shell:\w]\[\033[0m\]\n\$ '
          '';
        };
      }
    );
}
```

#### 网络访问和调试

**网络文件下载**：
```nix
let
  qemuSrc = pkgs.fetchurl {
    url = "https://download.qemu.org/qemu-2.10.0.tar.xz";
    sha256 = "55d81ac987a4821d2744359c026d766459319ba9c013746570369068d93ff335";
  };
in
{
  packages.default = pkgs.stdenv.mkDerivation {
    # ...
    preConfigure = ''
      ln -s ${qemuSrc} $PWD/qemu_mode/qemu-2.10.0.tar.xz
    '';
    # ...
  };
}
```

**构建调试**：
使用 `pkgs.breakpointHook` 进行构建调试：

```bash
# 构建失败时的输出
app> build failed in configurePhase with exit code 255
app> To attach install cntr and run the following command as root:
app>
app>    cntr attach -t command cntr-/nix/store/w7xyh195pizkyy44nr6pq387y2qyix1f-app-0.0.1

# 在 root 用户中运行调试
nix profile install nixpkgs#cntr
cntr attach -t command cntr-/nix/store/w7xyh195pizkyy44nr6pq387y2qyix1f-app-0.0.1

# 进入构建环境
nixbld@localhost:/var/lib/cntr$ cntr exec bash
```

#### NixOS 系统配置

**configuration.nix 示例**：
```nix
{ config, pkgs, ... }:

{
  imports = [ ./hardware-configuration.nix ];

  # 启用 Flakes
  nix.settings.experimental-features = [ "nix-command" "flakes" ];

  # 系统包
  environment.systemPackages = with pkgs; [
    vim
    git
    curl
    wget
    htop
  ];

  # 服务配置
  services.openssh.enable = true;
  services.nginx.enable = true;

  # 用户配置
  users.users.myuser = {
    isNormalUser = true;
    extraGroups = [ "wheel" "docker" ];
    shell = pkgs.zsh;
  };

  system.stateVersion = "23.05";
}
```

### Windows 系统管理

> 参考：[[Windows 命令]]、[[Windows 虚拟化]]

#### 磁盘管理

**DiskPart 命令**：
```cmd
# 启动 DiskPart
diskpart

# 列出磁盘
list disk

# 选择磁盘
select disk 0

# 列出分区
list partition

# 选择分区
select partition 1

# 删除分区（强制）
delete partition override

# 创建主分区
create partition primary

# 激活分区
active

# 格式化分区
format fs=ntfs quick

# 分配驱动器号
assign letter=C
```

**PowerShell 磁盘管理**：
```powershell
# 获取磁盘信息
Get-Disk

# 获取分区信息
Get-Partition

# 获取卷信息
Get-Volume

# 创建新分区
New-Partition -DiskNumber 1 -Size 100GB -AssignDriveLetter

# 格式化卷
Format-Volume -DriveLetter D -FileSystem NTFS -NewFileSystemLabel "Data"

# 扩展分区
Resize-Partition -DriveLetter C -Size (Get-PartitionSupportedSize -DriveLetter C).SizeMax
```

#### 系统恢复

**恢复分区管理**：
```cmd
# 查看恢复环境状态
reagentc /info

# 启用恢复环境
reagentc /enable

# 禁用恢复环境
reagentc /disable

# 设置恢复镜像位置
reagentc /setreimage /path C:\Recovery\WindowsRE

# 创建系统修复盘
recimg /createimage C:\RefreshImage
```

**系统文件检查**：
```cmd
# 系统文件检查器
sfc /scannow

# DISM 系统映像修复
DISM /Online /Cleanup-Image /CheckHealth
DISM /Online /Cleanup-Image /ScanHealth
DISM /Online /Cleanup-Image /RestoreHealth

# 内存诊断
mdsched
```

#### 虚拟化技术

**Hyper-V 管理**：
```powershell
# 启用 Hyper-V 功能
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

# 创建虚拟交换机
New-VMSwitch -Name "External Switch" -NetAdapterName "Ethernet" -AllowManagementOS $true

# 创建虚拟机
New-VM -Name "TestVM" -MemoryStartupBytes 2GB -Generation 2 -NewVHDPath "C:\VMs\TestVM.vhdx" -NewVHDSizeBytes 50GB

# 设置虚拟机配置
Set-VMProcessor -VMName "TestVM" -Count 2
Set-VMMemory -VMName "TestVM" -DynamicMemoryEnabled $true -MinimumBytes 1GB -MaximumBytes 4GB

# 启动虚拟机
Start-VM -Name "TestVM"

# 连接到虚拟机
vmconnect localhost "TestVM"
```

### 传统包管理

**多架构支持**：
```bash
# 64 位系统安装 32 位包
sudo dpkg --add-architecture i386
sudo apt-get update
sudo apt-get install package-name:i386

# 多库支持
sudo apt-get install gcc-multilib g++-multilib
```

**NodeJS 版本管理**：
```bash
# 通过 PPA 安装高版本 NodeJS
curl -sL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get update
sudo apt-get install nodejs
node -v
```

---

## 系统监控

### 性能分析

**系统追踪**：
```bash
# 记录系统调用
strace -f -o trace.log ./program
strace -e trace=open,read,write ./program

# 性能分析
perf record ./program
perf report

# 内存分析
valgrind --tool=memcheck ./program
valgrind --tool=callgrind ./program
```

### 日志分析

**Python 日志模块**：
> 参考：[[Python 日志模块 logging]]

```python
import logging
import logging.handlers
import os

class LogManager:
    def __init__(self, name, log_file=None, level=logging.INFO):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers(log_file)
    
    def _setup_handlers(self, log_file):
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file:
            # 确保日志目录存在
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            # 使用轮转文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                log_file, maxBytes=10*1024*1024, backupCount=5
            )
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
    
    def debug(self, message):
        self.logger.debug(message)
    
    def info(self, message):
        self.logger.info(message)
    
    def warning(self, message):
        self.logger.warning(message)
    
    def error(self, message):
        self.logger.error(message)
    
    def critical(self, message):
        self.logger.critical(message)

# 使用示例
log_manager = LogManager('MyApp', '/var/log/myapp/app.log')
log_manager.info('Application started')
log_manager.error('An error occurred')
```

---

## 自动化运维

### 系统服务管理

**systemctl 服务管理**：
> 参考：[[systemctl 使用]]

```bash
# 服务状态管理
sudo systemctl start service_name
sudo systemctl stop service_name
sudo systemctl restart service_name
sudo systemctl reload service_name

# 开机自启动
sudo systemctl enable service_name
sudo systemctl disable service_name

# 查看服务状态
systemctl status service_name
systemctl is-active service_name
systemctl is-enabled service_name

# 查看所有服务
systemctl list-units --type=service
systemctl list-unit-files --type=service
```

**自定义服务单元**：
```ini
# /etc/systemd/system/myapp.service
[Unit]
Description=My Application
After=network.target

[Service]
Type=simple
User=myuser
WorkingDirectory=/opt/myapp
ExecStart=/opt/myapp/bin/myapp
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 文件系统监控

> 参考：[[WatchDog 文件系统监控]]

```python
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import time
import logging

class FileChangeHandler(FileSystemEventHandler):
    def __init__(self, callback=None):
        self.callback = callback
        self.logger = logging.getLogger(__name__)
    
    def on_modified(self, event):
        if not event.is_directory:
            self.logger.info(f"File modified: {event.src_path}")
            if self.callback:
                self.callback(event.src_path, 'modified')
    
    def on_created(self, event):
        if not event.is_directory:
            self.logger.info(f"File created: {event.src_path}")
            if self.callback:
                self.callback(event.src_path, 'created')
    
    def on_deleted(self, event):
        if not event.is_directory:
            self.logger.info(f"File deleted: {event.src_path}")
            if self.callback:
                self.callback(event.src_path, 'deleted')

class FileMonitor:
    def __init__(self, watch_path, callback=None):
        self.watch_path = watch_path
        self.observer = Observer()
        self.handler = FileChangeHandler(callback)
    
    def start_monitoring(self):
        self.observer.schedule(self.handler, self.watch_path, recursive=True)
        self.observer.start()
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        self.observer.stop()
        self.observer.join()

# 使用示例
def file_change_callback(file_path, action):
    print(f"File {file_path} was {action}")

monitor = FileMonitor('/path/to/watch', file_change_callback)
monitor.start_monitoring()
```

---

## 🔗 相关链接

- [[编程语言与框架]]
- [[开发工具链]]
- [[网络与安全工具]]
- [[调试工具集合]]

---

*最后更新：2025-06-16*
