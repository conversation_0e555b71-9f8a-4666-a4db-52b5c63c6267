# Fastbin Attack

## 概述

<GeminiOptimizationFrom> Note/CTF-PWN/FastbinAttack.md </GeminiOptimizationFrom>

Fastbin Attack 是一种针对 glibc malloc 实现中 fastbin 机制的堆漏洞利用技术。通过修改 fastbin chunk 的 fd 指针，攻击者可以控制内存分配，实现任意地址写入。

## 参考资料

- 吾爱破解论坛：https://www.52pojie.cn/forum.php?mod=viewthread&tid=1882363
- libc 官方 Git：https://sourceware.org/git/gitweb.cgi?p=glibc.git
- libc GitHub 镜像：https://github.com/bminor/glibc

## 漏洞原理

### 漏洞原因
- **Overflow**: 缓冲区溢出
- **UAF**: Use After Free

### 适用范围
- **libc 2.23 ~ 至今**
- 可以伪造 `fastbin chunk` 的 `fd` 指针

### 利用原理
修改 fastbin chunk 的 fd 指针指向 fake fastbin chunk，使得链表指向伪造的 chunk，从伪造 chunk 中申请 chunk。

### 相关技巧
- **2.34 前**：可以攻击 `__malloc_hook` 函数
- **2.35 后**：需要结合其他技巧，例如 `house of corrosion` 进行利用

### 利用效果
把伪造 chunk 变成 fastbin chunk 并申请走。

## 实验一：how2heap - fastbin dup

### 实验环境
libc 2.35

### 核心代码

```cpp
#include <stdio.h>
#include <stdlib.h>
#include <assert.h>

int main()
{
    setbuf(stdout, NULL);

    printf("This file demonstrates a simple double-free attack with fastbins.\n");

    printf("Fill up tcache first.\n");
    void *ptrs[8];
    for (int i=0; i<8; i++) {
        ptrs[i] = malloc(8);
    }
    for (int i=0; i<7; i++) {
        free(ptrs[i]);
    }

    printf("Allocating 3 buffers.\n");
    int *a = calloc(1, 8);
    int *b = calloc(1, 8);
    int *c = calloc(1, 8);

    printf("1st calloc(1, 8): %p\n", a);
    printf("2nd calloc(1, 8): %p\n", b);
    printf("3rd calloc(1, 8): %p\n", c);

    printf("Freeing the first one...\n");
    free(a);

    printf("So, instead, we'll free %p.\n", b);
    free(b);

    printf("Now, we can free %p again, since it's not the head of the free list.\n", a);
    free(a);

    printf("Now the free list has [ %p, %p, %p ]. If we malloc 3 times, we'll get %p twice!\n", a, b, a, a);
    a = calloc(1, 8);
    b = calloc(1, 8);
    c = calloc(1, 8);
    printf("1st calloc(1, 8): %p\n", a);
    printf("2nd calloc(1, 8): %p\n", b);
    printf("3rd calloc(1, 8): %p\n", c);

    assert(a == c);
}
```

### 攻击流程

1. **填充 tcache**：为了使用 fastbin，首先填充满 tcachebin
2. **申请 chunk**：从 fastbin 申请 3 个 chunk：A，B，C
3. **双重释放**：按照顺序进行释放：A，B，A，完成对 fastbin 的双重释放
4. **控制指针**：同一个 chunk 被释放两次，意味着在第二次被申请前可以修改 chunk 指针

### 内存布局分析

释放后的 fastbin 链表：
```
0x20: 0x55555555b390 —▸ 0x55555555b3b0 ◂— 0x55555555b390
```

### 为什么可以 double-free？

在 `_int_free` 中对 fastbin 处理里，有一个双重释放安全检查：

```c
/* Check that the top of the bin is not the record we are going to
   add (i.e., double free).  */
// 如果该fastbin当前指向的chunk和要释放的chunk指针相同，报错，双重释放检测
// 只检测第一个chunk是否和要释放的chunk相同
if (__builtin_expect(old == p, 0))
    malloc_printerr("double free or corruption (fasttop)");
// 指针加密
p->fd = PROTECT_PTR(&p->fd, old);
*fb = p;
```

这个检查和 libc 2.23 一样，只检查释放的 fastbin 链表中的第一个 chunk 和释放的 chunk 是否是同一个。如果不是连续的，就会存在双重释放问题。

## 实验二：fastbin dup into stack

### 核心思想
在 fastbin dup 的基础上，将 fake chunk 构造在栈上，实现对栈内存的控制。

### 关键技术点

#### 1. 指针加密机制
fastbin 指针加密在 libc 2.32 引入：

```c
#define PROTECT_PTR(pos, ptr) \
    ((__typeof(ptr))((((size_t)pos) >> 12) ^ ((size_t)ptr)))
#define REVEAL_PTR(ptr) PROTECT_PTR(&ptr, ptr)
```

#### 2. 加密解密过程
- **加密**：`(pos >> 12) ^ ptr`
- **解密**：`(pos >> 12) ^ encrypted_ptr`

其中：
- `pos`：fd 指针所在的地址
- `ptr`：原始指针值

#### 3. 构造 fake chunk
在栈上构造 fake chunk：
```c
unsigned long stack_var[4] __attribute__ ((aligned (0x10)));
stack_var[1] = 0x20;  // fake size
```

#### 4. 覆盖 fd 指针
```c
unsigned long ptr = (unsigned long)stack_var;
unsigned long addr = (unsigned long) d;
*d = (addr >> 12) ^ ptr;  // 加密后的指针
```

## 安全检查机制

### libc 2.35 中的检查

1. **内存对齐检查**：检查 fastbin top chunk 的内存对齐
2. **指针解密**：解密该 chunk 的 fd 指针
3. **大小匹配检查**：检查 fastbin top size 对应的 fastbin idx 与当前使用的 idx 是否相同

### libc 2.23 中的检查
只有一个检查：检查 fastbin chunk 大小是否匹配对应的 fastbin。

## fastbin_index 计算

### 相关宏定义
```c
#define chunksize(p) ((p)->size & ~(SIZE_BITS))

#define fastbin_index(sz) \
    ((((unsigned int)(sz)) >> (SIZE_SZ == 8 ? 4 : 3)) - 2)
```

### 重要特性
大小检查时会跳过后 4 位，也就是说，size 为 0x7f 的 chunk，计算时会被当作 0x70 来看。

## 防护绕过技术

### 1. 寻找 fake chunk
在目标地址附近寻找合适的 size 字段，使其能够通过 fastbin 的大小检查。

### 2. 指针加密绕过
理解并正确计算指针加密算法，构造正确的加密指针。

### 3. 对齐要求
确保 fake chunk 满足内存对齐要求。

## 实际应用场景

### 攻击目标
1. **__malloc_hook**（libc 2.34 前）
2. **__free_hook**（libc 2.34 前）
3. **栈变量**
4. **全局变量**
5. **函数指针**

### 利用链构造
1. **信息泄露**：泄露 libc 基址
2. **fastbin dup**：构造双重释放
3. **fake chunk**：在目标位置构造 fake chunk
4. **指针劫持**：覆盖关键指针
5. **代码执行**：触发 shell 或 ROP 链

## 相关主题

- [[Ptmalloc_Bins]] - Ptmalloc Bins 结构详解
- [[Libc_Malloc_内部机制]] - malloc 内部实现
- [[堆漏洞利用总结]] - 堆漏洞利用方法总结
