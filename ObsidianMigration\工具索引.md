# 工具索引

> 按功能分类的工具快速查找索引

## 📋 目录

- [[#安全分析工具]]
- [[#开发调试工具]]
- [[#系统管理工具]]
- [[#网络工具]]
- [[#多媒体工具]]
- [[#AI 与自动化工具]]

---

## 安全分析工具

### 模糊测试
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **AFL** | 覆盖率引导的模糊测试 | [[AFL 使用说明]] |
| **AFL++** | AFL 的增强版本 | [[AFL 开发踩坑说明]] |
| **libFuzzer** | LLVM 内置模糊测试器 | [[编译器技术与程序分析]] |

### 动态分析
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Intel Pin** | 动态二进制插桩框架 | [[Intel Pin 使用]] |
| **PANDA-re** | 全系统动态分析平台 | [[PANDA 动态分析器]] |
| **QEMU** | 系统仿真与插桩 | [[系统虚拟化与仿真]] |
| **Frida** | 动态代码插桩工具 | - |

### 静态分析
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Clang Static Analyzer** | C/C++ 静态分析 | [[编译器技术与程序分析]] |
| **Cppcheck** | C/C++ 代码检查 | - |
| **SonarQube** | 多语言代码质量分析 | - |

### 内存安全检测
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **AddressSanitizer** | 内存错误检测 | [[Sanitizer 工具]] |
| **ThreadSanitizer** | 数据竞争检测 | [[ThreadSanitizer]] |
| **MemorySanitizer** | 未初始化内存检测 | [[Sanitizer 工具]] |
| **Valgrind** | 内存调试和性能分析 | [[调试工具集合]] |

### 逆向工程
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **IDA Pro** | 交互式反汇编器 | [[IDA Pro Downloads]] |
| **Ghidra** | NSA 开源逆向工程工具 | - |
| **Radare2** | 开源逆向工程框架 | - |
| **Capstone** | 反汇编引擎 | [[Capstone 使用]] |

---

## 开发调试工具

### 调试器
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **GDB** | GNU 调试器 | [[GDB 命令速查]] |
| **LLDB** | LLVM 调试器 | [[编译器技术与程序分析]] |
| **WinDbg** | Windows 内核调试器 | - |

### 编译器
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **GCC** | GNU 编译器集合 | [[gcc 查看头文件引用]] |
| **Clang** | LLVM C/C++ 编译器 | [[编译器技术与程序分析]] |
| **MSVC** | Microsoft Visual C++ | - |

### 构建工具
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Make** | 构建自动化工具 | [[Makefile 使用]] |
| **CMake** | 跨平台构建系统 | [[CMake and Clang Commands]] |
| **Ninja** | 小型构建系统 | - |
| **Bazel** | Google 构建工具 | - |

### 版本控制
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Git** | 分布式版本控制 | [[Git 常用命令]] |
| **Git LFS** | 大文件存储 | [[Git LFS]] |
| **SVN** | 集中式版本控制 | - |

### 性能分析
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **perf** | Linux 性能分析工具 | [[调试工具集合]] |
| **Intel VTune** | Intel 性能分析器 | - |
| **gperftools** | Google 性能工具 | - |

---

## 系统管理工具

### 容器技术
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Docker** | 容器化平台 | [[Docker 基础命令]] |
| **Podman** | 无守护进程容器引擎 | - |
| **Kubernetes** | 容器编排平台 | - |

### 虚拟化
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **QEMU** | 系统仿真器 | [[系统虚拟化与仿真]] |
| **VirtualBox** | 桌面虚拟化 | - |
| **VMware** | 企业虚拟化 | [[VMware 配置]] |
| **KVM** | 内核虚拟机 | - |

### 系统监控
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **htop** | 交互式进程查看器 | [[系统管理与运维]] |
| **iotop** | I/O 监控工具 | - |
| **nethogs** | 网络使用监控 | - |
| **Prometheus** | 监控和告警系统 | - |

### 包管理
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **apt** | Debian/Ubuntu 包管理 | [[系统管理与运维]] |
| **yum/dnf** | RedHat/Fedora 包管理 | - |
| **pacman** | Arch Linux 包管理 | - |
| **Nix** | 函数式包管理 | [[NixOS 全局配置]] |

---

## 网络工具

### 网络分析
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Wireshark** | 网络协议分析器 | - |
| **tcpdump** | 命令行抓包工具 | [[调试工具集合]] |
| **nmap** | 网络扫描工具 | [[命令行工具与脚本]] |
| **netcat** | 网络瑞士军刀 | - |

### 代理工具
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Clash** | 代理客户端 | [[ClashForWindows]] |
| **V2Ray** | 网络代理工具 | - |
| **Shadowsocks** | 安全代理协议 | - |

### 证书管理
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **OpenSSL** | 加密工具包 | [[OpenSSL 使用]] |
| **acme.sh** | 自动证书管理 | [[ACME 证书管理]] |
| **Let's Encrypt** | 免费 SSL 证书 | - |
| **Charles** | HTTP 代理调试工具 | [[Charles 证书]] |

---

## 多媒体工具

### 音视频处理
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **FFmpeg** | 音视频处理工具 | [[FFmpeg 使用]] |
| **VLC** | 多媒体播放器 | - |
| **OBS Studio** | 直播录制软件 | - |

### 图像处理
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **ImageMagick** | 图像处理工具 | - |
| **GIMP** | 图像编辑软件 | - |
| **OpenCV** | 计算机视觉库 | [[OpenCV 使用]] |

### 下载工具
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Aria2** | 多协议下载工具 | [[Aria2 使用]] |
| **wget** | 命令行下载工具 | [[命令行工具与脚本]] |
| **curl** | 数据传输工具 | [[命令行工具与脚本]] |
| **youtube-dl** | 视频下载工具 | - |

---

## AI 与自动化工具

### AI 开发
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **PyTorch** | 深度学习框架 | [[PyTorch 常用组件]] |
| **TensorFlow** | 机器学习平台 | - |
| **Jupyter** | 交互式开发环境 | - |

### OCR 工具
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Tesseract** | 开源 OCR 引擎 | [[JS OCR Tesseract]] |
| **ddddocr** | 中文 OCR 库 | [[AI 辅助开发]] |
| **PaddleOCR** | 百度 OCR 工具 | - |

### 自动化工具
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Selenium** | Web 自动化测试 | [[Selenium 使用]] |
| **Pywinauto** | Windows 自动化 | [[Pywinauto Windows窗口自动化库]] |
| **ADB** | Android 调试桥 | [[Python ADB]] |

### 文档工具
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Obsidian** | 知识管理工具 | 当前知识库 |
| **Notion** | 协作文档平台 | - |
| **Markdown** | 轻量级标记语言 | 所有文档 |

---

## 编程语言工具

### Python 生态
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **pip** | Python 包管理器 | [[编程语言与框架]] |
| **conda** | 科学计算包管理 | - |
| **virtualenv** | 虚拟环境管理 | - |
| **Flask** | Web 框架 | [[Flask 框架]] |
| **Django** | Web 框架 | - |

### JavaScript 生态
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Node.js** | JavaScript 运行时 | [[系统管理与运维]] |
| **npm** | Node.js 包管理器 | - |
| **webpack** | 模块打包工具 | - |
| **jQuery** | JavaScript 库 | [[jQuery 基础]] |

### 数据库工具
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **SQLite** | 轻量级数据库 | [[SQLite3 使用]] |
| **Neo4j** | 图数据库 | [[Neo4j 使用]] |
| **MySQL** | 关系型数据库 | - |
| **PostgreSQL** | 高级关系型数据库 | - |

---

## 终端工具

### 终端增强
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **Tmux** | 终端复用器 | [[Tmux 使用]] |
| **Screen** | 终端会话管理 | - |
| **Zsh** | 高级 Shell | - |
| **Oh My Zsh** | Zsh 配置框架 | - |

### 文本处理
| 工具 | 功能 | 参考文档 |
|------|------|----------|
| **vim/neovim** | 文本编辑器 | - |
| **emacs** | 文本编辑器 | - |
| **sed** | 流编辑器 | [[命令行工具与脚本]] |
| **awk** | 文本处理语言 | [[命令行工具与脚本]] |
| **jq** | JSON 处理工具 | [[命令行工具与脚本]] |

---

## 快速查找

### 按使用场景

#### 🔍 漏洞研究
- AFL → [[AFL 使用说明]]
- GDB → [[GDB 命令速查]]
- QEMU → [[系统虚拟化与仿真]]
- IDA Pro → [[IDA Pro Downloads]]

#### 🛠️ 开发调试
- Clang → [[编译器技术与程序分析]]
- Git → [[Git 常用命令]]
- Docker → [[Docker 基础命令]]
- Valgrind → [[调试工具集合]]

#### 🖥️ 系统管理
- Linux 命令 → [[命令行工具与脚本]]
- systemctl → [[系统管理与运维]]
- 网络工具 → [[网络与安全工具]]

#### 🤖 AI 开发
- PyTorch → [[PyTorch 常用组件]]
- OCR → [[AI 辅助开发]]
- ChatGPT → [[AI 辅助开发]]

---

## 🔗 相关链接

- [[README]]
- [[调试工具集合]]
- [[命令行工具与脚本]]
- [[AI 辅助开发]]

---

*最后更新：2025-06-16*
