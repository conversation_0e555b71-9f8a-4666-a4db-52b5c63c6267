# torch.Tensor 常用内置方法

---



以下是一些 `torch.Tensor` 常用函数及其功能：

1. **`squeeze()`**
     移除 tensor 中所有维度为 1 的轴。

    ```python
    x = torch.randn(1, 3, 1, 4)
    y = x.squeeze()  # 结果的形状为 (3, 4)
    ```

2. **`unsqueeze()`**
     在指定的维度上增加一个大小为 1 的轴。

    ```python
    x = torch.randn(3, 4)
    y = x.unsqueeze(0)  # 结果的形状为 (1, 3, 4)
    ```

3. **`argmax()`**
     返回最大值的索引。可以指定维度，默认返回展平后的索引。

    ```python
    x = torch.randn(3, 4)
    index = x.argmax()  # 返回最大值的索引
    index_dim = x.argmax(dim=1)  # 按行寻找最大值的索引
    ```

4. **`max()`**
     返回最大值及其索引。

    ```python
    x = torch.randn(3, 4)
    max_vals, indices = x.max(dim=1)  # 返回每行的最大值和索引
    ```

5. **`mean()`**
     计算 tensor 的均值。可以指定维度。

    ```python
    x = torch.randn(3, 4)
    mean_val = x.mean()  # 计算所有元素的均值
    mean_dim = x.mean(dim=1)  # 计算每行的均值
    ```

6. **`sum()`**
     计算 tensor 的总和。也可以指定维度。

    ```python
    x = torch.randn(3, 4)
    total_sum = x.sum()  # 所有元素的和
    sum_dim = x.sum(dim=1)  # 按行计算和
    ```

7. **`view()`**
     改变 tensor 的形状（类似于 numpy 的 reshape）。相较于 `tensor.reshape` 速度更快, 但是要求输入为连续内存(即经过`view`或者`permute`、`transpose`重排后的 Tensor 可能无法使用)

    ```python
    x = torch.randn(3, 4)
    y = x.view(2, 6)  # 改变形状为 (2, 6)
    ```

8. **`reshape()`**
     与 `view()` 类似，但可以在不保证连续内存的情况下重新调整形状。

    ```python
    x = torch.randn(3, 4)
    y = x.reshape(2, 6)
    ```

9. **`transpose()`**
     交换 tensor 的两个维度。

    ```python
    x = torch.randn(3, 4)
    y = x.transpose(0, 1)  # 交换第 0 维和第 1 维，结果形状为 (4, 3)
    ```

10. **`t()`**
     对**二维** tensor 进行转置。

    ```python
    x = torch.randn(3, 4)
    y = x.t()  # 转置结果形状为 (4, 3)
    ```

11. **`flatten()`**
     将多维 tensor 拉平成一维。

    ```python
    x = torch.randn(3, 4, 2)
    y = x.flatten()  # 拉平成 (24,)
    ```

12. **`clone()`**
     创建 tensor 的副本，避免原 tensor 的修改影响副本。

    ```python
    x = torch.randn(3, 4)
    y = x.clone()  # 创建副本
    ```

13. **`repeat()`**
     按指定的次数重复 tensor。

    ```python
    x = torch.randn(1, 3)
    y = x.repeat(2, 3)  # 结果的形状为 (1*2=2, 3*3=9)
    ```

14. **`cat()`**
     将多个 tensor 按指定维度拼接。

    ```python
    x = torch.randn(2, 3)
    y = torch.randn(2, 3)
    z = torch.cat((x, y), dim=0)  # 按第 0 维拼接，结果形状为 (4, 3)
    ```

15. **`stack()`**
     在新维度上堆叠多个 tensor。

    ```python
    x = torch.randn(2, 3)
    y = torch.randn(2, 3)
    z = torch.stack((x, y), dim=0)  # 结果形状为 (2, 2, 3)
    ```

16. **`permute()`**

    重新排列 Tensor 的维度, 即修改 Tensor 的各个维度的顺序

    ```python
    # 创建一个 3x4x5 的 tensor
    x = torch.randn(3, 4, 5)
    
    # permute 维度顺序
    y = x.permute(2, 0, 1)  # 将维度顺序改为 (5, 3, 4)
    
    print("原 tensor 形状:", x.shape)  # torch.Size([3, 4, 5])
    print("新 tensor 形状:", y.shape)  # torch.Size([5, 3, 4])
    ```

17. **`contiguous()`**

    获取 Tensor 的连续内存版本. 如果当前的 Tensor 已经是连续内存, 则直接返回本身; 如果当前 Tensor 经过例如 `transpose()` 或 `permute()` 等操作, 则返回一个新的 Tensor.

    ```python
    t0 = torch.randn(3,4,5)
    # 如果已经是连续内存, 则直接返回当前的对象
    t1 = t0.contiguous()
    assert t0.untyped_storage().data_ptr() == t1.untyped_storage().data_ptr()
    # 变化矩阵的维度并不会修改底层的数据对象, 但会使其成为非连续内存对象
    t2 = t0.permute(2,0,1)
    assert t0.untyped_storage().data_ptr() == t2.untyped_storage().data_ptr()
    # 对于非连续内存对象, 会得到一个新的 Tensor
    t3 = t2.contiguous()
    assert t0.untyped_storage().data_ptr() == t3.untyped_storage().data_ptr() # Fail
    ```

     