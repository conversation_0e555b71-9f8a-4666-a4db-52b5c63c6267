# 编译过程基本概念

---



### 编译工具链基本概念

- CC：编译器，对C源文件进行编译处理，生成汇编文件。
- AS：将汇编文件生成目标文件（汇编文件使用的是指令助记符，AS将它翻译成机器码）。
- AR：打包器，用于库操作，可以通过该工具从一个库中删除或者增加目标代码模块。
- LD：链接器，为前面生成的目标代码分配地址空间，将多个目标文件链接成一个库或者是可执行文件。
- GDB：调试工具，可以对运行过程中的程序进行代码调试工作。
- STRIP：以最终生成的可执行文件或者库文件作为输入，然后消除掉其中的源码。
- NM：查看静态库文件中的符号表。
- Objdump：查看静态库或者动态库的方法签名。



### 常见的编译命令

在编译过程中，我们可以使用各种编译选项和命令来控制编译器的行为和生成的目标文件的特性。下面是常见的编译选项和命令及其作用：

- `-Wl`: 把后面的参数传递给链接器
- `-W`: 开启编译器的警告信息
- `-I`: 添加头文件搜索路径
- `-L`: 添加库文件搜索路径
- `-l`: 链接库文件，指定需要链接的库文件名。注意，此处需要去掉库文件名前面的"lib"前缀和后缀名
- `-f`: 设置编译选项，如 `-fPIC` 表示生成位置独立的代码
- `-D`: 定义宏，即在编译过程中为代码添加预定义的宏定义
- `-fPIC`: 表示生成位置无关代码（Position Independent Code，PIC）, **在需要生成动态链接库的场景下，使用`-fPIC`选项是必须的**

需要注意的是，不同的编译器可能有不同的命令选项，因此需要根据具体的编译器来选择合适的编译选项和命令。





