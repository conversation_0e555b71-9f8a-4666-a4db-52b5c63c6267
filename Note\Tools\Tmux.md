TMUX - Terminal MUltipleXer

---



`tmux` 是一个终端多路复用器，这意味着它允许你在一个终端窗口中打开多个会话，每个会话都有自己的窗口和面板。`tmux` 提供了丰富的命令和快捷键，供你管理和切换这些会话、窗口和面板。

`tmux` 的配置可以通过修改 `~/.tmux.conf` 文件来进行，你可以在这个文件中设置你喜欢的颜色主题、增加或修改快捷键等。



### 基本概念

- 会话（Session）：每个 `tmux` 进程都有一个或多个会话，每个会话都是独立的，可以有自己的窗口和名字。
- 窗口（Window）：窗口就像一个标签页，每个窗口都运行在指定的会话中。一个会话可以有多个窗口，每个窗口都有一个索引号和（可选的）名字。
- 面板（Pane）：面板是窗口中的一个子窗口，你可以在一个窗口中分割出多个面板。 



### 常用命令

- `tmux` 或者 `tmux new`：新建一个会话。
- `tmux new-session -s session_name`：新建一个有名字的会话。
- `tmux attach` 或者 `tmux a`：连接到上一次的会话。
- `tmux attach -t session_name`：连接到指定名字的会话。
- `tmux ls`：列出所有会话。



### 常见参数含义

4. `-c` ：让新创建的窗口在指定的目录中打开。
5. `-n` ：定义一个窗口的名字。
6. `-d` ：使 `tmux` 在后台运行。
7. `-t` ：指定目标窗口，用于挂载或发送键。
8. `-s` ：为一个新会话指定名称。
9. `-v` ：垂直分割窗口。
10. `-h`：水平分割窗口。



### 常用快捷键

`tmux` 的操作基本都是通过按下 `Ctrl-b` 来进行的，以下是一些常用的快捷键：

- `Ctrl-b c`：新建一个窗口。
- `Ctrl-b n` / `Ctrl-b l` / `Ctrl-b p`：切换到下一个/最后一个/上一个窗口。
- `Ctrl-b 0` 到 `Ctrl-b 9`：切换到对应编号的窗口。
- `Ctrl-b "` / `Ctrl-b %`：垂直/水平分割当前窗口来创建新的面板。
- `Ctrl-b o` / `Ctrl-b ;`：切换到下一个/上一个面板。



### 常用功能

#### 在外界 Shell 中创建 Tmux Session 并在内部的多个窗口中运行多个命令

```shell
tmux new-session -s mysession -d 'ping 8.8.8.8'
tmux new-window  -t mysession:1  'ping 223.5.5.5'
tmux new-window  -t mysession:2  'top'
tmux a -t myssion
# 使用 CTRL+B n(下一个 Window)/p(上一个 Window)/<number>(指定编号的窗口) 切换 Window
tmux kill-session -t mysession
```





#### 获取 tmux 指定 pane 的内容

```shell
tmux capture-pane -t fuzz_16_databank -p
tmux capture-pane -t fuzz_16_databank:0 -p
```

`-t`：指定会话名、窗口索引和面板索引。

`-p`：打印面板的内容。

```python
import subprocess

def get_tmux_pane_content(session_name, window_index, pane_index):
    # 构建 tmux 命令
    cmd = ["tmux", "capture-pane", "-t", f"{session_name}:{window_index}.{pane_index}", "-p"]
    
    # 执行命令并捕获输出
    try:
        output = subprocess.check_output(cmd, text=True)
        return output
    except subprocess.CalledProcessError as e:
        print(f"Error capturing tmux pane: {e}")
        return None

# 示例使用
session_name = "my_session"
window_index = 0
pane_index = 1

content = get_tmux_pane_content(session_name, window_index, pane_index)
if content:
    print("Pane Content:\n", content)
```

