# ELF 文件格式

## 概述

<GeminiOptimizationFrom> Note/Linux/ELF 文件段格式.md </GeminiOptimizationFrom>

ELF (Executable and Linkable Format) 是 Linux 系统中可执行文件、目标文件、共享库和核心转储文件的标准格式。理解 ELF 文件的段结构对于系统编程、逆向工程和安全研究至关重要。

## ELF 文件段结构

| ELF Section   | 描述                                            | 备注                             |
| :------------ | ----------------------------------------------- | -------------------------------- |
| .init         | 初始化代码段                                    | 程序启动时执行的初始化代码       |
| .plt          | Procedure Linkage Table, 存放长跳转格式函数调用 | 静态链接时确定, 静态链接期间生成 |
| .plt.got      | PLT 的 GOT 部分                                 | 与 .plt 配合使用                 |
| .text         | 代码段                                          | 存放程序的可执行指令             |
| .fini         | 终止代码段                                      | 程序结束时执行的清理代码         |
| .rodata       | 存放只读数据                                    | 常量字符串、常量数组等           |
| .eh_frame_hdr | 异常处理框架头                                  | 用于异常处理和栈展开             |
| .eh_frame     | 异常处理框架                                    | 详细的异常处理信息               |
| .init_array   | 初始化函数指针数组                              | 存放构造函数指针                 |
| .jcr          | Java 类注册表                                   | 用于 Java 相关功能               |
| .got          | Global Offset Table, 全局偏移表                 | 在编译期间确定, 静态链接时生成   |
| .got.plt      | GOT 的 PLT 部分                                 | 与 .got 配合使用                 |
| .data         | 数据段                                          | 存放已初始化的全局变量           |
| .bss          | 未初始化数据段                                  | 存放未初始化的全局变量           |
| .prgend       | 程序结束标记                                    | 标记程序的结束位置               |
| extern        | 外部符号                                        | 引用的外部函数和变量             |

## 重要段详解

### 代码相关段

- **.text**: 包含程序的可执行指令，这是程序的核心部分
- **.init/.fini**: 分别包含程序初始化和终止时执行的代码
- **.plt**: 过程链接表，用于动态链接时的函数调用

### 数据相关段

- **.data**: 存放已初始化的全局和静态变量
- **.bss**: 存放未初始化的全局和静态变量（在程序加载时会被清零）
- **.rodata**: 存放只读数据，如字符串常量

### 链接相关段

- **.got**: 全局偏移表，用于存放全局变量的地址
- **.got.plt**: 与过程链接表配合使用的全局偏移表部分

## 分析工具

### 常用命令

```bash
# 查看 ELF 文件头信息
readelf -h <file>

# 查看段表信息
readelf -S <file>

# 查看程序头表信息
readelf -l <file>

# 查看符号表
readelf -s <file>

# 使用 objdump 查看反汇编
objdump -d <file>

# 查看段内容
objdump -s -j .rodata <file>
```

## 实际应用

理解 ELF 文件格式在以下场景中非常重要：

1. **逆向工程**: 分析程序结构和功能
2. **安全研究**: 查找漏洞和分析恶意软件
3. **系统编程**: 理解程序加载和执行过程
4. **调试**: 定位问题和分析程序行为

## 相关主题

- [[编译工具链]] - 编译工具链概述
- [[GCC_命令参考]] - GCC 编译器命令参考
- [[JMP_指令详解]] - 汇编指令详解
