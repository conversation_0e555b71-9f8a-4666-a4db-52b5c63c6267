# 自定义堆管理器

---



## dlmalloc

Doug Lea在1987年开发的一个非常著名的堆管理器。它是一个基于分离的空闲链表管理的堆管理器，效率高，被广泛使用。

### 官网

> https://github.com/ennorehling/dlmalloc
>
> https://gee.cs.oswego.edu/dl/html/malloc.html



### User APIs

> https://github.com/ennorehling/dlmalloc/blob/71296436f979a350870e10b869ccfd28bfcc17e4/malloc.c#L60C3-L60C80

```c
malloc(size_t n);
calloc(size_t n_elements, size_t element_size);
free(Void_t* p);
realloc(Void_t* p, size_t n);
memalign(size_t alignment, size_t n);
valloc(size_t n);
mallinfo()
mallopt(int parameter_number, int parameter_value)
```


### 附加说明

1. However it is among the **fastest** while also being among the most space-conserving, portable and tunable.
2. 线程安全性: NOT thread-safe unless USE_MALLOC_LOCK defined



## jemalloc

由Facebook的Jason Evans创建，主要用于高性能和可伸缩性。jemalloc在处理并发分配时非常出色，被许多大型的开源软件项目所采用。以下项目使用了`jemalloc`堆管理器:

- MySQL
- Redis
- Nginx

### 官网

> https://jemalloc.net/
>
> https://github.com/jemalloc/jemalloc
>
> https://jemalloc.net/jemalloc.3.html



### User APIs

标准 API:

```c
// 分配 size 字节大小的未初始化内存, 会对分配的内存进行适当的对其, 用于存储任何类型的对象
void *malloc(	size_t size);

// 分配 number*size 字节大小的内存, 并初始化为 0
void *calloc(	size_t number,
 	size_t size);

// 分配 size 字节大小的内存, 并尝试使用 alignment 倍数进行内存对齐. 
// 需要传入一个指向指针的指针获取分配的内存
// 返回值为 0, 表示分配成功
int posix_memalign(	void **ptr,
 	size_t alignment,
 	size_t size);

// 分配一个根据 alignment 倍数对齐的内存片段
void *aligned_alloc(	size_t alignment,
 	size_t size);

// 根据原本分配的内存, 改变其堆块的大小, 并返回新的地址
// 返回的地址可能改变也可能不会改变
// 如果地址改变, 则原本的数据也会被拷贝过来
// 如果 size 变大, 且地址不需要改变, 此时变大部分存储的内容是未定义的
void *realloc(	void *ptr,
 	size_t size);

// 用来释放上述 API 分配的堆块
void free(	void *ptr);
```



非标准 API:

```c
void *mallocx(	size_t size,
 	int flags);
 
void *rallocx(	void *ptr,
 	size_t size,
 	int flags);
 
size_t xallocx(	void *ptr,
 	size_t size,
 	size_t extra,
 	int flags);
 
size_t sallocx(	void *ptr,
 	int flags);
 
void dallocx(	void *ptr,
 	int flags);
 
void sdallocx(	void *ptr,
 	size_t size,
 	int flags);
 
size_t nallocx(	size_t size,
 	int flags);
 
int mallctl(	const char *name,
 	void *oldp,
 	size_t *oldlenp,
 	void *newp,
 	size_t newlen);
 
int mallctlnametomib(	const char *name,
 	size_t *mibp,
 	size_t *miblenp);
 
int mallctlbymib(	const size_t *mib,
 	size_t miblen,
 	void *oldp,
 	size_t *oldlenp,
 	void *newp,
 	size_t newlen);
 
void malloc_stats_print(	void (*write_cb) (void *, const char *) ,
 	void *cbopaque,
 	const char *opts);
 
size_t malloc_usable_size(	const void *ptr);
 
void (*malloc_message)(	void *cbopaque,
 	const char *s);
 
const char *malloc_conf;
```



## ptmalloc (PThreads Malloc)

ptmalloc是glibc（GNU C库）的堆管理器之一，用于Linux系统。 **事实上就是 GLibc 的堆管理器**

### 官网

> https://sourceware.org/glibc/wiki/MallocInternals
>
> https://www.gnu.org/software/libc/manual/html_node/The-GNU-Allocator.html

似乎可以通过 lic 中的`_malloc_stats `数据结构获取当前程序中的堆分配情况: 

> https://www.qnx.com/developers/docs/7.0.0////index.html#com.qnx.doc.ide.userguide/topic/libc_allocator_api.html
>
> https://web.archive.org/web/20231030083805/https://www.qnx.com/developers/docs/7.0.0////index.html#com.qnx.doc.ide.userguide/topic/libc_allocator_api.html



### User APIs

```c
void *malloc (size_t __size);

void *calloc (size_t __nmemb, size_t __size);

void *realloc (void *__ptr, size_t __size);

void *reallocarray (void *__ptr, size_t __nmemb, size_t __size);

void free (void *__ptr);

void *valloc (size_t __size);

int posix_memalign (void **__memptr, size_t __alignment, size_t __size);

void *aligned_alloc (size_t __alignment, size_t __size);
```





## TCMalloc

TCMalloc 是 Google 对 C 的 malloc（） 和 C++ 运算符的自定义实现，用于 C 和 C++ 代码中的内存分配。TCMalloc 是一种快速的多线程 malloc 实现。



### 官网

> https://github.com/google/tcmalloc
>
> https://github.com/google/tcmalloc/blob/master/docs/reference.md





### User APIs

> https://github.com/google/tcmalloc/blob/master/docs/reference.md#malloc

```c
void* malloc(size_t size);

void* calloc(size_t num, size_t size);

void* realloc(void *ptr, size_t new_size);

void* aligned_alloc(size_t alignment, size_t size);

int posix_memalign(void **memptr, size_t alignment, size_t size);

void free(void* ptr);

void* nallocx(size_t size, int flags);

int sdallocx(void* ptr, size_t size, int flags);
```





## Talloc

TA ("Tree Allocator") , Talloc 是一个分层的、引用计数的内存池系统，带有析构函数。它建立在 C 标准库之上，它定义了一组实用程序函数，这些函数完全简化了数据的分配和释放，特别是对于包含许多动态分配元素（如字符串和数组）的复杂结构。   

该库的主要目标是：消除为每个复杂结构创建清理函数的需要，提供分配的内存块的逻辑组织，并减少在长时间运行的应用程序中创建内存泄漏的可能性。所有这些都是通过在 talloc 上下文的分层结构中分配内存来实现的，这样，以递归方式释放一个上下文也会释放其所有后代。

### 官网

> https://talloc.samba.org/talloc/doc/html/index.html
>
> https://talloc.samba.org/talloc/doc/html/group__talloc.html



### User APIs

官网 API:

```c
void * 	talloc (const void *ctx,#type)
 	// Create a new talloc context.
    
void * 	talloc_init (const char *fmt,...)
 	// Create a new top level talloc context.
    
int 	talloc_free (void *ptr)
 	// Free a chunk of talloc memory.
    
void 	talloc_free_children (void *ptr)
 	// Free a talloc chunk's children.
    
void 	talloc_set_destructor (const void *ptr, int(*destructor)(void *))
 	// Assign a destructor function to be called when a chunk is freed.
    
void * 	talloc_steal (const void *new_ctx, const void *ptr)
 	// Change a talloc chunk's parent.
    
const char * 	talloc_set_name (const void *ptr, const char *fmt,...)
 	// Assign a name to a talloc chunk.
    
void * 	talloc_move (const void *new_ctx, void **pptr)
 	// Change a talloc chunk's parent.
    
void 	talloc_set_name_const (const void *ptr, const char *name)
 	// Assign a name to a talloc chunk.
    
void * 	talloc_named (const void *context, size_t size, const char *fmt,...)
 	// Create a named talloc chunk.
    
void * 	talloc_named_const (const void *context, size_t size, const char *name)
 	// Basic routine to allocate a chunk of memory.
    
void * 	talloc_size (const void *ctx, size_t size)
 	// Untyped allocation.
    
void * 	talloc_ptrtype (const void *ctx,#type)
 	// Allocate into a typed pointer.
    
void * 	talloc_new (const void *ctx)
 	// Allocate a new 0-sized talloc chunk.
    
void * 	talloc_zero (const void *ctx,#type)
 	// Allocate a 0-initizialized structure.
    
void * 	talloc_zero_size (const void *ctx, size_t size)
 	// Allocate untyped, 0-initialized memory.
    
const char * 	talloc_get_name (const void *ptr)
 	// Return the name of a talloc chunk.
    
void * 	talloc_check_name (const void *ptr, const char *name)
 	// Verify that a talloc chunk carries a specified name.
    
void * 	talloc_parent (const void *ptr)
 	// Get the parent chunk of a pointer.
    
const char * 	talloc_parent_name (const void *ptr)
 	// Get a talloc chunk's parent name.
    
size_t 	talloc_total_size (const void *ptr)
 	// Get the total size of a talloc chunk including its children.
    
size_t 	talloc_total_blocks (const void *ptr)
 	// Get the number of talloc chunks hanging off a chunk.
    
void * 	talloc_memdup (const void *t, const void *p, size_t size)
 	// Duplicate a memory area into a talloc chunk.
    
void 	talloc_set_type (const char *ptr,#type)
 	// Assign a type to a talloc chunk.
    
type * 	talloc_get_type (const void *ptr,#type)
 	// Get a typed pointer out of a talloc pointer.
    
void * 	talloc_get_type_abort (const void *ptr,#type)
 	// Safely turn a void pointer into a typed pointer.
    
void * 	talloc_find_parent_byname (const void *ctx, const char *name)
 	// Find a parent context by name.
    
void * 	talloc_find_parent_bytype (const void *ptr,#type)
 	// Find a parent context by type.
    
void * 	talloc_pool (const void *context, size_t size)
 	// Allocate a talloc pool.
    
void * 	talloc_pooled_object (const void *ctx,#type, unsigned num_subobjects, size_t total_subobjects_size)
 	// Allocate a talloc object as/with an additional pool.
```



逆向 API:

```c
// 为指定的 parent 分配一个指定大小的子内存区域, 当未来 parent 释放时, 当前的 allocation 也会被自动释放
void *ta_alloc_size(void *ta_parent, size_t size);

void *ta_zalloc_size(void *ta_parent, size_t size);

void *ta_realloc_size(void *ta_parent, void *ptr, size_t size);

// 释放当前块以及其所有的 child 块
void ta_free(void *ptr);

// 释放当前块所有的 child 块, 但不释放当前块
void ta_free_children(void *ptr);
```



































