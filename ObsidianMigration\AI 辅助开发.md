# AI 辅助开发

> ChatGPT、提示工程与 AI 驱动的开发工具集

## 📋 目录

- [[#ChatGPT 应用]]
- [[#提示工程]]
- [[#AI 工具集成]]
- [[#自动化应用]]

---

## ChatGPT 应用

### 常用魔咒

> 参考：[[ChatGPT 常用魔咒]]

#### 代码生成与优化

**代码生成提示**：
```
请帮我写一个 Python 函数，要求：
1. 功能：[具体功能描述]
2. 输入参数：[参数类型和说明]
3. 返回值：[返回值类型和说明]
4. 异常处理：[需要处理的异常情况]
5. 性能要求：[性能相关要求]

请包含详细的注释和使用示例。
```

**代码优化提示**：
```
请帮我优化以下代码：

[粘贴代码]

优化目标：
- 提高性能
- 增强可读性
- 减少内存使用
- 遵循最佳实践

请解释每个优化点的原因。
```

#### 调试与问题解决

**错误诊断提示**：
```
我遇到了以下错误：

错误信息：[错误信息]
代码片段：[相关代码]
运行环境：[Python版本、操作系统等]
期望行为：[期望的正确行为]

请帮我分析错误原因并提供解决方案。
```

**性能分析提示**：
```
请帮我分析以下代码的性能瓶颈：

[代码]

分析要点：
1. 时间复杂度
2. 空间复杂度
3. 可能的优化点
4. 替代算法建议

请提供具体的优化建议和改进后的代码。
```

#### 架构设计

**系统设计提示**：
```
我需要设计一个 [系统类型] 系统，要求：

功能需求：
- [功能1]
- [功能2]
- [功能3]

非功能需求：
- 性能：[性能要求]
- 可扩展性：[扩展性要求]
- 可靠性：[可靠性要求]

请提供：
1. 系统架构图
2. 技术栈选择
3. 数据库设计
4. API 设计
5. 部署方案
```

---

## 提示工程

### 论文优化提示

> 参考：[[Paper_optimize]]

#### 专业学术论文优化机器人

**角色设定**：
```
你是一个学术论文优化机器人，用户会向你输入论文片段，你需要帮用户按照以下要求进行优化：

1. 保留原本的 Latex 标记
2. 对于输入文本中的英文简写/大写缩写，保留其原本的内容即可，用户可能在上下文中已经进行定义，**不需要在括号中揣测其英文全拼或含义**
3. 确保输出的内容符合美式英语的表达习惯
4. 确保输出的内容表达符合学术论文的表达习惯
5. 专注于 CS/AI/Cybersecurity 领域，你可以额外专注于这些领域的表达习惯
6. 如果有多种推荐的表达方式，请你给出多种修改意见
```

**输出格式要求**：
```
# 修改 1:
### 修改原因:
```
此处列举你修改这个句子的原因，使用简体中文描述
```
### 修改结果:
```
此处填入你修改后的句子。
请注意，你输出的句子单行不应该过长，需要在合适的位置进行换行，以便用户在文本编辑器中查看

\begin{comment}
修改后的句子的中文翻译
\end{comment}
```

# 修改 2: (如果存在)
...
```

#### 实际应用示例

**示例 1 - 技术描述优化**：

用户输入：
```
The fuzzing tool we made uses genetic algorithms to find bugs. It worked really well, finding 15 bugs in the software in just a couple hours. Most of these bugs could lead to crashes.
```

优化输出：
```
# 修改 1:
### 修改原因:
1. 使用了"framework"替代"tool"，"vulnerabilities"替代"bugs"
2. 从非正式表达转为学术语调，如"worked really well"改为"demonstrated significant efficacy"
3. 增加了具体的时间描述"two-hour testing period"
4. 删除了第一人称"we made"，改用"proposed"的被动术语
5. 保持了"vulnerabilities"一词的一致使用，而不是混用"bugs"和"vulnerabilities"

### 修改结果:
The proposed fuzzing framework leverages genetic algorithms to identify
software vulnerabilities. The implementation demonstrated significant efficacy,
detecting 15 distinct vulnerabilities within a two-hour testing period.
The majority of these vulnerabilities exhibited potential for triggering
system failures.

\begin{comment}
所提议的模糊测试框架利用遗传算法来识别软件漏洞。实施结果显示出显著的有效性，
在两个小时的测试期间内检测到15个不同的漏洞。这些漏洞中的大多数具有引发系统故障的潜力。
\end{comment}
```

**示例 2 - AI 模型描述优化**：

用户输入：
```
This new AI model is like, super good at understanding what people say, even if they don't say it perfectly. It's way better than other models we've tried. It can even guess what you mean if you mess up your words.
```

优化输出：
```
# 修改 1:
### 修改原因:
1. 避免使用口语化和非正式的表达方式，如 "like, super good" 和 "way better"
2. 使用更精确的术语来描述模型的能力，如 "natural language understanding" 和 "robustness"
3. 提升语言的严谨性和客观性，避免主观评价，如 "better than other models we've tried"
4. 使用更专业的词汇，比如将"mess up your words"修改为"imperfect utterances"
5. 强调模型的泛化能力和处理真实世界数据的能力

### 修改结果:
This novel AI model demonstrates significant advancement in natural language understanding,
exhibiting robustness to imperfect utterances. Empirical evaluations reveal a notable
improvement in performance compared to existing models across a range of benchmark datasets.
Furthermore, the model exhibits an enhanced ability to infer intended meaning from incomplete
or malformed input, suggesting superior generalization capabilities for real-world applications.

\begin{comment}
这个新型 AI 模型在自然语言理解方面显示出显著的进步，表现出对不完美表达的鲁棒性。
实证评估表明，与现有模型相比，在一系列基准数据集上的性能有了显著提升。此外，该模型展现了
从不完整或格式错误的输入中推断意图含义的增强能力，这表明其在现实世界应用中的优越泛化能力。
\end{comment}
```

### 超级翻译器

> 参考：[[Super_Translator]]

#### 学术翻译专家系统

**角色设定**：
```
作为学术翻译专家，你需要完成从 ${sourceLang} 到 ${targetLang} 的翻译任务，需同时具备文本预处理能力，严格执行以下流程：
文本净化 → 术语映射 → 格式优化 → 精准翻译
```

#### 术语控制中心

**动态术语库**：

1. **排除翻译列表**（优先执行）：
   - 自动识别大小写敏感的专业词汇（如"BERT"不译，"Bert"需译）
   - 支持通配符保留（如"GPT-*"保留所有变体）
   - 示例配置：`["NASA", "COVID-19", "ResNet50", "α-SMA", "Instrument"]`

2. **强制映射词典**（次优先执行）：
   - 精确匹配词汇原型（包含全角字符处理）
   - 支持正则表达式绑定（如"/AI模型/g → AI Model"）
   - 示例配置：
     ```json
     {
       "人工神经网络": "Artificial Neural Network",
       "F1值": "F1-score",
       "交叉验证": "Cross-Validation (CV)"
     }
     ```

#### 预处理模块

**术语保护机制**：

1. **排除列表词汇**：
   - 跳过全角字符转换（如"ＲｅｓＮｅｔ５０"→"ResNet50"）
   - 保留原始大小写（如"transformer"不强制首字母大写）

2. **强制映射检测**：
   - 执行顺序：原文→全角转换→强制替换
   - 支持包含特殊符号的映射（如"α-折叠"→"alpha-fold"）

#### 质量控制

**术语双重验证**：
- 检查排除列表中98%的词汇未被翻译（允许2%误报）
- 验证强制映射覆盖率（统计命中率）

**上下文一致性**：
- 确保同一术语在段落中呈现方式统一
- 检测术语单复数使用是否符合学术规范

#### 翻译示例

**输入**：
```
在ＮＬＰ任务中，我们对比了BERT模型和人工神经网络的F1值，所有实验均通过5折交叉验证完成。
```

**输出**：
```json
{
  "source_text": "在ＮＬＰ任务中...",
  "translated_text": "In NLP tasks, we compared the F1-scores between the BERT model and Artificial Neural Networks, with all experiments completed through 5-fold Cross-Validation.",
  "untranslated_terms": ["BERT", "NLP"],
  "specified_translations": {
    "人工神经网络": "Artificial Neural Network",
    "F1值": "F1-score",
    "交叉验证": "Cross-Validation"
  },
  "translator_notes": [
    "[全角转换] ＮＬＰ → NLP",
    "[术语映射] 交叉验证 → Cross-Validation (CV)"
  ]
}
```

### Gemini 论文优化器

> 参考：[[Gemini_Paper_Optimizer]]

#### 基于大语言模型的学术论文写作优化助手

**角色简介**：
```
你是一款基于大语言模型的学术论文写作优化助手，为用户提供高质量的学术论文[翻译]与[优化]操作。你精通学术论文的写作、Latex 的使用，以及精通[计算机科学]、[人工智能]以及[网络空间安全]领域，同时你还是[简体中文]以及[美式英语]的专家，能够从多角度帮助用户解决各种论文写作问题。
```

#### 处理流程

**五步优化流程**：知识理解 → 写作优化 → 写作翻译 → 优化说明 → 改进建议

1. **知识理解**
   - 充分理解用户输入的句子，用户的输入可能是中文，也可能是英文
   - 当遇到表达不清楚的地方，推测用户可能的含义

2. **写作优化**（美式英语）
   - 根据用户输入的句子以及恰当的理解，将句子翻译为符合美式英语写作习惯的学术论文句子或段落
   - 保留句子中的 Latex 格式以及命令
   - 遇到句子中的英文缩写，其应该已经在上下文中良好定义，不需要翻译或者解释
   - 为了便于阅读，**请在 100 个字符左右的位置进行换行**

3. **写作翻译**（简体中文）
   - 将优化后的英文句子，翻译为简体中文

4. **优化说明**
   - 对于优化的部分，列项进行详细说明
   - 使用简体中文说明
   - 可以额外列出一些优化的词汇或者表达方式

5. **改进建议**
   - 用户的表达或者写作可能存在难以理解的部分
   - 对用户输入的表达从多个角度提供修改意见

#### 个性特点

**专业可靠**：
- 拥有专业的学术论文写作经验，以及 CS 领域知识
- 对待论文写作问题认真负责，且忠实于用户输入的内容，不会随意发挥
- 对于句子中的 Latex 进行保留或者修改

**细致周到**：
- 能够充分考虑当前的不足，提供可靠的建议
- [写作优化]以及[写作翻译]部分输出到 Code 框中

#### 实际应用示例

**用户输入**：
```
If the probability is greater than a pre-defined threshold, which is set to 0.25 in our prototype, HardsHeap stores a given set of heap actions and reduces it using Statistical Significance Delta Debugging (SSDD). To reduce test cases, Delta debugging [51] is one of the most widely used techniques; it re-runs an application with a more reduced case to check whether the same failure can occur. By repeating this procedure, delta debugging can reduce the test case for the same failure. In HardsHeap, the failure corresponds to a security violation in secure allocators, and the test case is the set of heap actions. Unfortunately, classical delta debugging is not directly applicable because it assumes that the failure can be reliably reproducible; however, in evaluating secure allocators, security violations only appear stochastically because of their random mechanisms (e.g., adjacent chunks happen in 30%).
```

**优化输出**：

**1. 知识理解**
用户输入了一段关于 HardsHeap 系统如何使用 Statistical Significance Delta Debugging (SSDD) 来减少堆操作以进行安全漏洞测试的英文段落。这段话解释了为什么传统的 Delta Debugging 方法不适用于这种情况，因为它假设故障是可稳定复现的，而安全分配器中的安全漏洞由于其随机机制（例如，相邻块以 30% 的概率发生）而随机出现。

**2. 写作优化**
```
If the probability exceeds a pre-defined threshold, set at 0.25 in our prototype,
HardsHeap stores a given set of heap actions and reduces it using Statistical
Significance Delta Debugging (SSDD). Delta debugging \cite{} is a widely employed
technique for reducing test cases, which involves re-running an application with a
simplified case to verify whether the same failure occurs. By iteratively applying this
procedure, delta debugging can effectively reduce the test case while preserving the
failure. In HardsHeap, the failure corresponds to a security violation in secure
allocators, and the test case comprises the set of heap actions. However, classical
delta debugging cannot be directly applied because it assumes the reliable reproducibility
of the failure. In the context of evaluating secure allocators, security violations appear
stochastically due to their inherent random mechanisms (e.g., adjacent chunks occur
with a probability of 30%).
```

**3. 写作翻译**
```
如果概率超过预定义的阈值（在我们的原型中设置为 0.25），HardsHeap 将存储一组给定的堆操作，并使用统计显著性 Delta 调试（SSDD）对其进行简化。Delta 调试 \cite{} 是一种广泛使用的减少测试用例的技术，它涉及使用简化的用例重新运行应用程序，以验证是否会发生相同的故障。通过迭代应用此过程，delta 调试可以有效地减少测试用例，同时保留故障。在 HardsHeap 中，故障对应于安全分配器中的安全违规，而测试用例包含一组堆操作。然而，经典 delta 调试不能直接应用，因为它假设故障的可重复性是可靠的。在评估安全分配器的上下文中，由于其固有的随机机制，安全违规是随机出现的（例如，相邻块以 30% 的概率出现）。
```

**4. 优化说明**
- **"is greater than" 改为 "exceeds"**: "exceeds" 在学术写作中更正式，更简洁
- **句式调整**: 将一些句子进行了拆分或者重组，使得逻辑关系更加清晰，更易于阅读
- **"one of the most widely used techniques" 改为 "a widely employed technique"**: 避免使用过于绝对的表达，更符合学术论文的严谨性
- **"can be reliably reproducible" 改为 "assumes the reliable reproducibility of the failure"**: 更加强调了delta debugging 的假设，即 failure 是可以稳定复现的
- **用词优化**: 使用了一些更学术化的词汇，例如 "comprises" 代替 "is"，"inherent" 代替 "random"

**5. 改进建议**
- **关于阈值**: 建议在上下文中更明确地说明阈值 0.25 的含义以及选择的原因
- **关于随机机制**: "adjacent chunks happen in 30%" 这个例子可以更具体地说明
- **关于 SSDD**: 可以更详细地介绍 Statistical Significance Delta Debugging (SSDD) 的原理

---

## AI 工具集成

### OCR 文字识别

> 参考：[[JS OCR Tesseract]]

#### Tesseract.js 集成

```javascript
import Tesseract from 'tesseract.js';

class AITextExtractor {
    constructor() {
        this.worker = null;
        this.isInitialized = false;
    }
    
    async initialize(languages = ['eng', 'chi_sim']) {
        if (this.isInitialized) return;
        
        try {
            this.worker = await Tesseract.createWorker();
            await this.worker.loadLanguage(languages.join('+'));
            await this.worker.initialize(languages.join('+'));
            this.isInitialized = true;
            console.log('OCR Worker initialized successfully');
        } catch (error) {
            console.error('Failed to initialize OCR worker:', error);
            throw error;
        }
    }
    
    async extractText(imageSource, options = {}) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        
        const defaultOptions = {
            logger: m => console.log(m),
            ...options
        };
        
        try {
            const { data: { text, confidence } } = await this.worker.recognize(
                imageSource, 
                defaultOptions
            );
            
            return {
                text: text.trim(),
                confidence: confidence,
                success: true
            };
        } catch (error) {
            console.error('Text extraction failed:', error);
            return {
                text: '',
                confidence: 0,
                success: false,
                error: error.message
            };
        }
    }
    
    async extractStructuredData(imageSource) {
        const result = await this.extractText(imageSource);
        if (!result.success) return result;
        
        // 使用 AI 进行结构化处理
        const structuredData = await this.processWithAI(result.text);
        
        return {
            ...result,
            structuredData
        };
    }
    
    async processWithAI(text) {
        // 这里可以集成 ChatGPT API 进行文本结构化
        const prompt = `
        请将以下OCR识别的文本进行结构化处理：
        
        ${text}
        
        要求：
        1. 纠正OCR错误
        2. 提取关键信息
        3. 按逻辑结构组织
        4. 返回JSON格式
        `;
        
        // 调用 AI API（示例）
        return await this.callAIAPI(prompt);
    }
    
    async callAIAPI(prompt) {
        // AI API 调用实现
        try {
            const response = await fetch('/api/ai/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt })
            });
            
            const result = await response.json();
            return result.data;
        } catch (error) {
            console.error('AI API call failed:', error);
            return null;
        }
    }
    
    async terminate() {
        if (this.worker) {
            await this.worker.terminate();
            this.worker = null;
            this.isInitialized = false;
        }
    }
}

// 使用示例
const textExtractor = new AITextExtractor();

document.getElementById('imageInput').addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
        const result = await textExtractor.extractStructuredData(file);
        
        if (result.success) {
            document.getElementById('extractedText').textContent = result.text;
            document.getElementById('confidence').textContent = `置信度: ${result.confidence}%`;
            
            if (result.structuredData) {
                document.getElementById('structuredData').textContent = 
                    JSON.stringify(result.structuredData, null, 2);
            }
        } else {
            console.error('提取失败:', result.error);
        }
    } catch (error) {
        console.error('处理失败:', error);
    }
});
```

#### Python OCR 集成

```python
import ddddocr
import requests
import json
from PIL import Image
import io

class AITextProcessor:
    def __init__(self):
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        self.ai_api_url = "https://api.openai.com/v1/chat/completions"
        self.ai_api_key = "your-api-key"
    
    def extract_text_from_image(self, image_path_or_bytes):
        """从图像中提取文字"""
        try:
            if isinstance(image_path_or_bytes, str):
                with open(image_path_or_bytes, 'rb') as f:
                    image_bytes = f.read()
            else:
                image_bytes = image_path_or_bytes
            
            text = self.ocr.classification(image_bytes)
            return {
                'success': True,
                'text': text,
                'error': None
            }
        except Exception as e:
            return {
                'success': False,
                'text': '',
                'error': str(e)
            }
    
    def process_with_ai(self, text, task_type="correct_and_structure"):
        """使用 AI 处理文本"""
        prompts = {
            "correct_and_structure": f"""
            请对以下OCR识别的文本进行处理：
            
            {text}
            
            任务：
            1. 纠正OCR识别错误
            2. 整理文本格式
            3. 提取关键信息
            4. 返回结构化的JSON数据
            
            请返回JSON格式的结果。
            """,
            
            "translate": f"""
            请将以下文本翻译成中文，并保持原有格式：
            
            {text}
            """,
            
            "summarize": f"""
            请总结以下文本的主要内容：
            
            {text}
            
            要求：
            1. 提取关键点
            2. 保持逻辑清晰
            3. 控制在200字以内
            """
        }
        
        prompt = prompts.get(task_type, prompts["correct_and_structure"])
        
        try:
            headers = {
                "Authorization": f"Bearer {self.ai_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 1000,
                "temperature": 0.3
            }
            
            response = requests.post(self.ai_api_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            ai_response = result['choices'][0]['message']['content']
            
            return {
                'success': True,
                'result': ai_response,
                'error': None
            }
        except Exception as e:
            return {
                'success': False,
                'result': '',
                'error': str(e)
    
    def process_image_with_ai(self, image_path, task_type="correct_and_structure"):
        """完整的图像文字处理流程"""
        # 1. OCR 提取文字
        ocr_result = self.extract_text_from_image(image_path)
        if not ocr_result['success']:
            return ocr_result
        
        # 2. AI 处理文字
        ai_result = self.process_with_ai(ocr_result['text'], task_type)
        
        return {
            'ocr_text': ocr_result['text'],
            'ai_processed': ai_result['result'] if ai_result['success'] else None,
            'success': ai_result['success'],
            'error': ai_result.get('error')
        }

# 使用示例
processor = AITextProcessor()

# 处理图像
result = processor.process_image_with_ai('document.png', 'correct_and_structure')

if result['success']:
    print("原始OCR文本:")
    print(result['ocr_text'])
    print("\nAI处理后:")
    print(result['ai_processed'])
else:
    print("处理失败:", result['error'])
```

---

## 自动化应用

### 企业微信集成示例

> 参考：[[Tencent_qiyeweixin_Example]]

```python
import requests
import json
import time
from datetime import datetime

class WeChatWorkBot:
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url
    
    def send_text_message(self, content, mentioned_list=None):
        """发送文本消息"""
        data = {
            "msgtype": "text",
            "text": {
                "content": content,
                "mentioned_list": mentioned_list or []
            }
        }
        return self._send_message(data)
    
    def send_markdown_message(self, content):
        """发送 Markdown 消息"""
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        return self._send_message(data)
    
    def send_code_review_notification(self, pr_info):
        """发送代码审查通知"""
        content = f"""
        ## 🔍 代码审查通知
        
        **项目**: {pr_info['project']}
        **分支**: {pr_info['branch']}
        **作者**: {pr_info['author']}
        **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        
        **变更摘要**:
        {pr_info['summary']}
        
        **文件变更**:
        {chr(10).join([f"- {file}" for file in pr_info['files']])}
        
        请相关同事及时进行代码审查。
        """
        
        return self.send_markdown_message(content)
    
    def send_deployment_notification(self, deploy_info):
        """发送部署通知"""
        status_emoji = "✅" if deploy_info['success'] else "❌"
        
        content = f"""
        ## {status_emoji} 部署通知
        
        **环境**: {deploy_info['environment']}
        **版本**: {deploy_info['version']}
        **状态**: {"成功" if deploy_info['success'] else "失败"}
        **时间**: {deploy_info['timestamp']}
        
        **变更内容**:
        {deploy_info['changes']}
        
        {"部署成功，请进行功能验证。" if deploy_info['success'] else f"部署失败，错误信息：{deploy_info.get('error', '未知错误')}"}
        """
        
        return self.send_markdown_message(content)
    
    def _send_message(self, data):
        """发送消息的底层方法"""
        try:
            response = requests.post(
                self.webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()
            
            result = response.json()
            if result.get('errcode') == 0:
                return {'success': True, 'message': '发送成功'}
            else:
                return {'success': False, 'error': result.get('errmsg', '未知错误')}
        
        except Exception as e:
            return {'success': False, 'error': str(e)}

# AI 驱动的自动化工作流
class AIWorkflowAutomation:
    def __init__(self, wechat_bot, ai_processor):
        self.wechat_bot = wechat_bot
        self.ai_processor = ai_processor
    
    def auto_code_review(self, code_diff):
        """AI 自动代码审查"""
        prompt = f"""
        请对以下代码变更进行审查：
        
        {code_diff}
        
        审查要点：
        1. 代码质量
        2. 潜在bug
        3. 性能问题
        4. 安全隐患
        5. 最佳实践
        
        请提供详细的审查意见和改进建议。
        """
        
        ai_result = self.ai_processor.process_with_ai(prompt, "code_review")
        
        if ai_result['success']:
            # 发送审查结果到企业微信
            review_content = f"""
            ## 🤖 AI 代码审查报告
            
            {ai_result['result']}
            
            *此报告由 AI 自动生成，请人工复核*
            """
            
            self.wechat_bot.send_markdown_message(review_content)
        
        return ai_result
    
    def auto_documentation(self, code_content):
        """AI 自动生成文档"""
        prompt = f"""
        请为以下代码生成详细的技术文档：
        
        {code_content}
        
        文档要求：
        1. 功能说明
        2. 参数说明
        3. 返回值说明
        4. 使用示例
        5. 注意事项
        
        请使用 Markdown 格式。
        """
        
        return self.ai_processor.process_with_ai(prompt, "documentation")

# 使用示例
webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key"
wechat_bot = WeChatWorkBot(webhook_url)
ai_processor = AITextProcessor()
automation = AIWorkflowAutomation(wechat_bot, ai_processor)

# 发送部署通知
deploy_info = {
    'environment': 'production',
    'version': 'v1.2.3',
    'success': True,
    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    'changes': '修复了用户登录问题，优化了查询性能'
}

wechat_bot.send_deployment_notification(deploy_info)
```

---

## 🔗 相关链接

- [[编程语言与框架]]
- [[开发工具链]]
- [[学术资源与方法论]]
- [[自动化运维]]

---

*最后更新：2025-06-16*
