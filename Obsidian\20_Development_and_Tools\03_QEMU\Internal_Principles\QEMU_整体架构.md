# QEMU 整体架构

## 概述

<GeminiOptimizationFrom> Note/QEMU/QEMU 基本结构.md </GeminiOptimizationFrom>

QEMU 是一个模拟器，它能够动态模拟特定架构的 CPU 指令，如 X86、PPC、ARM 等等。QEMU 模拟的架构叫目标架构，运行 QEMU 的系统架构叫主机架构。QEMU 中有一个模块叫做微型代码生成器（TCG），它用来将目标代码翻译成主机代码。

## 参考资料

> 详细分析：http://www.serversan.org/thread-46-1-1.html

## 整体架构

![QEMU 架构图](https://qiniu.maikebuke.com/104529gmkg4zmvfe7wp3ep.jpg)

QEMU 的主要功能就是不断提取客户机代码并且转化成主机指定架构的代码。整个翻译任务分为两个部分：
1. **第一部分**：将目标代码（TB）转化成 TCG 中间代码
2. **第二部分**：将中间代码转化成主机代码

## 代码结构

### 1.1 开始执行

主要比较重要的 C 文件有：
- `/vl.c`：QEMU 的 main 函数定义，执行起点
- `/cpus.c`：CPU 相关操作
- `/exec-all.c`：执行相关
- `/exec.c`：内存执行
- `/cpu-exec.c`：CPU 执行循环

**main 函数功能**：
- 建立虚拟硬件环境
- 参数解析
- 初始化内存
- 初始化模拟设备
- 设置 CPU 参数
- 初始化 KVM

### 1.2 硬件模拟

- **位置**：所有硬件设备都在 `/hw/` 目录下
- **设备类型**：总线、串口、网卡、鼠标等
- **初始化**：在 `vl.c` 中的 `machine_init` 中初始化
- **连接方式**：通过设备模块串联在一起

### 1.3 目标机器

**支持的 CPU 架构**：
- Alpha, ARM, Cris, i386, M68K, PPC, Sparc, Mips, MicroBlaze, S390X, SH4

**配置方式**：
- 使用 `./configure` 配置运行架构
- 脚本自动读取本机 CPU 架构
- 编译时编译对应架构代码

**代码组织**：
- `/target-arch/` 目录对应相应架构代码
- 如 `/target-i386/` 对应 x86 系列代码
- 实现将客户机 CPU 架构的 TBs 转化成 TCG 中间代码

### 1.4 主机

**功能**：使用 TCG 代码生成主机代码
**位置**：`/tcg/` 目录
**架构支持**：不同架构在不同子目录，如 i386 在 `/tcg/i386` 中

### 1.5 重要文件总结

| 文件路径 | 功能描述 |
|----------|----------|
| `/vl.c` | 最主要的模拟循环，虚拟机环境初始化，CPU 执行 |
| `/target-arch/translate.c` | 将客户机代码转化成不同架构的 TCG 操作码 |
| `/tcg/tcg.c` | 主要的 TCG 代码 |
| `/tcg/arch/tcg-target.c` | 将 TCG 代码转化生成主机代码 |
| `/cpu-exec.c` | cpu-exec() 函数寻找下一个 TB，操作生成的代码块 |

## TCG - 动态翻译

### 基本概念

QEMU 在 0.9.1 版本之前使用 DynGen 翻译 C 代码。TCG 会动态转换代码，目的是用更多时间去执行生成的代码。

### 缓存机制

- **代码缓存**：新代码从 TB 生成后保存到 cache 中
- **重用机制**：相同的 TB 会被反复操作
- **刷新算法**：cache 刷新使用 LRU 算法

![TCG 翻译流程](https://qiniu.maikebuke.com/0_1312529788lH8f.gif)

### 翻译过程

#### 客户机代码
![客户机代码](https://qiniu.maikebuke.com/0_1312529888kg6p.gif)

#### TCG 中间代码
![TCG 中间代码](https://qiniu.maikebuke.com/0_1310536077x99Y.gif)

#### 主机代码
![主机代码](https://qiniu.maikebuke.com/0_1310535574DDtb.gif)

### 函数调用机制

编译器产生目标代码时会生成特殊的汇编代码：
- **Prologue（前端）**：函数调用前的准备
- **Epilogue（后端）**：函数调用后的恢复

**后端恢复操作**：
1. 恢复堆栈指针（栈顶和基地址）
2. 修改 cs 和 ip，程序回到前端记录点

## TB 链

### 优化目的

从代码 cache 到静态代码再回到代码 cache 的过程比较耗时，TB 链将所有 TB 连在一起，让一个 TB 执行完后直接跳到下一个 TB。

![TB 链结构](https://qiniu.maikebuke.com/0_13105370022275.gif)

## QEMU TCG 代码执行流程

### 主要函数调用链

1. **main_loop(...){/vl.c}**：初始化 qemu_main_loop_start()，进入无限循环 cpu_exec_all()

2. **qemu_main_loop_start(...){/cpus.c}**：设置 qemu_system_ready = 1，重启所有线程

3. **cpu_exec_all(...){/cpus.c}**：CPU 循环，支持 256 个 CPU 核分时运行

4. **cpu_exec(...){/cpu-exec.c}**：主要执行循环，包含两个无限循环：
   - `tb_find_fast()`：查询下一个 TB
   - `tcg_qemu_tb_exec()`：执行生成的主机代码

### 重要数据结构

#### CPUState 结构体
```c
struct CPUState{/target-xyz/cpu.h}
```
CPU 状态结构体，包含 CPU 各种状态信息。

#### TranslationBlock 结构体
```c
struct TranslationBlock {/exec-all.h}
```
包含成员：
- PC, CS_BASE, Flags（表明 TB）
- tc_ptr（指向 TB 翻译代码的指针）
- tb_next_offset[2], tb_jmp_offset[2]（接下来的 TB）
- *jmp_next[2], *jmp_first（之前的 TB）

### TB 查找和生成

1. **tb_find_fast(...){/cpu-exec.c}**：通过哈希函数从 tb_jmp_cache[] 获得 TB 索引

2. **tb_find_slow(...){/cpu-exec.c}**：快速查找失败后访问物理内存寻找 TB

3. **tb_gen_code(...){/exec.c}**：分配新 TB，调用 cpu_gen_code() 和 tb_link_page()

4. **cpu_gen_code(...){translate-all.c}**：初始化代码生成

5. **disas_insn(){/target-arch/translate.c}**：将客户机代码翻译成 TCG 代码

6. **tcg_gen_code(...){/tcg/tcg.c}**：将 TCG 代码转化成主机代码

## 现代 QEMU 执行流程

### 指令执行调用栈

**指令执行位置**：`accel/tcg/cpu-exec.c:cpu_tb_exec->tcg_qemu_tb_exec`

```
cpu_tb_exec(CPUState * cpu, TranslationBlock * itb, int * tb_exit)
cpu_loop_exec_tb(CPUState * cpu, TranslationBlock * tb, vaddr pc, TranslationBlock ** last_tb, int * tb_exit)
cpu_exec_loop(CPUState * cpu, SyncClocks * sc)
cpu_exec_setjmp(CPUState * cpu, SyncClocks * sc)
cpu_exec(CPUState * cpu)
tcg_cpus_exec(CPUState * cpu)
mttcg_cpu_thread_fn(void * arg)
```

**指令翻译位置**：`accel/tcg/translator.c:translator_loop->translate_insn`

```
translator_loop(CPUState * cpu, TranslationBlock * tb, int * max_insns, vaddr pc, void * host_pc, const TranslatorOps * ops, DisasContextBase * db)
gen_intermediate_code(CPUState * cpu, TranslationBlock * tb, int * max_insns, target_ulong pc, void * host_pc)
setjmp_gen_code(CPUArchState * env, TranslationBlock * tb, vaddr pc, void * host_pc, int * max_insns, int64_t * ti)
tb_gen_code(CPUState * cpu, vaddr pc, uint64_t cs_base, uint32_t flags, int cflags)
```

## QEMU 与 KVM 交互

### IOCTL 通信

用户空间的 QEMU 通过 IOCTL 与内核空间的 KVM 模块通信。

### 关键 IOCTL

1. **创建 KVM**：在 `/vl.c` 中通过 `kvm_init()` 创建 KVM 结构体

2. **KVM_RUN**：最频繁使用的 IOCTL，KVM 主循环不断执行

3. **KVM_IRQ_LINE**：中断注入入口，处理设备中断

### 初始化流程

```
pc_init1 -> pc_cpus_init -> pc_new_cpu -> cpu_x86_init -> qemu_init_vcpu -> kvm_init_vcpu -> ap_main_loop -> kvm_main_loop_cpu -> kvm_cpu_exec -> kvm_run
```

## 相关主题

- [[QEMU_Monitor]] - QEMU 监控接口
- [[QEMU_插件机制]] - QEMU 插件系统
- [[QEMU_用户态内存]] - QEMU 内存管理
- [[QEMU_常用命令]] - QEMU 使用指南
