# 二进制安全与逆向工程

> 涵盖 CTF PWN、模糊测试、动态分析等二进制安全技术

## 📋 目录

- [[#CTF PWN 技术]]
- [[#模糊测试技术]]
- [[#动态分析工具]]
- [[#调试与分析]]
- [[#汇编与底层]]

---

## CTF PWN 技术

### 堆漏洞利用基础

#### libc malloc 机制

> 参考：[[libc malloc 详解]]

**核心数据结构**：

```c
struct malloc_chunk {
    INTERNAL_SIZE_T mchunk_prev_size;  /* Size of previous chunk (if free) */
    INTERNAL_SIZE_T mchunk_size;       /* Size in bytes, including overhead */
    struct malloc_chunk* fd;           /* double links -- used only if free */
    struct malloc_chunk* bk;
    struct malloc_chunk* fd_nextsize;  /* double links -- used only if free */
    struct malloc_chunk* bk_nextsize;
};
```

**重要概念**：

| 变量/宏/函数 | 描述 | 示例 |
|-------------|------|------|
| victim | 被选择的 Chunk | `victim = av->top;` |
| main_arena | 主分配区域 | `static struct malloc_state main_arena;` |
| ar_ptr | 指向 malloc_state 的指针 | `mstate ar_ptr = &main_arena;` |
| av | arena vector，当前分配区域 | `struct malloc_state * av;` |

**Fast Bins 机制**：

```c
typedef struct malloc_chunk *mfastbinptr;
#define fastbin(ar_ptr, idx) ((ar_ptr)->fastbinsY[idx])
#define fastbin_index(sz) \
  ((((unsigned int) (sz)) >> (SIZE_SZ == 8 ? 4 : 3)) - 2)
#define MAX_FAST_SIZE (80 * SIZE_SZ / 4)
#define NFASTBINS (fastbin_index (request2size (MAX_FAST_SIZE)) + 1)
```

#### 堆漏洞利用方法

> 参考：[[堆漏洞利用方法总结]]

**Fastbin Attack**：
- 利用 fastbin 的单链表结构
- 通过 double free 或 use after free 实现
- 目标：控制 fastbin 链表，实现任意地址分配

**ptmalloc bins 机制**：
- **Fast bins**：小块内存快速分配
- **Small bins**：中等大小内存管理
- **Large bins**：大块内存分配
- **Unsorted bin**：临时存储释放的 chunk

### 部署与环境配置

> 参考：[[CTF PWN 环境部署]]

**patchelf 工具使用**：
```bash
# 将所有依赖库导出
patchelf --set-interpreter /path/to/ld.so ./binary
patchelf --set-rpath /path/to/libs ./binary
```

---

## 模糊测试技术

### AFL 工具链

> 参考：[[AFL 使用说明]]、[[AFL 开发踩坑说明]]

#### 基本使用流程

**编译目标程序**：
```bash
# 使用 AFL 编译器
export CC=afl-gcc
export CXX=afl-g++
./configure
make
```

**启动模糊测试**：
```bash
# 创建输入输出目录
mkdir input output
echo "test" > input/seed

# 开始模糊测试
afl-fuzz -i input -o output ./target @@
```

#### 输入输出文件格式

> 参考：[[AFL 输入输出文件格式]]

**输入文件要求**：
- 文件大小适中（通常 < 1KB）
- 包含程序的有效输入格式
- 覆盖程序的主要执行路径

**输出目录结构**：
```
output/
├── crashes/        # 导致崩溃的输入
├── hangs/          # 导致超时的输入
├── queue/          # 测试用例队列
└── fuzzer_stats    # 统计信息
```

#### 开发注意事项

**常见问题**：
1. **编译问题**：确保使用正确的编译器
2. **权限问题**：AFL 需要特定的系统配置
3. **性能优化**：合理设置内存限制和超时时间

---

## 动态分析工具

### Intel Pin

> 参考：[[Intel Pin 使用指南]]

**Pin 工具特点**：
- 动态二进制插桩框架
- 支持 x86/x64 架构
- 提供丰富的 API 接口

**基本使用**：
```bash
# 使用 Pin 工具分析程序
pin -t pintool.so -- ./target_program
```

### PANDA-re

> 参考：[[PANDA-re 动态分析]]

**PANDA 特性**：
- 基于 QEMU 的全系统分析平台
- 支持录制和重放
- 提供丰富的分析插件

**使用场景**：
- 恶意软件分析
- 漏洞研究
- 系统行为分析

---

## 调试与分析

### GDB 调试技巧

> 参考：[[GDB 命令速查]]

**常用命令**：
```bash
# 附加到进程
gdb -ex "set confirm off" attach $(pidof target)

# 查看内存映射
info proc mappings

# 设置断点
break *0x400000
break function_name

# 查看寄存器
info registers
```

### 系统调用追踪

**Strace 使用**：
```bash
# 追踪系统调用
strace -f -o trace.log ./program

# 只追踪特定系统调用
strace -e trace=open,read,write ./program
```

**Python-Strace**：
```python
import subprocess
import os

# 使用 Python 控制 strace
proc = subprocess.Popen(['strace', '-f', './target'], 
                       stdout=subprocess.PIPE, 
                       stderr=subprocess.PIPE)
```

---

## 汇编与底层

### 汇编代码分析

> 参考：[[汇编 JMP 指令]]

**JMP 指令类型**：
- **直接跳转**：`jmp label`
- **间接跳转**：`jmp *%rax`
- **条件跳转**：`je`, `jne`, `jg` 等

### 反汇编工具

**objdump 使用**：
```bash
# 导出汇编代码
objdump -S ./binary > assembly.s

# 查看特定段
objdump -s -j .text ./binary
```

**ELF 文件格式**：
> 参考：[[ELF 文件段格式]]

- **.text**：代码段
- **.data**：已初始化数据
- **.bss**：未初始化数据
- **.rodata**：只读数据

---

## 🔗 相关链接

- [[编译器技术与程序分析]]
- [[系统虚拟化与仿真]]
- [[调试工具集合]]
- [[WAJI 项目]]

---

*最后更新：2025-06-16*
