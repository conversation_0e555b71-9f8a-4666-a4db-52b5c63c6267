# 📚 知识库总览

> 重新整理的技术笔记知识库，采用 Obsidian 语法组织

## 🎯 核心研究领域

### [[二进制安全与逆向工程]]
- **CTF PWN 技术**：堆漏洞利用、内存管理机制
- **模糊测试**：AFL 工具链与使用技巧
- **动态分析**：Intel Pin、PANDA-re、调试技术
- **静态分析**：反汇编、代码分析工具

### [[编译器技术与程序分析]]
- **LLVM 生态系统**：Pass 开发、IR 分析
- **Clang 工具链**：AST 导出、静态分析
- **编译原理**：构建系统、Makefile

### [[系统虚拟化与仿真]]
- **QEMU 深度应用**：插桩、内存管理、快照
- **容器技术**：Docker 实践与配置
- **系统调试**：PTrace、系统调用追踪

## 🛠️ 开发技术栈

### [[编程语言与框架]]
- **Python 生态**：Web 开发、数据处理、自动化
- **机器学习**：PyTorch 深度学习框架
- **Web 技术**：JavaScript、HTML/CSS

### [[系统管理与运维]]
- **Linux 系统管理**：命令行技巧、系统配置
- **网络与安全**：证书管理、代理配置
- **包管理**：NixOS、包管理器使用

### [[开发工具链]]
- **版本控制**：Git 高级用法、Git LFS
- **数据库**：Neo4j、SQLite
- **构建工具**：编译器配置、交叉编译

## 📚 实用工具与技巧

### [[命令行工具与脚本]]
- **Linux 命令速查**：系统管理、文件操作
- **Shell 脚本**：自动化任务、批处理
- **文本处理**：正则表达式、数据提取

### [[网络与安全工具]]
- **网络分析**：抓包工具、代理配置
- **安全工具**：渗透测试、漏洞分析
- **证书管理**：SSL/TLS 配置

### [[AI 辅助开发]]
- **ChatGPT 应用**：提示工程、代码生成
- **自动化工具**：OCR、图像处理
- **效率提升**：AI 辅助编程技巧

### [[开发工具详细指南]]
- **多媒体工具**：FFmpeg、Aria2 下载
- **逆向工程**：IDA Pro、反汇编分析
- **数据可视化**：Matplotlib、图表制作

### [[Python 详细指南]]
- **核心语言特性**：字节码、内置函数
- **数据处理**：16进制、Ctypes 系统调用
- **网络编程**：urllib3 高级用法

### [[终端工具与自动化]]
- **终端多路复用**：Tmux 详细配置
- **Web 自动化**：Selenium、验证码识别
- **移动端自动化**：ADB、Windows 自动化

### [[数据库与网络工具]]
- **图数据库**：Neo4j 详细使用
- **关系型数据库**：SQLite3 高级应用
- **证书管理**：ACME 自动化证书

### [[Java 与构建系统]]
- **Java 开发环境**：OpenJDK 版本管理
- **构建系统**：Maven、Gradle、Makefile
- **编译工具链**：GCC 高级使用

## 🔬 研究项目与学术

### [[WAJI 项目]]
- **项目概述**：自定义堆分配器识别与利用
- **技术实现**：二进制分析、漏洞利用自动化
- **研究方法**：动态分析、符号执行

### [[学术资源与方法论]]
- **论文写作**：学术规范、术语整理
- **研究工具**：文献管理、实验设计
- **知识管理**：笔记方法、思维导图

---

## 🔗 快速导航

### 按技术栈浏览
- [[Python 技术栈]]
- [[C/C++ 开发]]
- [[Web 开发]]
- [[系统编程]]

### 按应用场景浏览
- [[安全研究]]
- [[性能优化]]
- [[自动化脚本]]
- [[学术研究]]

### 工具索引
- [[工具索引]]
- [[调试工具集合]]
- [[开发环境配置]]
- [[常用命令速查]]

---

*最后更新：2025-06-16*
*知识库版本：v2.0*
