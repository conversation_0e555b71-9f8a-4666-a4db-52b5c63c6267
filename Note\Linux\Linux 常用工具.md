# Linux 常用工具安装

---

## 1. Python

```shell
cd ~/Downloads/
curl -O https://www.python.org/ftp/python/3.7.12/Python-3.7.12.tgz
tar -xf Python-3.7.12.tgz
cd Python-3.7.12
sudo apt-get install pkg-config
sudo apt-get install -y build-essential gdb lcov pkg-config libbz2-dev libffi-dev libgdbm-dev libgdbm-compat-dev liblzma-dev libncurses5-dev libreadline6-dev libsqlite3-dev libssl-dev lzma lzma-dev tk-dev uuid-dev zlib1g-dev
PYTHON_HOME=${HOME}/.local/python/3.7/
mkdir -p ${PYTHON_HOME}
./configure --enable-optimizations --prefix=${PYTHON_HOME}
make
make install
cd ~
```



## 2. 开发环境

### 2.1 64 位开发环境

```shell
# 常用工具
sudo apt-get install zsh curl wget git flex bison 
# 开发dev
sudo apt-get install build-essential ninja-build libglib2.0-dev libpixman-1-dev 
```

### 2.2 32 位开发环境

```shell
sudo dpkg --add-architecture i386
sudo apt-get install libc6-dev-i386
sudo apt-get install gcc-multilib g++-multilib
```

### 2.3 SSH 密钥设置

```shell
# 生成4096位的 ssh 密钥
ssh-keygen -t rsa -C "<邮箱>" -b 4096
```



## 3. 终端 oh-my-zsh

```shell
sudo apt-get install -y curl git
cd ~/Downloads
ZSH_INSTALL_SCRIPT="oh-my-zsh-install.sh"
curl -o ${ZSH_INSTALL_SCRIPT} https://gitee.com/mirrors/oh-my-zsh/raw/master/tools/install.sh
sed -i "s/REPO:-ohmyzsh\/ohmyzsh/REPO:-mirrors\/oh-my-zsh/g" ${ZSH_INSTALL_SCRIPT}
sed -i "s/REMOTE:-https:\/\/github.com\/\${REPO}.git/REMOTE:-https:\/\/gitee.com\/\${REPO}.git/g" ${ZSH_INSTALL_SCRIPT}
sed -i "s/read -r opt/opt=\"Y\"/g" ${ZSH_INSTALL_SCRIPT}
chmod +x ${ZSH_INSTALL_SCRIPT}
./${ZSH_INSTALL_SCRIPT}
```

切换为 `ys` 主题

```shell
cd ~
sed -ri "s/ZSH_THEME=\".+\"/ZSH_THEME=\"ys\"/g" .zshrc
source ~/.zshrc
echo Finish
```



## Samba 服务器设置

1. 安装samba
`sudo apt-get install samba`

2. 查看samba版本
`sudo samba -V`

3. 修改配置文件
```shell
cd /etc/samba
sudo cp smb.conf smb.conf.bak
```

在结尾添加以下内容:
```config
[lizhenghao]
   comment = lizhenghao@************* home 
   path = /home/<USER>/
   public = yes 
   browseable = yes 
   public = yes 
   writeable = yes 
   read only = no
   valid users = lizhenghao
   create mask = 0755
   directory mask = 0755 
   available = yes 
```

4. 创建 samba 用户
`sudo smbpasswd -a lizhenghao`

5. 重载与重启samba服务
```shell
sudo service smbd reload
sudo service smbd restart
```





### VS Code Linux x86

---

`https://code.visualstudio.com/updates/v1_35`





### sshd 配置额外的端口

---

```configure
# /etc/ssh/sshd_config.d/99_frp_ssh_proxy.conf 
Port 2222

Match LocalPort 2222
        PubkeyAuthentication yes
        PasswordAuthentication no
```





