# Install Docker

---





```shell
sudo apt update
sudo apt upgrade -y
# 安装 Docker 依赖
sudo apt install apt-transport-https ca-certificates curl software-properties-common -y
# 添加 Docker 的 GPG 密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
# 添加 Docker 的 APT 仓库源
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
# 更新仓库列表
sudo apt update
# 安装 Docker 引擎
sudo apt install docker-ce docker-ce-cli containerd.io -y
# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker
# 验证 Docker 版本
docker --version
# 将用户添加到 Docker 用户组
sudo usermod -aG docker $USER
newgrp docker


# 配置 Docker 的 HTTP 代理
sudo mkdir -p /etc/systemd/system/docker.service.d
sudo nano /etc/systemd/system/docker.service.d/http-proxy.conf
echo "[Service]
Environment=\"HTTP_PROXY=$HTTP_PROXY\"
Environment=\"HTTPS_PROXY=$HTTPS_PROXY\"
Environment=\"NO_PROXY=localhost,127.0.0.1,.your_local_domain\"" | sudo tee /etc/systemd/system/docker.service.d/http-proxy.conf > /dev/null
# 重新加载 systemd 配置并重启 Docker 服务
sudo systemctl daemon-reload
sudo systemctl restart docker


# 测试 Docker 功能
docker run hello-world

# 安装 Nvidia-Docker Toolkit
# https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/latest/install-guide.html
# sudo docker run --rm --runtime=nvidia --gpus all ubuntu nvidia-smi
```

