# 导出 ELF 的所有依赖库

---



> 使用: patch_target_library.sh `which gdbserver` GDBServerLocalLibrary

```shell
#!/bin/sh

# set -ex       # 调试时开启此选项
set -e

# Step 0: 检查 patchelf 命令是否存在
if command -v patchelf >/dev/null 2>&1; then
    echo "patchelf 命令存在"
else
    echo "patchelf 命令不存在, 请安装 patchelf"
    exit 1
fi

# Step 1: 检查输入参数是否合法
TARGET_DIR="${2}"               # "GDBServer"
TARGET_ELF_ABS_PATH="${1}"      # `which gdbserver`
if [ -z ${TARGET_ELF_ABS_PATH} ] || [ -z ${TARGET_DIR} ]; then
    echo "Error: 请提供两个参数:  ${0} <dynamic_elf_file> <dump_dir>"
    echo "${0} `which gdbserver` GDBServerLocalLibrary"
    exit 1
fi

# Step 2: 检查 目标二进制文件 是否存在
if [ -e "${TARGET_ELF_ABS_PATH}" ]; then
    echo "文件 ${TARGET_ELF_ABS_PATH} 存在"
else
    echo "文件 ${TARGET_ELF_ABS_PATH} 不存在, 请在参数中输入 ELF 文件绝对路径. "
    exit 1
fi

# Step 3: 检查目录是否存在, 如果存在则删除; 不存在会创建新的. 
if [ -d "$TARGET_DIR" ]; then
    echo "目录 $TARGET_DIR 存在, 自动删除目标分析目录"
    rm -r $TARGET_DIR
else
    echo "目录 $TARGET_DIR 不存在, 将会创建目录"
fi
mkdir $TARGET_DIR

# Step 4: 拷贝目标二进制文件到输出目录中
cd $TARGET_DIR
cp ${TARGET_ELF_ABS_PATH} ./

# Step 5: 通过 ldd 命令, 获取动态库以及链接器信息
mkdir -p ./libs
LIBS=$(ldd $TARGET_ELF_ABS_PATH | awk '{if (match($3, "/")) { print $3 }}')
LD_LINUX=$(ldd $TARGET_ELF_ABS_PATH | grep 'ld-linux' | awk '{print $1}')

# Step 6: 将所有的依赖文件拷贝到 libs 目录中, 并且将 rpath 设置为本地目录
cp $LIBS $LD_LINUX ./libs
patchelf --set-rpath ./libs ./$(basename $TARGET_ELF_ABS_PATH)
patchelf --set-interpreter ./libs/$(basename $LD_LINUX) ./$(basename $TARGET_ELF_ABS_PATH)

# Step 7: 检查目标 ELF 文件的动态链接是否正确
ldd ./$(basename $TARGET_ELF_ABS_PATH)
```





