# 系统虚拟化与仿真

> QEMU 深度应用、容器技术与系统级调试

## 📋 目录

- [[#QEMU 核心技术]]
- [[#容器技术]]
- [[#系统调试技术]]
- [[#虚拟化应用]]

---

## QEMU 核心技术

### QEMU 基础架构

> 参考：[[QEMU 基本结构]]、[[QEMU 文档]]

**QEMU 特性**：
- **全系统仿真**：完整的硬件虚拟化
- **用户模式仿真**：单个程序的跨架构执行
- **动态翻译**：二进制代码的实时翻译
- **设备模拟**：各种硬件设备的软件实现

**核心组件**：
- **TCG**（Tiny Code Generator）：动态二进制翻译引擎
- **Memory Management**：内存管理子系统
- **Device Model**：设备模拟框架
- **Monitor Interface**：运行时控制接口

### QEMU 常用命令

> 参考：[[QEMU 常用命令]]

**基本启动**：
```bash
# 启动虚拟机
qemu-system-x86_64 -m 2G -hda disk.img

# 启用 KVM 加速
qemu-system-x86_64 -enable-kvm -m 2G -hda disk.img

# 网络配置
qemu-system-x86_64 -netdev user,id=net0 -device e1000,netdev=net0
```

**高级选项**：
```bash
# 启用 VNC 服务
qemu-system-x86_64 -vnc :1 -hda disk.img

# 设置 CPU 类型和核心数
qemu-system-x86_64 -cpu host -smp 4

# 内存配置
qemu-system-x86_64 -m 4G -object memory-backend-ram,id=ram1,size=4G
```

### QEMU Monitor 接口

> 参考：[[QEMU Monitor]]

**Monitor 命令**：
```bash
# 进入 Monitor 模式
(qemu) help

# 查看虚拟机状态
(qemu) info status
(qemu) info registers
(qemu) info memory

# 内存操作
(qemu) x/10i $pc          # 查看当前指令
(qemu) x/16xb 0x400000    # 查看内存内容

# 断点设置
(qemu) breakpoint_insert 0x400000
(qemu) continue
```

### 快照与状态管理

> 参考：[[QEMU 快照 loadvm]]

**快照操作**：
```bash
# 创建快照
(qemu) savevm snapshot_name

# 加载快照
(qemu) loadvm snapshot_name

# 查看快照列表
(qemu) info snapshots

# 删除快照
(qemu) delvm snapshot_name
```

**外部快照**：
```bash
# 创建外部快照
qemu-img create -f qcow2 -b base.img snapshot.img

# 使用快照启动
qemu-system-x86_64 -hda snapshot.img
```

### QEMU 插桩技术

> 参考：[[QEMU Plugin 插桩原理]]

#### 插桩原理详解

**QEMU 插桩流程**：

1. **指令翻译阶段**：将目标架构指令翻译为 QEMU TCG
   - `accel/tcg/translator.c:translator_loop` 函数处理翻译
   - 包含关键插件函数：`plugin_gen_tb_start`、`plugin_gen_insn_start`
   - 配对函数：`plugin_gen_insn_end`、`plugin_gen_tb_end`

2. **插桩注册机制**：
   - **基本块翻译时插桩**：`qemu_plugin_register_vcpu_tb_trans_cb`
   - **基本块执行前插桩**：`qemu_plugin_register_vcpu_tb_exec_cb`

**核心数据结构**：
```c
struct qemu_plugin_insn {
    GByteArray *data;
    uint64_t vaddr;
    void *haddr;
    GArray *cbs[PLUGIN_N_CB_TYPES][PLUGIN_N_CB_SUBTYPES];
    bool calls_helpers;
    bool mem_helper;
    bool mem_only;
};
```

#### 插件开发实例

**基础插件模板**：
```c
#include <qemu-plugin.h>

static void vcpu_insn_exec_before(unsigned int cpu_index, void *udata)
{
    g_autoptr(GString) out = g_string_new("");
    g_string_printf(out, "CPU %d executed instruction at 0x%lx\n",
                   cpu_index, (unsigned long)udata);
    qemu_plugin_outs(out->str);
}

static void vcpu_tb_trans(qemu_plugin_id_t id, struct qemu_plugin_tb *tb)
{
    size_t n = qemu_plugin_tb_n_insns(tb);
    uint64_t vaddr = qemu_plugin_tb_vaddr(tb);

    g_autoptr(GString) out = g_string_new("");
    g_string_printf(out, "Translating TB at 0x%lx with %zu instructions\n",
                   vaddr, n);
    qemu_plugin_outs(out->str);

    for (size_t i = 0; i < n; i++) {
        struct qemu_plugin_insn *insn = qemu_plugin_tb_get_insn(tb, i);
        uint64_t insn_vaddr = qemu_plugin_insn_vaddr(insn);

        qemu_plugin_register_vcpu_insn_exec_cb(insn, vcpu_insn_exec_before,
                                               QEMU_PLUGIN_CB_NO_REGS,
                                               (void*)insn_vaddr);
    }
}

QEMU_PLUGIN_EXPORT int qemu_plugin_install(qemu_plugin_id_t id,
                                            const qemu_info_t *info,
                                            int argc, char **argv)
{
    qemu_plugin_register_vcpu_tb_trans_cb(id, vcpu_tb_trans);
    return 0;
}
```

**内存访问追踪插件**：
```c
#include <qemu-plugin.h>

static void vcpu_mem_access(unsigned int cpu_index, qemu_plugin_meminfo_t info,
                           uint64_t vaddr, void *udata)
{
    bool is_store = qemu_plugin_mem_is_store(info);
    bool is_load = !is_store;
    uint32_t size = qemu_plugin_mem_size_shift(info);

    g_autoptr(GString) out = g_string_new("");
    g_string_printf(out, "CPU %d: %s at 0x%lx, size: %d bytes\n",
                   cpu_index, is_store ? "STORE" : "LOAD", vaddr, 1 << size);
    qemu_plugin_outs(out->str);
}

static void vcpu_tb_trans_mem(qemu_plugin_id_t id, struct qemu_plugin_tb *tb)
{
    size_t n = qemu_plugin_tb_n_insns(tb);

    for (size_t i = 0; i < n; i++) {
        struct qemu_plugin_insn *insn = qemu_plugin_tb_get_insn(tb, i);

        qemu_plugin_register_vcpu_mem_cb(insn, vcpu_mem_access,
                                        QEMU_PLUGIN_CB_NO_REGS,
                                        QEMU_PLUGIN_MEM_RW, NULL);
    }
}

QEMU_PLUGIN_EXPORT int qemu_plugin_install(qemu_plugin_id_t id,
                                            const qemu_info_t *info,
                                            int argc, char **argv)
{
    qemu_plugin_register_vcpu_tb_trans_cb(id, vcpu_tb_trans_mem);
    return 0;
}
```

**插件编译和使用**：
```bash
# 编译插件
gcc -shared -fPIC -I/usr/include/qemu-plugin -o trace_plugin.so trace_plugin.c

# 使用插件
qemu-system-x86_64 -plugin ./trace_plugin.so -hda disk.img

# 用户模式仿真
qemu-x86_64 -plugin ./trace_plugin.so ./target_program
```

#### 调用栈分析

**动态调用栈**：
```
libins.so!vcpu_insn_exec_before(unsigned int cpu_index, void * udata)
code_gen_buffer (JIT生成的代码)
cpu_tb_exec(CPUState * cpu, TranslationBlock * itb, int * tb_exit)
cpu_loop_exec_tb(CPUState * cpu, TranslationBlock * tb, vaddr pc, ...)
cpu_exec_loop(CPUState * cpu, SyncClocks * sc)
cpu_exec_setjmp(CPUState * cpu, SyncClocks * sc)
cpu_exec(CPUState * cpu)
tcg_cpus_exec(CPUState * cpu)
rr_cpu_thread_fn(void * arg)
qemu_thread_start(void * args)
```

**关键函数说明**：
- `plugin_gen_insn_start`：在指令翻译开始时调用
- `plugin_register_dyn_cb__udata`：注册动态回调函数
- `cpu_tb_exec`：执行翻译块时调用插件函数

### 内存管理

> 参考：[[QEMU User Memory]]

**内存映射**：
```c
// QEMU 内存区域定义
typedef struct MemoryRegion {
    Object parent_obj;
    bool romd_mode;
    bool ram;
    bool subpage;
    bool readonly;
    bool nonvolatile;
    bool rom_device;
    bool flush_coalesced_mmio;
    uint8_t dirty_log_mask;
    bool is_iommu;
    RAMBlock *ram_block;
    Object *owner;
    const MemoryRegionOps *ops;
    void *opaque;
    MemoryRegion *container;
    Int128 size;
    hwaddr addr;
    void (*destructor)(MemoryRegion *mr);
    uint64_t align;
    bool terminates;
    bool ram_device;
    bool enabled;
    bool warning_printed;
    uint8_t vga_logging_count;
    MemoryRegion *alias;
    hwaddr alias_offset;
    int32_t priority;
    QTAILQ_HEAD(, MemoryRegion) subregions;
    QTAILQ_ENTRY(MemoryRegion) subregions_link;
    QTAILQ_HEAD(, CoalescedMemoryRange) coalesced;
    const char *name;
    unsigned ioeventfd_nb;
    MemoryRegionIoeventfd *ioeventfds;
} MemoryRegion;
```

### 网络块设备

> 参考：[[QEMU NBD 网络块设备]]

**NBD 服务器**：
```bash
# 启动 NBD 服务器
qemu-nbd -t -p 10809 disk.img

# 客户端连接
nbd-client localhost 10809 /dev/nbd0

# 挂载设备
mount /dev/nbd0 /mnt/nbd
```

### 时间控制

> 参考：[[QEMU 时间流速控制]]

#### 时间系统原理

**QEMU 时间类型**：
QEMU 虚拟机中的所有时间都通过 `int64_t qemu_clock_get_ns(QEMUClockType type)` 函数实现：

- `QEMU_CLOCK_REALTIME`：真实时间
- `QEMU_CLOCK_VIRTUAL`：虚拟时间
- `QEMU_CLOCK_HOST`：主机时间
- `QEMU_CLOCK_VIRTUAL_RT`：虚拟实时时间

#### 时间缩放实现

**基本原理**：
时间缩放通过在虚拟机启动时保存基准时间 `base_time`，然后在获取时间时通过当前时间与基准时间的差值进行缩放。

**实现方式一**：
```c
// 全局变量
struct QEMUTimeScaline {
    int64_t realtime;
    int64_t virtual;
    int64_t host;
    int64_t virtual_rt;
} base_time;

int64_t qemu_clock_get_ns(QEMUClockType type)
{
    int64_t clock, base, delta;
    switch (type) {
    case QEMU_CLOCK_REALTIME:
        clock = get_clock();
        if (unlikely(clock < base_time.realtime || !base_time.realtime)) {
            base_time.realtime = clock;
        }
        base = base_time.realtime;
        break;
    default:
    case QEMU_CLOCK_VIRTUAL:
        clock = cpus_get_virtual_clock();
        if (unlikely(clock < base_time.virtual || !base_time.virtual)) {
            base_time.virtual = clock;
        }
        base = base_time.virtual;
        break;
    case QEMU_CLOCK_HOST:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_HOST, get_clock_realtime());
        if (unlikely(clock < base_time.host || !base_time.host)) {
            base_time.host = clock;
        }
        base = base_time.host;
        break;
    case QEMU_CLOCK_VIRTUAL_RT:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_VIRTUAL_RT, cpu_get_clock());
        if (unlikely(clock < base_time.virtual_rt || !base_time.virtual_rt)) {
            base_time.virtual_rt = clock;
        }
        base = base_time.virtual_rt;
        break;
    }
    delta = clock - base;
    return base + (delta >> 3);  // 8倍减速
}
```

**简化实现**：
```c
int64_t base_time[4];

int64_t qemu_clock_get_ns(QEMUClockType type)
{
    int64_t clock, base, delta;
    switch (type) {
    case QEMU_CLOCK_REALTIME:
        clock = get_clock();
        break;
    default:
    case QEMU_CLOCK_VIRTUAL:
        clock = cpus_get_virtual_clock();
        break;
    case QEMU_CLOCK_HOST:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_HOST, get_clock_realtime());
        break;
    case QEMU_CLOCK_VIRTUAL_RT:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_VIRTUAL_RT, cpu_get_clock());
        break;
    }
    if (unlikely(clock < base_time[type] || !base_time[type])) {
        base_time[type] = clock;
    }
    base = base_time[type];
    delta = clock - base;
    return base + (delta >> 3);  // 8倍减速
}
```

**使用方法**：
```bash
# 设置时间倍率（icount 模式）
qemu-system-x86_64 -icount shift=3 -hda disk.img

# Monitor 命令控制
(qemu) stop    # 暂停时间
(qemu) cont    # 继续时间
(qemu) info cpus  # 查看 CPU 状态

# 设置时钟倍率
(qemu) clock_set_speed virtual 0.5  # 设置虚拟时钟为一半速度
```

**注意事项**：
- 该实现存在 bug，不能应对重复加载快照的情况
- 需要考虑时间回退的处理
- 在多核环境下需要同步处理

---

## 容器技术

### Docker 基础

> 参考：[[Docker 基础命令]]、[[Docker 安装]]

**基本操作**：
```bash
# 拉取镜像
docker pull ubuntu:20.04

# 运行容器
docker run -it ubuntu:20.04 /bin/bash

# 查看容器
docker ps -a

# 停止容器
docker stop container_id

# 删除容器
docker rm container_id
```

**镜像管理**：
```bash
# 构建镜像
docker build -t myapp:latest .

# 查看镜像
docker images

# 删除镜像
docker rmi image_id

# 导出/导入镜像
docker save myapp:latest > myapp.tar
docker load < myapp.tar
```

### Docker 网络配置

> 参考：[[Docker Proxy]]

**代理配置**：
```json
{
  "proxies": {
    "default": {
      "httpProxy": "http://proxy.example.com:8080",
      "httpsProxy": "http://proxy.example.com:8080",
      "noProxy": "localhost,127.0.0.1"
    }
  }
}
```

**网络管理**：
```bash
# 创建网络
docker network create mynetwork

# 连接容器到网络
docker run --network mynetwork ubuntu:20.04

# 查看网络
docker network ls
docker network inspect mynetwork
```

### Docker 高级应用

**Docker Wine**：
> 参考：[[Docker Wine]]

```dockerfile
FROM ubuntu:20.04

RUN apt-get update && apt-get install -y \
    wine \
    winetricks \
    && rm -rf /var/lib/apt/lists/*

ENV WINEPREFIX=/wine
ENV DISPLAY=:0

COPY app.exe /app/
WORKDIR /app

CMD ["wine", "app.exe"]
```

---

## 系统调试技术

### PTrace 系统调用

> 参考：[[PTrace 基本逻辑]]、[[PTrace 常见使用方法]]

**PTrace 基础**：
```c
#include <sys/ptrace.h>
#include <sys/wait.h>

// 附加到进程
ptrace(PTRACE_ATTACH, pid, NULL, NULL);
waitpid(pid, &status, 0);

// 读取内存
long data = ptrace(PTRACE_PEEKDATA, pid, addr, NULL);

// 写入内存
ptrace(PTRACE_POKEDATA, pid, addr, data);

// 继续执行
ptrace(PTRACE_CONT, pid, NULL, NULL);
```

**调试器实现**：
```c
void simple_debugger(pid_t child_pid) {
    int status;
    struct user_regs_struct regs;
    
    while (1) {
        waitpid(child_pid, &status, 0);
        
        if (WIFEXITED(status)) break;
        
        // 获取寄存器状态
        ptrace(PTRACE_GETREGS, child_pid, NULL, &regs);
        printf("RIP: 0x%llx\n", regs.rip);
        
        // 单步执行
        ptrace(PTRACE_SINGLESTEP, child_pid, NULL, NULL);
    }
}
```

### 系统调用追踪

**Strace 使用**：
> 参考：[[Strace 使用]]

```bash
# 基本追踪
strace -f -o trace.log ./program

# 追踪特定系统调用
strace -e trace=open,read,write ./program

# 统计系统调用
strace -c ./program

# 追踪信号
strace -e signal=all ./program
```

**自定义追踪**：
```python
import subprocess
import re

def trace_syscalls(program):
    proc = subprocess.Popen(['strace', '-f', program], 
                           stderr=subprocess.PIPE, 
                           universal_newlines=True)
    
    for line in proc.stderr:
        if 'open(' in line:
            match = re.search(r'open\("([^"]+)"', line)
            if match:
                print(f"Opened file: {match.group(1)}")
```

---

## 虚拟化应用

### PANDA 动态分析

> 参考：[[PANDA 动态分析器]]、[[Pandas Images]]

**PANDA 特性**：
- 基于 QEMU 的全系统分析平台
- 支持录制和重放功能
- 提供丰富的分析插件
- 支持多架构分析

**基本使用**：
```bash
# 录制执行过程
panda-system-x86_64 -replay record -monitor stdio disk.img

# 重放并分析
panda-system-x86_64 -replay replay -panda plugin_name disk.img
```

### VMware 配置

> 参考：[[VMware 配置]]

**虚拟机优化**：
- **内存配置**：合理分配内存大小
- **CPU 设置**：启用虚拟化扩展
- **磁盘优化**：使用 SSD 存储
- **网络配置**：选择合适的网络模式

---

## 🔗 相关链接

- [[二进制安全与逆向工程]]
- [[编译器技术与程序分析]]
- [[调试工具集合]]
- [[WAJI 项目]]

---

*最后更新：2025-06-16*
