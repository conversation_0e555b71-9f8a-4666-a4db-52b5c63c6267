# 进程调度机制

## 概述

<GeminiOptimizationFrom> Note/Linux/理解Linux进程调度.md </GeminiOptimizationFrom>

Linux 进程调度是操作系统内核的核心功能之一，负责管理进程的执行顺序和资源分配。理解进程调度机制对于系统编程、性能优化和内核开发至关重要。

## 进程地址空间管理

### 虚拟内存空间隔离

对于 32 位的 Linux，其每一个进程都有 4G 的寻址空间，但当一个进程访问其虚拟内存空间中的某个地址时又是怎样实现不与其它进程的虚拟空间混淆的呢？

每个进程都有其自身的页面目录 PGD，Linux 将该目录的指针存放在与进程对应的内存结构 `task_struct.(struct mm_struct)mm->pgd` 中。每当一个进程被调度（`schedule()`）即**将进入运行态**时，Linux 内核都要用该进程的 PGD 指针设置 CR3（`switch_mm()`）。

### 进程切换机制

进程调度的核心是上下文切换，包括：

1. **保存当前进程状态**: 寄存器、程序计数器等
2. **切换页表**: 更新 CR3 寄存器指向新进程的页目录
3. **恢复目标进程状态**: 加载新进程的上下文
4. **跳转执行**: 开始执行新进程的代码

## 进程与线程的内核视图

> 参考：https://stackoverflow.com/questions/9305992/if-threads-share-the-same-pid-how-can-they-be-identified

```
                         USER VIEW
                         vvvv vvvv
              |          
<-- PID 43 -->|<----------------- PID 42 ----------------->
              |                           |
              |      +---------+          |
              |      | process |          |
              |     _| pid=42  |_         |
         __(fork) _/ | tgid=42 | \_ (new thread) _
        /     |      +---------+          |       \
+---------+   |                           |    +---------+
| process |   |                           |    | process |
| pid=43  |   |                           |    | pid=44  |
| tgid=43 |   |                           |    | tgid=42 |
+---------+   |                           |    +---------+
              |                           |
<-- PID 43 -->|<--------- PID 42 -------->|<--- PID 44 --->
              |                           |
                        ^^^^^^ ^^^^
                        KERNEL VIEW
```

### 进程标识符说明

- **PID (Process ID)**: 在内核中，每个执行实体（包括线程）都有唯一的 PID
- **TGID (Thread Group ID)**: 线程组 ID，同一进程中的所有线程共享相同的 TGID
- **用户视图**: 用户空间看到的是 TGID，即传统意义上的进程 ID
- **内核视图**: 内核中每个线程都有独立的 PID，但属于同一 TGID

## 调度算法

### CFS (Completely Fair Scheduler)

Linux 内核使用 CFS 作为默认的进程调度器：

1. **虚拟运行时间**: 每个进程维护一个虚拟运行时间
2. **红黑树**: 使用红黑树维护可运行进程队列
3. **公平性**: 确保所有进程获得公平的 CPU 时间

### 调度类

Linux 支持多种调度类：

- **CFS**: 普通进程的公平调度
- **RT**: 实时进程调度
- **DL**: 截止时间调度
- **IDLE**: 空闲进程调度

## 内存管理与调度

### 页表切换

进程调度时的关键操作：

```c
// 简化的上下文切换过程
void context_switch(struct task_struct *prev, struct task_struct *next) {
    // 切换内存管理上下文
    switch_mm(prev->mm, next->mm, next);
    
    // 切换处理器状态
    switch_to(prev, next, prev);
}
```

### 内存保护

1. **地址空间隔离**: 每个进程有独立的虚拟地址空间
2. **权限检查**: 内核态/用户态权限分离
3. **页面保护**: 读/写/执行权限控制

## 性能考虑

### 调度延迟

- **调度延迟**: 从进程变为可运行到实际运行的时间
- **上下文切换开销**: 保存/恢复进程状态的成本
- **缓存影响**: 进程切换对 CPU 缓存的影响

### 优化策略

1. **CPU 亲和性**: 尽量在同一 CPU 上运行进程
2. **负载均衡**: 在多核系统中平衡负载
3. **抢占控制**: 合理的抢占策略

## 相关主题

- [[内核内存初始化]] - 内存管理基础
- [[ELF_文件格式]] - 进程映像格式
- [[编译工具链]] - 进程创建和加载
