# Libc Malloc 内部机制

## 概述

<GeminiOptimizationFrom> Note/CTF-PWN/libc-malloc.md </GeminiOptimizationFrom>

glibc 的 malloc 实现是基于 ptmalloc2 的内存分配器，它提供了高效的内存管理机制。理解其内部实现对于系统编程、性能优化和安全研究都至关重要。

## 参考资料

- **libc 官方 Git**: https://sourceware.org/git/gitweb.cgi?p=glibc.git
- **libc Git 仓库**: https://sourceware.org/git/glibc.git
- **GitHub 镜像**: https://github.com/bminor/glibc
- **glibc Wiki**: https://sourceware.org/glibc/wiki/MallocInternals

## 核心数据结构

### malloc_chunk 结构

```c
/*
  This struct declaration is misleading (but accurate and necessary).
  It declares a "view" into memory allowing access to necessary
  fields at known offsets from a given base. See explanation below.
*/

struct malloc_chunk {

  INTERNAL_SIZE_T      mchunk_prev_size;  /* Size of previous chunk (if free).  */
  INTERNAL_SIZE_T      mchunk_size;       /* Size in bytes, including overhead. */

  struct malloc_chunk* fd;         /* double links -- used only if free. */
  struct malloc_chunk* bk;

  /* Only used for large blocks: pointer to next larger size.  */
  struct malloc_chunk* fd_nextsize; /* double links -- used only if free. */
  struct malloc_chunk* bk_nextsize;
};

typedef struct malloc_chunk *mbinptr;
```

### 字段说明

- **mchunk_prev_size**: 前一个 chunk 的大小（仅当前一个 chunk 为空闲时有效）
- **mchunk_size**: 当前 chunk 的大小，包括元数据开销
- **fd/bk**: 双向链表指针，仅在 chunk 空闲时使用
- **fd_nextsize/bk_nextsize**: 用于大块内存的额外链表指针

## Arena 管理

### main_arena 结构

```c
static struct malloc_state main_arena =
{
  .mutex = _LIBC_LOCK_INITIALIZER,
  .next = &main_arena,
  .attached_threads = 1
};
```

### Arena 相关概念

- **main_arena**: 主分配区域，glibc 的 malloc.c 中存在一个静态变量唯一指向主分配区域，通过 next 指针指向其他的分配区域
- **多线程支持**: 每个线程可以有自己的 arena，减少锁竞争

## Fast Bins 实现

### 数据结构

```c
typedef struct malloc_chunk *mfastbinptr;
#define fastbin(ar_ptr, idx) ((ar_ptr)->fastbinsY[idx])

/* offset 2 to use otherwise unindexable first 2 bins */
#define fastbin_index(sz) \
  ((((unsigned int) (sz)) >> (SIZE_SZ == 8 ? 4 : 3)) - 2)

/* The maximum fastbin request size we support */
#define MAX_FAST_SIZE     (80 * SIZE_SZ / 4)

#define NFASTBINS  (fastbin_index (request2size (MAX_FAST_SIZE)) + 1)
```

### Fast Bins 特性

1. **单链表结构**: 使用 fd 指针构成单向链表
2. **LIFO 策略**: 后进先出的分配策略
3. **无合并**: 不会与相邻的空闲 chunk 合并
4. **快速分配**: 分配和释放操作都很快

## 内存分配术语

### 常用变量和宏

| 变量/宏/函数 | 描述                                                         | 示例                               |
| ------------ | ------------------------------------------------------------ | ---------------------------------- |
| victim       | **inspected/selected chunk**，被选择的 Chunk                | victim = av->top;                  |
| main_arena   | 主分配区域。glibc 的 malloc.c 中存在一个静态变量唯一指向主分配区域，通过 next 指针指向其他的分配区域 | static struct malloc_state main_arena; |
| ar_ptr       | 一个指向 `malloc_state` 的指针，可以用来遍历所有的 `Arenas`。可以通过 `arena.c:_int_new_arena` 创建一个新的 `Arena` | mstate ar_ptr = &main_arena;       |
| av           | **arena vector**，通常作为函数的**形式参数**使用，表示当前的堆操作行为所在的分配区域 | struct malloc_state * av;         |

## 内部函数命名规范

### 函数前缀说明

> **备忘**: `_int_` 开头的函数表示 `internal`，这些是内部实现的函数，不会暴露到外部

常见的内部函数：
- `_int_malloc()`: 内部内存分配函数
- `_int_free()`: 内部内存释放函数
- `_int_realloc()`: 内部内存重分配函数

## 分配策略

### 分配流程

1. **Fast Bin 检查**: 首先检查是否可以从 fast bin 分配
2. **Small Bin 检查**: 检查对应大小的 small bin
3. **Unsorted Bin 处理**: 处理 unsorted bin 中的 chunk
4. **Large Bin 检查**: 对于大块内存，检查 large bin
5. **Top Chunk 分割**: 从 top chunk 分割内存
6. **系统调用**: 如果都不满足，调用 `sbrk()` 或 `mmap()`

### 释放流程

1. **Fast Bin 范围**: 如果大小在 fast bin 范围内，直接加入 fast bin
2. **合并检查**: 检查前后相邻 chunk 是否空闲，进行合并
3. **Unsorted Bin**: 将合并后的 chunk 加入 unsorted bin
4. **Top Chunk 合并**: 如果与 top chunk 相邻，合并到 top chunk

## 安全机制

### 完整性检查

1. **Chunk 大小检查**: 验证 chunk 大小的合理性
2. **指针验证**: 检查链表指针的有效性
3. **Double Free 检测**: 防止重复释放同一个 chunk

### 现代防护

1. **Safe Linking**: 对指针进行异或加密
2. **Tcache**: 线程本地缓存，减少锁竞争
3. **随机化**: 地址空间布局随机化

## 性能优化

### 缓存机制

1. **Fast Bins**: 快速分配小块内存
2. **Tcache**: 线程本地缓存
3. **Arena 分离**: 多线程环境下的性能优化

### 内存对齐

- 所有分配的内存都按照 `MALLOC_ALIGNMENT` 对齐
- 通常为 8 字节（32位）或 16 字节（64位）

## 调试和分析

### 环境变量

```bash
export MALLOC_CHECK_=1    # 启用基本检查
export MALLOC_CHECK_=2    # 启用详细检查
export MALLOC_CHECK_=3    # 启用完整检查并在错误时中止
```

### 调试工具

- **malloc_stats()**: 打印内存分配统计信息
- **malloc_info()**: 获取详细的内存分配信息
- **mtrace()**: 内存泄漏检测

## 相关主题

- [[Ptmalloc_Bins]] - Ptmalloc Bins 详细结构
- [[Fastbin_Attack]] - Fast Bin 攻击技术
- [[堆漏洞利用总结]] - 堆漏洞利用方法
