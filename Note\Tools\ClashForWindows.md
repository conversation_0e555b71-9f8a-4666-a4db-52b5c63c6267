# Clash For Windows

---



### Mixin

```yaml
mixin:
   hosts:
     'mtalk.google.com': ***************
     'services.googleapis.cn': *************
     'raw.githubusercontent.com': **************
   dns:
     enable: true
     default-nameserver:
       - *********
       - *******
     ipv6: false
     enhanced-mode: redir-host #fake-ip
     nameserver:
       - https://dns.rubyfish.cn/dns-query
       - https://*********/dns-query
       - https://dns.pub/dns-query
     fallback:
       - https://*******/dns-query
       - https://public.dns.iij.jp/dns-query
       - https://dns.twnic.tw/dns-query
     fallback-filter:
       geoip: true
       ipcidr:
         - 240.0.0.0/4
         - 0.0.0.0/32
         - 127.0.0.1/32
       domain:
         - +.google.com
         - +.facebook.com
         - +.twitter.com
         - +.youtube.com
         - +.xn--ngstr-lra8j.com
         - +.google.cn
         - +.googleapis.cn
         - +.gvt1.com
   tun:
     enable: true
     stack: gvisor
     dns-hijack:
       - **********:53
     macOS-auto-route: true
     macOS-auto-detect-interface: true # 自动检测出口网卡
```



