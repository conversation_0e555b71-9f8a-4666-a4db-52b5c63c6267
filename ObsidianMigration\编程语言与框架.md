# 编程语言与框架

> Python、JavaScript、Java 等编程语言及其生态系统

## 📋 目录

- [[#Python 生态系统]]
- [[#机器学习框架]]
- [[#Web 开发技术]]
- [[#Java 开发]]
- [[#跨平台开发]]

---

## Python 生态系统

### Web 开发

#### Flask 框架

> 参考：[[Flask 框架使用]]

**基本应用结构**：
```python
from flask import Flask, request, Response
from flask_cors import cross_origin
import re
from base64 import b64decode

app = Flask(__name__)

@app.before_request
def flask_before_request():
    headers = dict(request.headers)
    print(headers)
    print("Flask Before Request Hook")

@app.after_request
def flask_after_request(response: Response):
    print("Flask After Request Hook")
    print(type(response))
    return response

@app.route("/", methods=["GET"])
def index():
    return "Hello World!"

# API 端点示例
JS_BASE64_PATTERN = re.compile(r"^data:[\w\d]+/[\w\d]+;base64,(.+)$")

@app.route("/api/process", methods=["POST"])
@cross_origin()
def handle_api():
    # 验证请求来源
    assert "trusted-domain.com" in request.headers.get("Origin")
    
    # 获取 JSON 数据
    data = request.get_json()
    assert isinstance(data, dict)
    
    # 处理 Base64 图像数据
    image_base64 = data.get("image")
    assert re.match(JS_BASE64_PATTERN, image_base64)
    image_base64 = re.findall(JS_BASE64_PATTERN, image_base64)[0]
    image = b64decode(image_base64)
    
    # 返回处理结果
    resp = {"code": 0, "data": "processed"}
    return resp

if __name__ == "__main__":
    app.run("0.0.0.0", "8000", debug=True)
```

**依赖安装**：
```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple \
    flask==2.1.0 \
    flask-cors==3.0.10 \
    ddddocr==1.4.7
```

### 系统编程

#### 进程与线程

> 参考：[[Python 多线程 or 多进程]]

**多线程示例**：
```python
import threading
import queue
import time

def worker(q, worker_id):
    while True:
        item = q.get()
        if item is None:
            break
        print(f"Worker {worker_id} processing {item}")
        time.sleep(1)
        q.task_done()

# 创建队列和线程
q = queue.Queue()
threads = []

for i in range(3):
    t = threading.Thread(target=worker, args=(q, i))
    t.start()
    threads.append(t)

# 添加任务
for i in range(10):
    q.put(f"task_{i}")

# 等待完成
q.join()

# 停止线程
for i in range(3):
    q.put(None)
for t in threads:
    t.join()
```

**多进程示例**：
```python
import multiprocessing
import time

def cpu_bound_task(n):
    """CPU 密集型任务"""
    result = 0
    for i in range(n):
        result += i * i
    return result

def io_bound_task(duration):
    """I/O 密集型任务"""
    time.sleep(duration)
    return f"Completed after {duration}s"

if __name__ == "__main__":
    # CPU 密集型任务使用多进程
    with multiprocessing.Pool() as pool:
        results = pool.map(cpu_bound_task, [1000000, 2000000, 3000000])
        print("CPU tasks results:", results)
    
    # I/O 密集型任务使用多线程更合适
    import concurrent.futures
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = [executor.submit(io_bound_task, i) for i in [1, 2, 3]]
        results = [f.result() for f in futures]
        print("I/O tasks results:", results)
```

#### 系统交互

**ADB 操作**：
> 参考：[[Python ADB]]

```python
import subprocess
import json

class ADBHelper:
    @staticmethod
    def execute_command(command):
        """执行 ADB 命令"""
        try:
            result = subprocess.run(command, shell=True, 
                                  capture_output=True, text=True)
            return result.stdout.strip()
        except Exception as e:
            print(f"Error executing command: {e}")
            return None
    
    @staticmethod
    def get_devices():
        """获取连接的设备列表"""
        output = ADBHelper.execute_command("adb devices")
        devices = []
        for line in output.split('\n')[1:]:
            if line.strip() and 'device' in line:
                device_id = line.split()[0]
                devices.append(device_id)
        return devices
    
    @staticmethod
    def install_apk(apk_path, device_id=None):
        """安装 APK"""
        cmd = f"adb install {apk_path}"
        if device_id:
            cmd = f"adb -s {device_id} install {apk_path}"
        return ADBHelper.execute_command(cmd)
    
    @staticmethod
    def screenshot(output_path, device_id=None):
        """截屏"""
        cmd = f"adb shell screencap -p > {output_path}"
        if device_id:
            cmd = f"adb -s {device_id} shell screencap -p > {output_path}"
        return ADBHelper.execute_command(cmd)
```

**Telnet 操作**：
> 参考：[[Python Telnet]]

```python
import telnetlib
import time

class TelnetClient:
    def __init__(self, host, port, timeout=10):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.tn = None
    
    def connect(self):
        """连接到 Telnet 服务器"""
        try:
            self.tn = telnetlib.Telnet(self.host, self.port, self.timeout)
            return True
        except Exception as e:
            print(f"Connection failed: {e}")
            return False
    
    def login(self, username, password):
        """登录"""
        self.tn.read_until(b"login: ")
        self.tn.write(username.encode('ascii') + b"\n")
        self.tn.read_until(b"Password: ")
        self.tn.write(password.encode('ascii') + b"\n")
        return self.tn.read_until(b"$ ", timeout=5)
    
    def execute_command(self, command):
        """执行命令"""
        self.tn.write(command.encode('ascii') + b"\n")
        return self.tn.read_until(b"$ ", timeout=10)
    
    def close(self):
        """关闭连接"""
        if self.tn:
            self.tn.close()
```

### 数据处理

#### 数据库操作

**SQLite3 使用**：
> 参考：[[SQLite3 使用]]

```python
import sqlite3
import json
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    data TEXT
                )
            ''')
            conn.commit()
    
    def insert_log(self, level, message, data=None):
        """插入日志记录"""
        timestamp = datetime.now().isoformat()
        data_json = json.dumps(data) if data else None
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                'INSERT INTO logs (timestamp, level, message, data) VALUES (?, ?, ?, ?)',
                (timestamp, level, message, data_json)
            )
            conn.commit()
    
    def query_logs(self, level=None, limit=100):
        """查询日志"""
        with sqlite3.connect(self.db_path) as conn:
            if level:
                cursor = conn.execute(
                    'SELECT * FROM logs WHERE level = ? ORDER BY timestamp DESC LIMIT ?',
                    (level, limit)
                )
            else:
                cursor = conn.execute(
                    'SELECT * FROM logs ORDER BY timestamp DESC LIMIT ?',
                    (limit,)
                )
            return cursor.fetchall()
```

#### 图像处理

**OpenCV 应用**：
> 参考：[[OpenCV 使用]]

```python
import cv2
import numpy as np

class ImageProcessor:
    @staticmethod
    def load_image(path):
        """加载图像"""
        return cv2.imread(path)
    
    @staticmethod
    def resize_image(image, width, height):
        """调整图像大小"""
        return cv2.resize(image, (width, height))
    
    @staticmethod
    def apply_blur(image, kernel_size=5):
        """应用模糊效果"""
        return cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
    
    @staticmethod
    def detect_edges(image, threshold1=100, threshold2=200):
        """边缘检测"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        return cv2.Canny(gray, threshold1, threshold2)
    
    @staticmethod
    def find_contours(image):
        """查找轮廓"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        contours, hierarchy = cv2.findContours(
            gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
        return contours, hierarchy
    
    @staticmethod
    def save_image(image, path):
        """保存图像"""
        cv2.imwrite(path, image)

# 使用示例
processor = ImageProcessor()
img = processor.load_image('input.jpg')
resized = processor.resize_image(img, 800, 600)
blurred = processor.apply_blur(resized)
processor.save_image(blurred, 'output.jpg')
```

### 自动化工具

**屏幕截图**：
> 参考：[[MSS 高性能截屏库]]

```python
import mss
import time
from PIL import Image

class ScreenCapture:
    def __init__(self):
        self.sct = mss.mss()
    
    def capture_screen(self, monitor=1):
        """截取指定显示器的屏幕"""
        monitor_info = self.sct.monitors[monitor]
        screenshot = self.sct.grab(monitor_info)
        return Image.frombytes('RGB', screenshot.size, screenshot.bgra, 'raw', 'BGRX')
    
    def capture_region(self, left, top, width, height):
        """截取指定区域"""
        region = {'left': left, 'top': top, 'width': width, 'height': height}
        screenshot = self.sct.grab(region)
        return Image.frombytes('RGB', screenshot.size, screenshot.bgra, 'raw', 'BGRX')
    
    def continuous_capture(self, interval=1.0, duration=10.0):
        """连续截图"""
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < duration:
            screenshot = self.capture_screen()
            screenshot.save(f'frame_{frame_count:04d}.png')
            frame_count += 1
            time.sleep(interval)
        
        return frame_count

# 使用示例
capture = ScreenCapture()
img = capture.capture_screen()
img.save('screenshot.png')
```

---

## 机器学习框架

### PyTorch 深度学习

> 参考：[[PyTorch 常用组件]]、[[LSTM Mechanism]]

#### 基础组件

**Tensor 操作**：
> 参考：[[Tensor 常用的内置方法]]

```python
import torch
import torch.nn as nn
import torch.optim as optim

# 创建张量
x = torch.randn(3, 4)
y = torch.zeros(3, 4)

# 基本操作
z = x + y
z = torch.add(x, y)
z = x.add_(y)  # 原地操作

# 形状操作
x_reshaped = x.view(2, 6)
x_transposed = x.transpose(0, 1)

# GPU 操作
if torch.cuda.is_available():
    x_gpu = x.cuda()
    y_gpu = y.cuda()
    z_gpu = x_gpu + y_gpu
```

#### 神经网络模型

**LSTM 实现**：
```python
class LSTMModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)
    
    def forward(self, x):
        # 初始化隐藏状态
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        
        # LSTM 前向传播
        out, _ = self.lstm(x, (h0, c0))
        
        # 取最后一个时间步的输出
        out = self.fc(out[:, -1, :])
        return out

# 模型训练
model = LSTMModel(input_size=10, hidden_size=50, num_layers=2, output_size=1)
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

for epoch in range(100):
    # 前向传播
    outputs = model(train_data)
    loss = criterion(outputs, train_labels)
    
    # 反向传播
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    if epoch % 10 == 0:
        print(f'Epoch [{epoch}/100], Loss: {loss.item():.4f}')
```

#### 损失函数

> 参考：[[Criterion 损失函数]]

```python
# 常用损失函数
mse_loss = nn.MSELoss()
cross_entropy = nn.CrossEntropyLoss()
bce_loss = nn.BCELoss()

# 自定义损失函数
class CustomLoss(nn.Module):
    def __init__(self, weight=1.0):
        super(CustomLoss, self).__init__()
        self.weight = weight
    
    def forward(self, predictions, targets):
        mse = nn.MSELoss()(predictions, targets)
        l1 = nn.L1Loss()(predictions, targets)
        return mse + self.weight * l1
```

---

## Web 开发技术

### JavaScript 基础

#### jQuery 应用

> 参考：[[jQuery 基础]]

```javascript
$(document).ready(function() {
    // DOM 操作
    $('#myButton').click(function() {
        $('.content').fadeIn();
    });
    
    // AJAX 请求
    $.ajax({
        url: '/api/data',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            $('#result').html(data.message);
        },
        error: function(xhr, status, error) {
            console.error('Error:', error);
        }
    });
    
    // 表单处理
    $('#myForm').submit(function(e) {
        e.preventDefault();
        var formData = $(this).serialize();
        
        $.post('/api/submit', formData)
            .done(function(response) {
                alert('Success!');
            })
            .fail(function() {
                alert('Error occurred!');
            });
    });
});
```

#### Fetch API

> 参考：[[Fetch API 使用]]

```javascript
// 基本 GET 请求
async function fetchData() {
    try {
        const response = await fetch('/api/data');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}

// POST 请求
async function postData(data) {
    try {
        const response = await fetch('/api/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Post error:', error);
        throw error;
    }
}

// 文件上传
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        return await response.json();
    } catch (error) {
        console.error('Upload error:', error);
        throw error;
    }
}
```

#### OCR 集成

> 参考：[[JS OCR Tesseract]]

```javascript
// Tesseract.js OCR 实现
import Tesseract from 'tesseract.js';

class OCRProcessor {
    constructor() {
        this.worker = null;
    }
    
    async initialize() {
        this.worker = await Tesseract.createWorker();
        await this.worker.loadLanguage('eng+chi_sim');
        await this.worker.initialize('eng+chi_sim');
    }
    
    async recognizeText(imageFile) {
        if (!this.worker) {
            await this.initialize();
        }
        
        try {
            const { data: { text } } = await this.worker.recognize(imageFile);
            return text;
        } catch (error) {
            console.error('OCR error:', error);
            throw error;
        }
    }
    
    async terminate() {
        if (this.worker) {
            await this.worker.terminate();
            this.worker = null;
        }
    }
}

// 使用示例
const ocr = new OCRProcessor();

document.getElementById('imageInput').addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        try {
            const text = await ocr.recognizeText(file);
            document.getElementById('result').textContent = text;
        } catch (error) {
            console.error('Recognition failed:', error);
        }
    }
});
```

---

## 🔗 相关链接

- [[系统管理与运维]]
- [[开发工具链]]
- [[AI 辅助开发]]
- [[调试工具集合]]

---

*最后更新：2025-06-16*
