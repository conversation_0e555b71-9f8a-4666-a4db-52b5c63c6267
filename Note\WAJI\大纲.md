# WAJI: What Allocator Just In-use?

---





### Abstract

在现代应用程序中, 各种各样的 Dynamic Memory Allocator(动态内存分配器, DMA) 被广泛使用. 不同种类的应用程序对 DMA 有着不同的需求, 内存密集型的程序希望分配器越快越好, 而一些安全敏感的应用程序希望即使代码中存在内存破坏漏洞, 堆分配器也能够确保程序中的控制流不会被篡改. 开发人员在选择堆分配器时, 需要在安全性与性能之间找一个平衡点, 因此大量的专业应用程序都没有使用系统中内置的堆分配器, 例如 ptmalloc(glibc), 而是使用了 Custom Memory Allocator(自定义堆管理器, CMA). 



与标准库中的分配器不同, CMA 的实现多种多样, 这使得我们在分析目标应用时遇到了极大的挑战. 通常而言, 我们只能够获取一个没有符号的二进制文件, 从中找到堆漏洞往往需要大量的人工介入同时花费大量的时间; 即使发现了内存破坏类型的漏洞, CMA 程序的利用方式也与标准库中的漏洞利用方式不同, 需要人工理解内存管理过程, 通过大量的调试才能够完成漏洞的利用.



为了填补上述难点, 本文提出了 WAJI 系统, 它能够不依赖符号的从二进程程序中识别出 CMA 相关函数, 并且通过破坏后执行的方法识别CMA元数据中的管理字段含义, 并根据上述信息, 为内存破坏漏洞计算目标堆布局, 并基于此利用堆风水求解器完成proof-of-concept (POC)样本的生成, 从而实现 CMA 程序的漏洞自动利用.





### 1.  Introduction

 #### Problem

> 现有的自定义堆识别方法识别效率低, 且识别准确率较低, 且缺乏一种有效的验证手段进行验证. 同时, 对于使用 CMA 的应用程序, 在漏洞利用时无法套用现有的标准库堆漏洞利用知识, 导致需要大量接入人工分析.
>
> 1. 如何在 Binary 中识别出 CMA 函数, 并快速验证?
> 2. 如何自动识别 CMA 中的 Metadata, 并识别其中不同字段的功能?
> 3. 在有内存破坏漏洞的前提下, 如何为 CMA 应用程序生成 Exploit?



#### Challenges

- 从 Trace 中找到的大量 CMA 函数, 如何构造合法的上下文进行快速验证?
- 如何从 Metadata 中识别字段范围, 并找到对应的语义?
- 如何为 MAZE 生成一个合法的目标堆布局?



#### Solution

- **恢复-执行机制**, 针对候选的 CMA 函数 f, 在 f 第一次调用的时刻快照, 通过强制执行技术验证 f 函数的功能是否符合预期
- **破坏-执行机制**, 破坏 CMA 头部与尾部的字段, 然后进行特定的内存操作, 通过观察后续的控制流与数据流行为, 判断该字段的功能
- **Knowledge-based Generator**, 根据现有的内存破坏能力以及 Metadata 中的字段, 计算出能够破坏的字段, 并提供给 MAZE 求解



#### Contributions

- 设计了一种新方法对二进程程序中的函数进行识别并验证, 以确认其是否为Allocator
- 对堆的元数据进行了建模, 并且能够恢复未知Allocator中的大部分与利用有关的字段
- 对堆元数据破坏有关的漏洞利用方式进行了建模, 并且修改了 MAZE 原型系统, 使之能够自动构建目标堆布局, 并完成元数据破坏漏洞的 AEG



### 2.   Background(Heap Management Mechanism) 

> 介绍现有的堆管理机制, 以及在堆相关漏洞利用过程中, Metadata的关键作用, 并且介绍一个 Motivation Example



#### 2.1 Heap Implementations

> 讲一些常见的堆实现, 例如 ptmalloc(libc), mimalloc, talloc 等
>
> 讲堆管理器的功能、目的以及一些通用的实现模式



#### 2.2 Heap Metadata Exploiting

> 说明堆元数据的重要作用, 以及堆元数据在漏洞利用过程中的重要作用
>
> 介绍元数据破坏漏洞的一般利用思路
>
> 以 ptmalloc 为例讲元数据破坏的后果



 #### 2.3 Motivation Example

> [CVE-2021-30145](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-30145) 或 MAZE 的 CTF 样本

```c
struct Person {
    void (*destructor)(void *_p);
    char name[33];
};

int idx = 0;
struct Person *lst[MAX_LENGTH];

// ----------------- Target -----------------
void default_event(void *_p) {
    memset(_p, '\0', sizeof(struct Person));
}

void win(char *_s) {
    system(_s);
}

// ---------------- Primitives ----------------
void person_new() {
    lst[idx] = __malloc(sizeof(struct Person));
    lst[idx]->destructor = default_event;
    assert(++idx < MAX_LENGTH);
}

void person_delete(int __index) {
    lst[__index]->destructor(lst[__index]);
    __free(lst[__index]);
    lst[__index] = NULL;
}

void person_set_name(int __index) {
    scanf("%s", lst[__index]->name);
}
```



### 3.   WAJI: Design Overview(Approach)

 

![image-20241020225637876](https://cdn.img2ipfs.com/ipfs/QmRrM4SDhTr7SNCETEMA4nyJPEUjTyTZMjNKeMTSRTGdHY?filename=image.png)



 







### 4.   CMA Modeling

> 描述自定义堆的建模过程

#### 4.1 Heap Operation

> 描述堆的操作行为, 包含 静态原型建模 以及 动态行为
>
> malloc, free, realloc, calloc 等

#### 4.2 Heap Interaction

> 描述堆的动态特征行为, 包括控制流特征以及数据流特征
>
> 并且在此处列出从Trace中筛选CMA函数的方法

#### 4.3 CMA Proving

> CMA验证阶段, 包含如何构造合法的上下文, 以及如何在该上下文中进行 CMA 的验证









### 5.   Heap Metadata Structure

 

#### 5.1 Metadata Modeling

> 介绍堆元数据的基本要件, 以及组成成分, 并且对基本的结构进行总结

#### 5.2 Break and Executing

> 介绍“破坏后执行”的基本思路, 实现方法以及检测原理

#### 5.3 Symbolic Heap Metadata Members

> 根据"破坏后执行"的结果, 对堆元数据中的成员进行符号化/语义化, 并验证其功能









### 6.   Heap-related AEG



#### 6.1 MAZE

> 介绍 MAZE 的基本原理, 以及目前 MAZE 的主要限制所在

#### 6.2 Target Layout Generation

> 根据现有的CMA结构, 以及 `Heap Metadata Exploiting ` 的方法, 
>
> 结合MAZE计算得到的当前布局, 生成一个能够破坏指定字段的目标的堆布局

#### 6.3 PoC Generation

> 根据CMA中的字段, 计算当前CMA中的限制(ALignment, Canary)等
>
> 结合目标堆布局, 生成一个MAZE POC

 

 

### 7.   Evaluation

 

 