# pywinauto

---



```python
import os
import sys
import psutil
from pywinauto.application import Application

os.environ['PYDEVD_WARN_EVALUATION_TIMEOUT'] = '10'

def getWechatPID() -> int:
    p_list = psutil.pids()
    for pid in p_list:
        p = psutil.Process(pid=pid)
        if p.name().lower() == "wechat.exe":
            return p.pid
    return 0

print(getWechatPID())
app = Application(backend="uia").connect(process=getWechatPID())

win_pyq = app['朋友圈']
print(win_pyq.dump_tree())
print(dir(win_pyq.wrapper_object()))

if __name__ == "__main__":
    print("Hello World!")

```



