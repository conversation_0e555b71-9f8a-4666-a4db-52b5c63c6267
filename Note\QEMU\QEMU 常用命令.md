# QEMU 常用命令
---



### 0. 暂停/启动虚拟机

```shell
(qemu) stop
(qemu) cont
```





### 1. 启动一个镜像

```shell
qemu-system-x86_64 
# -m设置内存, 如果使用 qemu-system-i386 则最大内存不能超过3583(4096-512), QEMU 需要 512M 空间
# 如果是32位系统, 推荐使用 2G
-m 2G 
# 设置使用的镜像
-hda img.qcow2 
# 网络设置, 设置网络前端(虚拟网卡硬件), NIC即(Network Interface Controller, 网络接口控制器)
# 可以选择网卡模式["e1000", "rtl8139"]
-net nic,model=rtl8139 
# 网络设置, 设置网络后端(QEMU与HOST交换数据部分), user模式提供NAT服务
# hostfwd, 估计意思是 host forward, 可以写为 hostfwd=tcp::<主机端口>-:<虚拟机端口>
-net user,hostfwd=tcp:0.0.0.0:8088-0.0.0.0:8080 
# 开启声卡
-soundhw all 
# USB 相关配置, 具体含义不清楚
-usb -device usb-tablet 
# 是否开启 QEMU 的交互命令行
-monitor stdio 
# 开启 VNC , 开启的端口为5900+所写的端口号
-vnc :25 
# 快照模式启动, 该模式启动不会修改加载的镜像文件
-snapshot 
```
单行命令如下
`qemu-system-i386 -m 2G -hda img.qcow2 -net nic,model=e1000 -net user,hostfwd=tcp::8080-:80 -monitor stdio -vnc :25 -snapshot`



制作一个可以被 `QEMU` 中 `-cdrom` 选项挂载的光驱: 

```shell
# 制作一个文件名使用 UTF-8 编码, 使用 Joliet 文件系统长目录拓展的 ISO 镜像
mkisofs -input-charset utf-8 -J -o cdrom_test.iso test/
```



在 QEMU 中切换光驱:

```shell
# 查看所有的外部设备
info block
# 切换指定cd挂载的文件
change ide1-cd0 xxxx.iso
```








### 2. 将 VMware 镜像转换为 QEMU 镜像
```shell
qemu-img convert -f vmdk -O qcow2 ${img}.vmdk ${img}.qcow2
```

### 3. 挂载虚拟磁盘
```shell
# 编译
../configure --prefix=${HOME}/.local/qemu-6.1.0 --python=`which python3` --enable-kvm --enable-vnc --enable-virtfs 
# 主机
~/.local/qemu-6.1.0/bin/qemu-system-i386 -m 3G -enable-kvm -hda ubuntu_1604_x86.qcow -net nic,model=e1000 -net user,hostfwd=tcp::<主机端口>-:<客户端端口> -virtfs local,path=<host dir>,mount_tag=<name>,security_model=none,id=<name> -monitor stdio -vnc :25
# 客户端
mount -t 9p -o trans=virtio <name> <客户端路径> -oversion=9p2000.L,posixacl,msize=104857600,cache=loose
```



### 4. 从新制作新硬盘

```shell
qemu-img create -f qcow2 myubuntu.qcow2 100G
qemu-system-i386 -m 3G -hda myubuntu.qcow2 -cdrom ~/.img/ubuntu-16.04.6-desktop-i386.iso -boot d -net nic,model=e1000 -monitor stdio -vnc :12 -enable-kvm 
(qemu) eject -f ide1-cd0
```



### 5. 快照

```bash
# 制作快照
(qemu) migrate "exec: gzip -c > <snapshot_name>"
# 加载快照
-incoming "exec: gzip -c -d <snapshot_name>"
```



### 6. 指定 pc-bios

```shell
$ qemu-system-x86_64 -L /path/to/pc-bios ...
```



### 7. 使用 noVNC

```bash
-vnc :24,websocket=15901 
```

