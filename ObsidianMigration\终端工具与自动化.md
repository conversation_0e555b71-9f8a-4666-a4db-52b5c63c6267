# 终端工具与自动化

> 终端多路复用、Web 自动化与系统自动化工具

## 📋 目录

- [[#终端多路复用]]
- [[#Web 自动化]]
- [[#Windows 自动化]]
- [[#移动端自动化]]
- [[#文件监控]]

---

## 终端多路复用

### Tmux 详细使用

> 参考：[[Tmux 使用]]

#### 基本概念

**Tmux 架构**：
- **会话（Session）**：每个 tmux 进程都有一个或多个会话，每个会话都是独立的
- **窗口（Window）**：窗口就像一个标签页，每个窗口都运行在指定的会话中
- **面板（Pane）**：面板是窗口中的一个子窗口，可以在一个窗口中分割出多个面板

#### 常用命令

**会话管理**：
```bash
# 创建会话
tmux new-session -s session_name
tmux new -s session_name

# 连接会话
tmux attach -t session_name
tmux a -t session_name

# 列出会话
tmux list-sessions
tmux ls

# 杀死会话
tmux kill-session -t session_name

# 重命名会话
tmux rename-session -t old_name new_name
```

**窗口管理**：
```bash
# 创建新窗口
tmux new-window -t session_name -n window_name

# 切换窗口
tmux select-window -t session_name:window_index

# 重命名窗口
tmux rename-window -t session_name:window_index new_name

# 杀死窗口
tmux kill-window -t session_name:window_index
```

#### 快捷键操作

**基础快捷键**（前缀键：`Ctrl-b`）：
```bash
# 会话操作
Ctrl-b d        # 分离会话
Ctrl-b s        # 列出会话
Ctrl-b $        # 重命名当前会话

# 窗口操作
Ctrl-b c        # 创建新窗口
Ctrl-b n        # 下一个窗口
Ctrl-b p        # 上一个窗口
Ctrl-b l        # 最后一个窗口
Ctrl-b 0-9      # 切换到指定编号的窗口
Ctrl-b ,        # 重命名当前窗口
Ctrl-b &        # 杀死当前窗口

# 面板操作
Ctrl-b "        # 水平分割面板
Ctrl-b %        # 垂直分割面板
Ctrl-b o        # 切换到下一个面板
Ctrl-b ;        # 切换到上一个面板
Ctrl-b x        # 杀死当前面板
Ctrl-b z        # 最大化/恢复面板
Ctrl-b 方向键   # 调整面板大小
```

#### 高级功能

**自动化会话创建**：
```bash
#!/bin/bash
# 创建开发环境会话

SESSION_NAME="dev_env"

# 创建新会话
tmux new-session -s $SESSION_NAME -d

# 创建多个窗口
tmux new-window -t $SESSION_NAME:1 -n "editor" "vim"
tmux new-window -t $SESSION_NAME:2 -n "server" "cd ~/project && python manage.py runserver"
tmux new-window -t $SESSION_NAME:3 -n "logs" "tail -f /var/log/app.log"
tmux new-window -t $SESSION_NAME:4 -n "database" "mysql -u root -p"

# 分割面板
tmux split-window -t $SESSION_NAME:1 -h "cd ~/project"
tmux split-window -t $SESSION_NAME:1 -v "htop"

# 连接到会话
tmux attach -t $SESSION_NAME
```

**获取面板内容**：
```python
import subprocess

class TmuxManager:
    def __init__(self):
        self.sessions = {}
    
    def create_session(self, session_name, command=None):
        """创建新会话"""
        cmd = ["tmux", "new-session", "-s", session_name, "-d"]
        if command:
            cmd.append(command)
        
        try:
            subprocess.run(cmd, check=True)
            return True
        except subprocess.CalledProcessError:
            return False
    
    def get_pane_content(self, session_name, window_index=0, pane_index=0):
        """获取面板内容"""
        target = f"{session_name}:{window_index}.{pane_index}"
        cmd = ["tmux", "capture-pane", "-t", target, "-p"]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            print(f"Error capturing pane: {e}")
            return None
    
    def send_keys(self, session_name, keys, window_index=0, pane_index=0):
        """向面板发送按键"""
        target = f"{session_name}:{window_index}.{pane_index}"
        cmd = ["tmux", "send-keys", "-t", target, keys, "Enter"]
        
        try:
            subprocess.run(cmd, check=True)
            return True
        except subprocess.CalledProcessError:
            return False
    
    def list_sessions(self):
        """列出所有会话"""
        try:
            result = subprocess.run(["tmux", "list-sessions"], 
                                  capture_output=True, text=True, check=True)
            return result.stdout.strip().split('\n')
        except subprocess.CalledProcessError:
            return []
    
    def kill_session(self, session_name):
        """杀死会话"""
        try:
            subprocess.run(["tmux", "kill-session", "-t", session_name], check=True)
            return True
        except subprocess.CalledProcessError:
            return False

# 使用示例
tmux = TmuxManager()

# 创建会话
tmux.create_session("test_session", "ping *******")

# 获取内容
content = tmux.get_pane_content("test_session")
print("Pane content:", content)

# 发送命令
tmux.send_keys("test_session", "echo 'Hello from Python!'")
```

---

## Web 自动化

### Selenium 详细使用

> 参考：[[Selenium Web 自动化]]

#### 环境配置

**安装依赖**：
```bash
# 安装 Selenium
pip install selenium

# 安装 OCR 库
pip install ddddocr

# 下载 ChromeDriver
# https://chromedriver.chromium.org/downloads
```

#### 基础使用

**创建 WebDriver**：
```python
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.common.exceptions import TimeoutException
import ddddocr

class WebAutomation:
    def __init__(self, driver_path="chromedriver.exe", headless=False):
        self.driver_path = driver_path
        self.headless = headless
        self.driver = None
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        self.setup_driver()
    
    def setup_driver(self):
        """设置 Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument("--log-level=3")
        chrome_options.add_argument("--ignore-certificate-errors")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # 设置用户代理
        chrome_options.add_argument(
            "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/91.0.4472.124 Safari/537.36"
        )
        
        service = Service(executable_path=self.driver_path)
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.implicitly_wait(10)
    
    def navigate_to(self, url):
        """导航到指定 URL"""
        self.driver.get(url)
        return self.driver.current_url
    
    def find_element_safe(self, by, value, timeout=10):
        """安全查找元素"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            print(f"Element not found: {by}={value}")
            return None
    
    def login_with_captcha(self, username, password, 
                          username_selector, password_selector, 
                          captcha_input_selector, captcha_img_selector,
                          submit_selector):
        """带验证码的登录"""
        # 输入用户名和密码
        username_elem = self.find_element_safe(By.CSS_SELECTOR, username_selector)
        password_elem = self.find_element_safe(By.CSS_SELECTOR, password_selector)
        
        if not username_elem or not password_elem:
            return False
        
        username_elem.clear()
        username_elem.send_keys(username)
        
        password_elem.clear()
        password_elem.send_keys(password)
        
        # 处理验证码
        captcha_text = self.solve_captcha(captcha_img_selector)
        if captcha_text:
            captcha_input = self.find_element_safe(By.CSS_SELECTOR, captcha_input_selector)
            if captcha_input:
                captcha_input.clear()
                captcha_input.send_keys(captcha_text)
        
        # 提交表单
        submit_btn = self.find_element_safe(By.CSS_SELECTOR, submit_selector)
        if submit_btn:
            submit_btn.click()
            return True
        
        return False
    
    def solve_captcha(self, img_selector):
        """解决验证码"""
        try:
            captcha_img = self.find_element_safe(By.CSS_SELECTOR, img_selector)
            if not captcha_img:
                return None
            
            # 截取验证码图片
            captcha_bytes = captcha_img.screenshot_as_png
            
            # OCR 识别
            captcha_text = self.ocr.classification(captcha_bytes)
            print(f"验证码识别结果: {captcha_text}")
            
            return captcha_text
        except Exception as e:
            print(f"验证码识别失败: {e}")
            return None
    
    def execute_script(self, script):
        """执行 JavaScript"""
        return self.driver.execute_script(script)
    
    def get_cookies(self):
        """获取 Cookies"""
        return self.driver.get_cookies()
    
    def set_cookie(self, cookie_dict):
        """设置 Cookie"""
        self.driver.add_cookie(cookie_dict)
    
    def take_screenshot(self, filename):
        """截图"""
        return self.driver.save_screenshot(filename)
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

# 使用示例
automation = WebAutomation()

# 导航到登录页面
automation.navigate_to("https://example.com/login")

# 自动登录
success = automation.login_with_captcha(
    username="your_username",
    password="your_password",
    username_selector="input[name='username']",
    password_selector="input[name='password']",
    captcha_input_selector="input[name='captcha']",
    captcha_img_selector="img#captcha",
    submit_selector="button[type='submit']"
)

if success:
    print("登录成功")
    # 获取登录后的 cookies
    cookies = automation.get_cookies()
    print("Cookies:", cookies)

automation.close()
```

---

## Windows 自动化

### Pywinauto 使用

> 参考：[[Pywinauto Windows 窗口自动化]]

```python
from pywinauto import Application, Desktop
from pywinauto.findwindows import find_elements
import time

class WindowsAutomation:
    def __init__(self):
        self.desktop = Desktop(backend="uia")
    
    def find_windows(self, title_pattern=None, class_name=None):
        """查找窗口"""
        criteria = {}
        if title_pattern:
            criteria['title_re'] = title_pattern
        if class_name:
            criteria['class_name'] = class_name
        
        try:
            elements = find_elements(**criteria)
            return elements
        except Exception as e:
            print(f"查找窗口失败: {e}")
            return []
    
    def connect_to_app(self, process_id=None, path=None, title=None):
        """连接到应用程序"""
        try:
            if process_id:
                app = Application(backend="uia").connect(process=process_id)
            elif path:
                app = Application(backend="uia").connect(path=path)
            elif title:
                app = Application(backend="uia").connect(title=title)
            else:
                return None
            
            return app
        except Exception as e:
            print(f"连接应用程序失败: {e}")
            return None
    
    def start_application(self, path, args=None):
        """启动应用程序"""
        try:
            if args:
                app = Application(backend="uia").start(f"{path} {args}")
            else:
                app = Application(backend="uia").start(path)
            
            return app
        except Exception as e:
            print(f"启动应用程序失败: {e}")
            return None
    
    def automate_notepad(self, text_content):
        """自动化记事本操作"""
        # 启动记事本
        app = self.start_application("notepad.exe")
        if not app:
            return False
        
        # 等待窗口出现
        time.sleep(2)
        
        # 获取记事本窗口
        notepad = app.window(title_re=".*记事本|.*Notepad")
        
        # 输入文本
        notepad.Edit.type_keys(text_content)
        
        # 保存文件
        notepad.menu_select("文件->保存")
        
        # 在保存对话框中输入文件名
        save_dialog = app.window(title_re=".*保存|.*Save")
        save_dialog.Edit.type_keys("test_file.txt")
        save_dialog.Button2.click()  # 保存按钮
        
        return True
    
    def get_window_info(self, window_title):
        """获取窗口信息"""
        try:
            window = self.desktop.window(title_re=window_title)
            
            info = {
                'title': window.window_text(),
                'class_name': window.class_name(),
                'rectangle': window.rectangle(),
                'is_visible': window.is_visible(),
                'is_enabled': window.is_enabled()
            }
            
            return info
        except Exception as e:
            print(f"获取窗口信息失败: {e}")
            return None

# 使用示例
win_auto = WindowsAutomation()

# 查找所有记事本窗口
notepad_windows = win_auto.find_windows(title_pattern=".*记事本|.*Notepad")
print(f"找到 {len(notepad_windows)} 个记事本窗口")

# 自动化记事本操作
success = win_auto.automate_notepad("这是通过 Python 自动输入的文本！")
if success:
    print("记事本自动化操作成功")
```

---

## 移动端自动化

### ADB 自动化

> 参考：[[Python ADB]]

```python
import subprocess
import time
import json

class ADBAutomation:
    def __init__(self, device_id=None):
        self.device_id = device_id
        self.adb_prefix = ["adb"]
        if device_id:
            self.adb_prefix.extend(["-s", device_id])
    
    def execute_command(self, command):
        """执行 ADB 命令"""
        try:
            full_command = self.adb_prefix + command
            result = subprocess.run(
                full_command, 
                capture_output=True, 
                text=True, 
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"ADB 命令执行失败: {e}")
            return None
    
    def get_devices(self):
        """获取连接的设备列表"""
        output = subprocess.run(["adb", "devices"], capture_output=True, text=True)
        devices = []
        
        for line in output.stdout.split('\n')[1:]:
            if line.strip() and 'device' in line:
                device_id = line.split()[0]
                devices.append(device_id)
        
        return devices
    
    def install_apk(self, apk_path):
        """安装 APK"""
        return self.execute_command(["install", apk_path])
    
    def uninstall_app(self, package_name):
        """卸载应用"""
        return self.execute_command(["uninstall", package_name])
    
    def start_app(self, package_name, activity_name):
        """启动应用"""
        component = f"{package_name}/{activity_name}"
        return self.execute_command(["shell", "am", "start", "-n", component])
    
    def stop_app(self, package_name):
        """停止应用"""
        return self.execute_command(["shell", "am", "force-stop", package_name])
    
    def screenshot(self, output_path):
        """截屏"""
        # 在设备上截屏
        self.execute_command(["shell", "screencap", "-p", "/sdcard/screenshot.png"])
        
        # 拉取到本地
        result = self.execute_command(["pull", "/sdcard/screenshot.png", output_path])
        
        # 删除设备上的文件
        self.execute_command(["shell", "rm", "/sdcard/screenshot.png"])
        
        return result is not None
    
    def tap(self, x, y):
        """点击屏幕坐标"""
        return self.execute_command(["shell", "input", "tap", str(x), str(y)])
    
    def swipe(self, x1, y1, x2, y2, duration=1000):
        """滑动屏幕"""
        return self.execute_command([
            "shell", "input", "swipe", 
            str(x1), str(y1), str(x2), str(y2), str(duration)
        ])
    
    def input_text(self, text):
        """输入文本"""
        # 转义特殊字符
        escaped_text = text.replace(" ", "%s").replace("&", "\\&")
        return self.execute_command(["shell", "input", "text", escaped_text])
    
    def press_key(self, keycode):
        """按键"""
        return self.execute_command(["shell", "input", "keyevent", str(keycode)])
    
    def get_screen_size(self):
        """获取屏幕尺寸"""
        output = self.execute_command(["shell", "wm", "size"])
        if output:
            # 解析输出: Physical size: 1080x2340
            size_str = output.split(": ")[1]
            width, height = map(int, size_str.split("x"))
            return width, height
        return None, None
    
    def get_installed_packages(self):
        """获取已安装的应用包名"""
        output = self.execute_command(["shell", "pm", "list", "packages"])
        if output:
            packages = []
            for line in output.split('\n'):
                if line.startswith('package:'):
                    package_name = line.replace('package:', '')
                    packages.append(package_name)
            return packages
        return []

# 使用示例
adb = ADBAutomation()

# 获取设备列表
devices = adb.get_devices()
print(f"连接的设备: {devices}")

if devices:
    # 使用第一个设备
    adb = ADBAutomation(devices[0])
    
    # 获取屏幕尺寸
    width, height = adb.get_screen_size()
    print(f"屏幕尺寸: {width}x{height}")
    
    # 截屏
    if adb.screenshot("screenshot.png"):
        print("截屏成功")
    
    # 点击屏幕中心
    if width and height:
        adb.tap(width // 2, height // 2)
        print("点击屏幕中心")
```

---

## 🔗 相关链接

- [[命令行工具与脚本]]
- [[Python 详细指南]]
- [[AI 辅助开发]]
- [[系统管理与运维]]

---

*最后更新：2025-06-16*
