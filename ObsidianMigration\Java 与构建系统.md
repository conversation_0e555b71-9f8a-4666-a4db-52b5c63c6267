# Java 与构建系统

> Java 开发环境、构建工具与编译系统详解

## 📋 目录

- [[#Java 开发环境]]
- [[#构建系统基础]]
- [[#编译工具链]]
- [[#项目管理]]

---

## Java 开发环境

### OpenJDK 版本管理

> 参考：[[OpenJDK 下载与配置]]

#### 主要发行版

**官方下载源**：
- [OpenLogic OpenJDK](https://www.openlogic.com/openjdk-downloads)
- [Microsoft OpenJDK](https://www.microsoft.com/openjdk)
- [Eclipse Adoptium](https://adoptium.net/)
- [Amazon Corretto](https://aws.amazon.com/corretto/)

#### 版本对照表

| JDK版本 | 内部版本 | 发布时间 | LTS | 下载链接 |
|---------|----------|----------|-----|----------|
| **JDK 8** | 1.8.0 | 2014-03 | ✅ | [OpenLogic JDK 8u352](https://builds.openlogic.com/downloadJDK/openlogic-openjdk/8u352-b08/openlogic-openjdk-8u352-b08-windows-x64.zip) |
| **JDK 11** | 11.0.17 | 2018-09 | ✅ | [Microsoft JDK 11.0.17](https://aka.ms/download-jdk/microsoft-jdk-11.0.17-windows-x64.zip) |
| **JDK 17** | 17.0.5 | 2021-09 | ✅ | [Microsoft JDK 17.0.5](https://aka.ms/download-jdk/microsoft-jdk-17.0.5-windows-x64.zip) |
| **JDK 21** | 21.0.1 | 2023-09 | ✅ | [Microsoft JDK 21.0.1](https://aka.ms/download-jdk/microsoft-jdk-21.0.1-windows-x64.zip) |

#### 环境配置

**Windows 环境变量设置**：
```cmd
# 设置 JAVA_HOME
set JAVA_HOME=C:\Program Files\Microsoft\jdk-17.0.5

# 添加到 PATH
set PATH=%JAVA_HOME%\bin;%PATH%

# 验证安装
java -version
javac -version
```

**Linux 环境配置**：
```bash
# 解压 JDK
sudo tar -xzf openjdk-17.0.5_linux-x64_bin.tar.gz -C /opt/

# 设置环境变量
echo 'export JAVA_HOME=/opt/jdk-17.0.5' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 验证安装
java -version
```

#### 多版本管理

**SDKMAN 使用**：
```bash
# 安装 SDKMAN
curl -s "https://get.sdkman.io" | bash
source "$HOME/.sdkman/bin/sdkman-init.sh"

# 列出可用的 Java 版本
sdk list java

# 安装特定版本
sdk install java 17.0.5-ms
sdk install java 11.0.17-ms
sdk install java 8.0.352-open

# 切换版本
sdk use java 17.0.5-ms
sdk default java 17.0.5-ms

# 查看当前版本
sdk current java
```

**jenv 使用**（macOS/Linux）：
```bash
# 安装 jenv
brew install jenv

# 配置 shell
echo 'export PATH="$HOME/.jenv/bin:$PATH"' >> ~/.zshrc
echo 'eval "$(jenv init -)"' >> ~/.zshrc

# 添加 Java 版本
jenv add /Library/Java/JavaVirtualMachines/jdk-17.0.5.jdk/Contents/Home
jenv add /Library/Java/JavaVirtualMachines/jdk-11.0.17.jdk/Contents/Home

# 设置全局版本
jenv global 17.0.5

# 设置项目特定版本
cd my-project
jenv local 11.0.17
```

---

## 构建系统基础

### 编译过程基本概念

> 参考：[[构建系统基本概念]]

#### 编译工具链组件

**核心工具说明**：

| 工具 | 功能 | 输入 | 输出 |
|------|------|------|------|
| **CC** | 编译器 | C源文件 | 汇编文件 |
| **AS** | 汇编器 | 汇编文件 | 目标文件 |
| **AR** | 打包器 | 目标文件 | 静态库 |
| **LD** | 链接器 | 目标文件 | 可执行文件/动态库 |
| **GDB** | 调试器 | 可执行文件 | 调试会话 |
| **STRIP** | 符号剥离 | 可执行文件 | 精简的可执行文件 |
| **NM** | 符号查看 | 静态库 | 符号表 |
| **Objdump** | 反汇编 | 目标文件 | 汇编代码 |

#### 编译选项详解

**常用编译选项**：

```bash
# 基本编译
gcc -o program source.c

# 调试信息
gcc -g -o program source.c

# 优化级别
gcc -O0 -o program source.c  # 无优化
gcc -O1 -o program source.c  # 基本优化
gcc -O2 -o program source.c  # 标准优化
gcc -O3 -o program source.c  # 高级优化
gcc -Os -o program source.c  # 大小优化

# 警告控制
gcc -Wall -Wextra -Werror -o program source.c

# 头文件和库
gcc -I/usr/include/custom -L/usr/lib/custom -lcustom -o program source.c

# 位置无关代码（动态库必需）
gcc -fPIC -shared -o libexample.so source.c

# 宏定义
gcc -DDEBUG=1 -DVERSION=\"1.0\" -o program source.c

# 链接器选项
gcc -Wl,--as-needed -Wl,-rpath,/usr/local/lib -o program source.c
```

**高级编译选项**：

```bash
# 静态链接
gcc -static -o program source.c

# 生成依赖文件
gcc -MMD -MP -o program source.c

# 预处理输出
gcc -E source.c > preprocessed.i

# 汇编输出
gcc -S source.c  # 生成 source.s

# 目标文件
gcc -c source.c  # 生成 source.o

# 交叉编译
arm-linux-gnueabihf-gcc -o program source.c

# 链接时优化
gcc -flto -o program source1.c source2.c

# 地址消毒器
gcc -fsanitize=address -g -o program source.c

# 线程消毒器
gcc -fsanitize=thread -g -o program source.c
```

### Makefile 详细使用

> 参考：[[Makefile 编写指南]]

#### 基础语法

**基本结构**：
```makefile
# 变量定义
CC = gcc
CFLAGS = -Wall -Wextra -O2
LDFLAGS = -lm
SRCDIR = src
OBJDIR = obj
BINDIR = bin

# 源文件和目标文件
SOURCES = $(wildcard $(SRCDIR)/*.c)
OBJECTS = $(SOURCES:$(SRCDIR)/%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/program

# 默认目标
.PHONY: all clean install

all: $(TARGET)

# 链接规则
$(TARGET): $(OBJECTS) | $(BINDIR)
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)

# 编译规则
$(OBJDIR)/%.o: $(SRCDIR)/%.c | $(OBJDIR)
	$(CC) $(CFLAGS) -c $< -o $@

# 创建目录
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

# 清理
clean:
	rm -rf $(OBJDIR) $(BINDIR)

# 安装
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/
```

#### 高级特性

**条件编译**：
```makefile
# 检测操作系统
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
    CFLAGS += -DLINUX
endif
ifeq ($(UNAME_S),Darwin)
    CFLAGS += -DMACOS
endif

# 调试/发布模式
DEBUG ?= 0
ifeq ($(DEBUG), 1)
    CFLAGS += -g -DDEBUG
else
    CFLAGS += -O2 -DNDEBUG
endif
```

**函数使用**：
```makefile
# 自定义函数
define compile_template
$(1): $(2)
	@echo "Compiling $$@"
	$(CC) $(CFLAGS) -c $$< -o $$@
endef

# 使用函数
$(eval $(call compile_template,$(OBJDIR)/main.o,$(SRCDIR)/main.c))

# 内置函数
SOURCES := $(shell find $(SRCDIR) -name "*.c")
HEADERS := $(patsubst %.c,%.h,$(SOURCES))
UPPERCASE := $(shell echo $(TARGET) | tr a-z A-Z)
```

**并行构建**：
```makefile
# 支持并行构建
.NOTPARALLEL: clean

# 依赖关系
$(OBJDIR)/main.o: $(SRCDIR)/main.c $(SRCDIR)/common.h
$(OBJDIR)/utils.o: $(SRCDIR)/utils.c $(SRCDIR)/utils.h $(SRCDIR)/common.h

# 自动依赖生成
DEPDIR := .deps
DEPFLAGS = -MT $@ -MMD -MP -MF $(DEPDIR)/$*.d

$(OBJDIR)/%.o: $(SRCDIR)/%.c $(DEPDIR)/%.d | $(DEPDIR)
	$(CC) $(DEPFLAGS) $(CFLAGS) -c $< -o $@

$(DEPDIR): ; @mkdir -p $@

DEPFILES := $(SOURCES:$(SRCDIR)/%.c=$(DEPDIR)/%.d)
$(DEPFILES):

include $(wildcard $(DEPFILES))
```

---

## 编译工具链

### GCC 高级使用

#### 编译器内置功能

**内置宏查看**：
```bash
# 查看预定义宏
gcc -dM -E - < /dev/null

# 查看特定架构的宏
gcc -march=native -dM -E - < /dev/null

# 查看头文件搜索路径
gcc -v -E - < /dev/null 2>&1 | grep -A 20 "search starts here"

# 查看库搜索路径
gcc -print-search-dirs

# 查看默认链接器脚本
ld --verbose
```

**编译器特性检测**：
```c
// feature_test.c
#include <stdio.h>

int main() {
    printf("GCC Version: %d.%d.%d\n", 
           __GNUC__, __GNUC_MINOR__, __GNUC_PATCHLEVEL__);
    
#ifdef __x86_64__
    printf("Architecture: x86_64\n");
#endif

#ifdef __SSE2__
    printf("SSE2 Support: Yes\n");
#endif

#ifdef __AVX2__
    printf("AVX2 Support: Yes\n");
#endif

    return 0;
}
```

#### 交叉编译

**ARM 交叉编译**：
```bash
# 安装交叉编译工具链
sudo apt-get install gcc-arm-linux-gnueabihf

# 编译 ARM 程序
arm-linux-gnueabihf-gcc -o program_arm source.c

# 查看目标文件信息
file program_arm
readelf -h program_arm
```

**自定义工具链**：
```makefile
# Makefile for cross compilation
CROSS_COMPILE ?= arm-linux-gnueabihf-
CC = $(CROSS_COMPILE)gcc
AR = $(CROSS_COMPILE)ar
STRIP = $(CROSS_COMPILE)strip

ARCH_CFLAGS = -march=armv7-a -mfpu=neon
CFLAGS += $(ARCH_CFLAGS)

program: source.c
	$(CC) $(CFLAGS) -o $@ $<
	$(STRIP) $@
```

### 静态分析工具

**Clang Static Analyzer**：
```bash
# 使用 scan-build
scan-build make

# 直接使用 clang
clang --analyze source.c

# 生成 HTML 报告
scan-build -o analysis_results make
```

**其他静态分析工具**：
```bash
# Cppcheck
cppcheck --enable=all --xml source.c 2> report.xml

# PVS-Studio
pvs-studio-analyzer trace -- make
pvs-studio-analyzer analyze
plog-converter -a GA:1,2 -t tasklist PVS-Studio.log
```

---

## 项目管理

### Maven 项目管理

#### 基本项目结构

```
my-project/
├── pom.xml
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/example/App.java
│   │   └── resources/
│   └── test/
│       ├── java/
│       │   └── com/example/AppTest.java
│       └── resources/
└── target/
```

**pom.xml 配置**：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.example</groupId>
    <artifactId>my-project</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

### Gradle 构建系统

**build.gradle 配置**：
```gradle
plugins {
    id 'java'
    id 'application'
}

group = 'com.example'
version = '1.0.0'

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    testImplementation 'junit:junit:4.13.2'
}

application {
    mainClass = 'com.example.App'
}

tasks.named('test') {
    useJUnitPlatform()
}
```

---

## 🔗 相关链接

- [[编程语言与框架]]
- [[编译器技术与程序分析]]
- [[开发工具详细指南]]
- [[系统管理与运维]]

---

*最后更新：2025-06-16*
