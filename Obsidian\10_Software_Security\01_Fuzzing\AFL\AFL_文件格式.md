# AFL 文件格式

## 概述

<GeminiOptimizationFrom> Note/AFL/输入输出文件格式.md </GeminiOptimizationFrom>

AFL (American Fuzzy Lop) 在运行过程中会生成多种文件和目录，用于存储测试用例、统计信息和分析结果。理解这些文件格式对于有效使用 AFL 和分析测试结果至关重要。

## 参考资料

- Fuzzing 理论基础：https://sayfer.io/blog/fuzzing-part-1-the-theory
- AFL 实践指南：https://sayfer.io/blog/fuzzing-part-2-fuzzing-with-afl/
- 备份链接：
  - https://web.archive.org/web/20230626140725/https://sayfer.io/blog/fuzzing-part-1-the-theory/
  - https://web.archive.org/web/20230626140523/https://sayfer.io/blog/fuzzing-part-2-fuzzing-with-afl/

## AFL 状态显示

### 实时状态界面 (`show_stats`)

```
                        american fuzzy lop 2.57b (gzip)

┌─ process timing ─────────────────────────────────────┬─ overall results ─────┐
│        run time : 0 days, 0 hrs, 0 min, 29 sec       │  cycles done : 1      │
│   last new path : none yet (odd, check syntax!)      │  total paths : 10     │
│ last uniq crash : none seen yet                      │ uniq crashes : 0      │
│  last uniq hang : none seen yet                      │   uniq hangs : 0      │
├─ cycle progress ────────────────────┬─ map coverage ─┴───────────────────────┤
│  now processing : 3* (30.00%)       │    map density : 0.59% / 0.59%         │
│ paths timed out : 0 (0.00%)         │ count coverage : 1.00 bits/tuple       │
├─ stage progress ────────────────────┼─ findings in depth ────────────────────┤
│  now trying : splice 1              │ favored paths : 1 (10.00%)             │
│ stage execs : 11/32 (34.38%)        │  new edges on : 1 (10.00%)             │
│ total execs : 17.2k                 │ total crashes : 0 (0 unique)           │
│  exec speed : 570.9/sec             │  total tmouts : 0 (0 unique)           │
├─ fuzzing strategy yields ───────────┴───────────────┬─ path geometry ────────┤
│   bit flips : 0/320, 0/310, 0/290                   │    levels : 1          │
│  byte flips : 0/40, 0/30, 0/10                      │   pending : 0          │
│ arithmetics : 0/2229, 0/325, 0/0                    │  pend fav : 0          │
│  known ints : 0/257, 0/835, 0/440                   │ own finds : 0          │
│  dictionary : 0/0, 0/0, 0/0                         │  imported : n/a        │
│       havoc : 0/10.5k, 0/1320                       │ stability : 100.00%    │
│        trim : 99.61%/190, 0.00%                     ├────────────────────────┘
──────────────────────────────────────────────────────┘          [cpu000:  2%]
```

### 状态字段说明

#### 进程时间统计
- **run time**: AFL 运行的总时间
- **last new path**: 最后发现新路径的时间
- **last uniq crash**: 最后发现唯一崩溃的时间
- **last uniq hang**: 最后发现唯一挂起的时间

#### 整体结果
- **cycles done**: 完成的循环数
- **total paths**: 发现的总路径数
- **uniq crashes**: 唯一崩溃数
- **uniq hangs**: 唯一挂起数

#### 覆盖率信息
- **map density**: 覆盖率密度
- **count coverage**: 计数覆盖率

#### 变异策略统计
- **bit flips**: 位翻转变异的统计
- **byte flips**: 字节翻转变异的统计
- **arithmetics**: 算术变异的统计
- **known ints**: 已知整数变异的统计
- **dictionary**: 字典变异的统计
- **havoc**: 随机变异的统计

## 输出目录结构

![AFL 输出目录结构](https://qiniu.maikebuke.com/image-20230626215628991.png)

### 目录和文件说明

#### 核心目录

- **`crashes`**: 存储能够造成测试样本崩溃的输入用例的目录。文件名称包含以下内容：
  - 崩溃的内核信号
  - AFL 用于创建导致崩溃的输入文件的 ID
  - AFL 开始运行以来经过的时间
  - 用于从其种子生成输入的突变

- **`hangs`**: 存储造成程序挂起或超时的输入用例的目录

- **`queue`**: 当前时刻所有等待尝试的输入文件

#### 辅助文件

- **`.cur_input`**: 本质上，AFL 会将启动命令中的 `@@` 替换为 `.cur_input` 的绝对路径，该文件为当前的输入文件

- **`cmdline`**: 启动 AFL 使用的命令

- **`fuzz_bitmap`**: bitmap 文件，记录代码覆盖率信息

- **`fuzzer_stats`**: 关于 fuzzing 过程中的一些统计信息，人类可读格式

- **`plot_data`**: fuzzing 过程中的信息，用于 plot 数据可视化

## 文件命名规则

### 崩溃文件命名

崩溃文件通常按以下格式命名：
```
id:XXXXXX,sig:XX,src:XXXXXX,time:XXXXXXX,op:XXXX
```

- **id**: 测试用例 ID
- **sig**: 信号编号（如 SIGSEGV=11）
- **src**: 源测试用例 ID
- **time**: 发现时间戳
- **op**: 使用的变异操作

### 队列文件命名

队列文件通常按以下格式命名：
```
id:XXXXXX,orig:filename
id:XXXXXX,src:XXXXXX,time:XXXXXXX,op:XXXX
```

## 分析工具

### 内置工具

- **afl-whatsup**: 查看多个 AFL 实例的状态
- **afl-plot**: 生成性能图表
- **afl-cmin**: 最小化测试用例集合
- **afl-tmin**: 最小化单个测试用例

### 第三方工具

- **afl-utils**: AFL 辅助工具集
- **afl-cov**: 代码覆盖率分析
- **aflplusplus**: AFL 的增强版本

## 相关主题

- [[AFL_使用指南]] - AFL 基础使用方法
- [[AFL_开发笔记]] - AFL 开发注意事项
- [[QEMU_插件机制]] - QEMU 模式下的 AFL
