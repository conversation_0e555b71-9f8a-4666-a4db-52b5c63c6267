# QEMU 时间流速控制

---



`QEMU` 虚拟机中的所有时间都是通过 `int64_t qemu_clock_get_ns(QEMUClockType type)` 函数实现的, 包含三种类型的时间:

- QEMU_CLOCK_REALTIME
- QEMU_CLOCK_VIRTUAL
- QEMU_CLOCK_HOST
- QEMU_CLOCK_VIRTUAL_RT

时间缩放, 原理为, 在虚拟机 "启动" 时保存虚拟机的时间作为 `base_time`, 然后后续在获取时间时, 通过当前时间 `clock` 与 `base_time` 的差值 , 获取时间差 `delta`, 然后通过例如 `base_time + delta / 2` 的方式返回当前经过 "缩放" 后的时间

我的实现方式如下:

```c
// 全局变量
struct QEMUTimeScaline {
    int64_t realtime;
    int64_t virtual;
    int64_t host;
    int64_t virtual_rt;
} base_time;

int64_t qemu_clock_get_ns(QEMUClockType type)
{
    int64_t clock, base, delta;
    switch (type) {
    case QEMU_CLOCK_REALTIME:
        clock = get_clock();
        if (unlikely(clock < base_time.realtime || !base_time.realtime)) {
            base_time.realtime = clock;
        }
        base = base_time.realtime;
        break;
    default:
    case QEMU_CLOCK_VIRTUAL:
        clock = cpus_get_virtual_clock();
        if (unlikely(clock < base_time.virtual || !base_time.virtual)) {
            base_time.virtual = clock;
        }
        base = base_time.virtual;
        break;
    case QEMU_CLOCK_HOST:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_HOST, get_clock_realtime());
        if (unlikely(clock < base_time.host || !base_time.host)) {
            base_time.host = clock;
        }
        base = base_time.host;
        break;
    case QEMU_CLOCK_VIRTUAL_RT:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_VIRTUAL_RT, cpu_get_clock());
        if (unlikely(clock < base_time.virtual_rt || !base_time.virtual_rt)) {
            base_time.virtual_rt = clock;
        }
        base = base_time.virtual_rt;
        break;
    }
    delta = clock - base;
    return base + (delta >> 3);
}
```

或者更简洁的:

```c
int64_t base_time[4];

int64_t qemu_clock_get_ns(QEMUClockType type)
{
    int64_t clock, base, delta;
    switch (type) {
    case QEMU_CLOCK_REALTIME:
        clock = get_clock();
        break;
    default:
    case QEMU_CLOCK_VIRTUAL:
        clock = cpus_get_virtual_clock();
        break;
    case QEMU_CLOCK_HOST:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_HOST, get_clock_realtime());
        break;
    case QEMU_CLOCK_VIRTUAL_RT:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_VIRTUAL_RT, cpu_get_clock());
        break;
    }
    if ( unlikely(clock < base_time[type] || !base_time[type]) ) {
        base_time[type] = clock;
    }
    base = base_time[type];
    delta = clock - base;
    return base + (delta >> 3);
}
```

值得注意的是, 该实现有 bug, 不能应对重复加载快照的情况. 



