# Clang 命令参考

## 概述

Clang 是一个基于 LLVM 的 C/C++/Objective-C 编译器前端，提供了优秀的错误诊断和快速编译速度。

## AST 操作

### 导出抽象语法树 (AST)

<GeminiOptimizationFrom> Note/Compiler/Clang 导出 AST.md </GeminiOptimizationFrom>

使用 Clang 导出抽象语法树：

```shell
clang -Xclang -ast-dump -fsyntax-only <source_file>
```

**参数说明：**
- `-Xclang`: 将参数传递给 Clang 前端
- `-ast-dump`: 输出抽象语法树
- `-fsyntax-only`: 仅进行语法检查，不生成目标代码

## 常用编译选项

### 基本编译
```shell
# 基本编译
clang -o output source.c

# 启用调试信息
clang -g -o output source.c

# 优化编译
clang -O2 -o output source.c
```

### 静态分析
```shell
# 启用静态分析器
clang --analyze source.c

# 详细分析报告
clang --analyze -Xanalyzer -analyzer-output=html -o analysis_results source.c
```

## 相关工具

- [[GCC_命令参考]] - GCC 编译器命令参考
- [[LLVM_概述]] - LLVM 工具链概述
- [[编译工具链]] - 完整编译工具链概述
