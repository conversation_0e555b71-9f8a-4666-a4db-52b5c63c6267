# 关于我理解的LLVM

---



关于`LLVM`的一些重要网站

> https://llvm.org/
>
> https://llvm.org/docs/
>
> https://github.com/llvm/llvm-project
>
> https://gitee.com/mirrors/llvm-project



## 啥是 LLVM？

LLVM 是一个现代的`可重用编译器`(reusable compiler)以及`工具链`(toolchain)技术集合. 

> LLVM(*Low Level Virtual Machine*), 即低层次虚拟机, 虽然它已经和虚拟机没有任何关系.

LLVM包含以下几个子项目:

| Name                               | 说明                                                         |
| ---------------------------------- | ------------------------------------------------------------ |
| The LLVM Core libraries            | LLVM 核心库提供了独立于源码与目标架构的现代化优化器(optimizer), 这些库围绕着LLVM 中间语言(LLVM Intermediate Representation)代码进行构建. |
| Clang                              | Clang 是"LLVM 原生C/C++/Objective-C"编译器. 其中包含了"[Clang 静态分析器](https://clang-analyzer.llvm.org/)"与"[clang-tidy](https://clang.llvm.org/extra/clang-tidy/)"用于自动发现代码中的 bug. 也可以使用 Clang 前端库, 对 C/C++ 代码进行解析. |
| The LLDB project                   | LLDB 是一个高性能调试器(Debugger), 建立在 LLVM 与 Clang 之上. 同时使用了 Clang AST, 表达式语法分析器(Parser), LLVM JIT(运行时), LLVM disassembler 等, 比 GDB 快的多. |
| The libc++ and libc++ ABI projects | 提供了与标准库中一致性的高性能实现, 对 C++11 和 C++14 全面支持. |
| compiler-rt                        | 提供了高性能的底层代码生成器支持组件, 同时提供了一些运行时测试工具(AddressSanitizer, ThreadSanitizer, MemorySanitizer, DataFlowSanitizer) |
| MLIR                               |                                                              |
| OpenMP                             |                                                              |
| The polly project                  |                                                              |
| libclc                             | 实现一个 OpenCL 标准库                                       |
| klee                               | 实现了一个"符号虚拟机"(symbolic virtual machine), 即符号执行技术. Klee 能够在检测到错误时生成对应的测试用例. |
| LLD                                | 是一个新的链接器(Linker), 是系统默认链接器的替代品, 速度会快很多. |
| BOLT                               | 是一个"链接后端优化器"(post-link optimizer)                  |









