# LLVM

---



## 安装

```shell
# 安装一些开发的常见依赖项
sudo apt-get install build-essential git python2.7 cmake libedit-dev libxml2-dev
# 拉取 LLVM 最新代码
git clone https://github.com/llvm/llvm-project.git -b llvmorg-10.0.1
# 使用 cmake 生成编译的 Makefile
cd llvm-project && mkdir build && cd build && cmake ../llvm -DCMAKE_BUILD_TYPE=Debug -DLLVM_ENABLE_PROJECTS="clang" -DLLVM_ENABLE_RUNTIMES="libcxx;libcxxabi" -DCMAKE_INSTALL_PREFIX=${HOME}/.local/llvm/10.0/
# 编译 LLVM, 此过程非常耗时且极度吃内存, 如果物理内存不够的话请创建 Swap Space (虚拟内存)
make -j16
# 安装
make install
```

**创建 Swap Space**

```shell
# https://docs.oracle.com/cd/E24457_01/html/E21988/giprn.html
# 创建一个存放 swapfile 的目录
sudo mkdir /swapfile && cd /swapfile/
# 在选定的目录中创建 Swap Space, 新的交换区大小为 30G
sudo dd if=/dev/zero of=<swapfile> bs=1024 count=30000000
# 初始化指定的 Swap Space
sudo mkswap -f <swapfile>
# 使用 swapon 启用新的 Swap Space
sudo swapon <swapfile>
# 查看交换空间是否添加成功
swapon -s

# 取消挂载 Swap Space
swapoff <swapfile>
```



示例代码:

```c
#include <stdio.h>

unsigned long fibonacci(int n) {
 if (n == 0 || n == 1)
  return n;
 else
  return fibonacci(n - 1) + fibonacci(n - 2);
}

int main(int argc, char* argv[]) {
 int num;
 printf("Please enter a positive integer: ");
 scanf("%d", &num);
 printf("Fibonacci number at index %d is %lu", num, fibonacci(num));
 return 0;
}
```



```shell
clang -S -emit-llvm <file>.c -o <file>.bc
opt -load ./LLVMBirkhoff.so -hello fibonacci.bc > fibonacci_opt.bc
llvm-dis fibonacci_opt.bc -o fibonacci_opt.ll
```





```
clang -S -emit-llvm fibonacci.c -o - | llc -o fibonacci.s -O 3
```





## LLVM 中间语言

| From | To   | Command                         |
| ---- | ---- | ------------------------------- |
| .c   | .ll  | clang -emit-llvm -S a.c -o a.ll |
| .c   | .bc  | clang -emit-llvm -c a.c -o a.bc |
| .ll  | .bc  | llvm-as a.ll -o a.bc            |
| .bc  | .ll  | llvm-dis a.bc -o a.ll           |
| .bc  | .s   | llc a.bc -o a.s                 |
| .bc  | .o   | llc -filetype=obj a.bc          |
| .s   | .o   | clang -c <file>.s -o <file>.o   |



完整的编译过程如下：

```shell
clang -Wall -Wextra -g -emit-llvm -S <file>.c -o <file>.ll
opt -enable-new-pm=0 -load <LLVM_PASS>.so -<PASS_FLAG> <file>.ll -o <file_opt>.bc
llvm-dis <file_opt>.bc -o <file_opt>.ll
llc <file_opt>.ll -o <file_opt>.s
clang -c <file_opt>.s -o <file_opt>.o
clang <file_opt>.o -o <file_opt>.elf
```





## LLVM Pass

参考[官方文档]()编写一个简单的`LLVM Pass`, 代码如下:

```c++
#include "llvm/ADT/Statistic.h"
#include "llvm/IR/Function.h"
#include "llvm/Pass.h"
#include "llvm/Support/raw_ostream.h"
#include "llvm/Transforms/IPO/PassManagerBuilder.h"
using namespace llvm;

#define DEBUG_TYPE "hello"

STATISTIC(HelloCounter, "Counts number of functions greeted");

namespace {
  struct Hello : public FunctionPass {
    static char ID;
    Hello() : FunctionPass(ID) {}

    bool runOnFunction(Function &F) override {
      ++HelloCounter;
      errs() << "Birkhoff> ";
      errs().write_escaped(F.getName()) << '\n';
      return false;
    }
  };
}

char Hello::ID = 0;
// 为 opt 注册
static RegisterPass<Hello> X("funcname", "Hello World Pass");
// 为 clang 注册, 在高版本中不可用, 测试在10.0.0版本中可用
static RegisterStandardPasses Y(PassManagerBuilder::EP_EarlyAsPossible,
                                [](const PassManagerBuilder& Builder,
                                   legacy::PassManagerBase& PM) {
                                  PM.add(new Hello());
                                });
```



使用如下命令生成`LLVM Pass`

```shell
clang `llvm-config --cxxflags` -Wl,-znodelete -fno-rtti -fPIC -shared Hello.cpp -o LLVMHello.so `llvm-config --ldflags`
```

各个参数的含义如下:

| 参数                     | 说明                                                         |
| ------------------------ | ------------------------------------------------------------ |
| `llvm-config --cxxflags` | 提供了`CXXFLAGS`与`LDFLAGS`参数方便查找LLVM的头文件与库文件。 如果链接有问题，还可以用`llvm-config --libs`提供动态链接的LLVM库。 |
| `-fPIC -shared`          | 编译动态库所需的必要参数                                     |
| `-Wl,-znodelete`         | 主要是为了应对LLVM 5.0+中加载ModulePass引起segmentation fault的bug。如果你的Pass继承了ModulePass，还请务必加上。 |



使用`LLVM Pass`

```shell
# opt, -enable-new-pm=0选项在高版本中需要指定
opt -enable-new-pm=0 -load LLVMBirkhoff.so -hello fibonacci.bc
# clang
clang -Xclang -load -Xclang ./LLVMBirkhoff.so fibonacci.c -o fibonacci.elf
```



https://zhuanlan.zhihu.com/p/122522485

https://github.com/imdea-software/LLVM_Instrumentation_Pass/blob/master/InstrumentFunctions/Pass.cpp

https://github.com/numba/llvmlite



## CFG

`.dot`转`.png`命令: `dot -Tpng <name>.dot -o <name>.png`



## clang 预处理阶段

`clang -E main.c -o main_preprocessed.c -DMY_DEFINE=1 -C`



## 参考

> https://zhuanlan.zhihu.com/p/122522485
>
> https://zhuanlan.zhihu.com/p/392381317
>
> https://www.cs.cornell.edu/~asampson/blog/llvm.html
>
> https://clang.llvm.org/docs/ClangPlugins.html
>
> https://github.com/sampsyo/llvm-pass-skeleton/tree/rtlib