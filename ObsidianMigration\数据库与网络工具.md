# 数据库与网络工具

> 数据库管理、网络安全与证书管理工具集

## 📋 目录

- [[#图数据库]]
- [[#关系型数据库]]
- [[#证书管理]]
- [[#网络代理]]
- [[#网络分析]]

---

## 图数据库

### Neo4j 详细使用

> 参考：[[Neo4j 使用说明]]

#### 安装配置

**下载安装**：
- [Neo4j 5.3.0 Linux](https://dist.neo4j.org/neo4j-community-5.3.0-unix.tar.gz)
- [Neo4j 5.3.0 Windows](https://dist.neo4j.org/neo4j-community-5.3.0-windows.zip)
- 依赖：Java 17
- [Microsoft Build OpenJDK](https://aka.ms/download-jdk/microsoft-jdk-17.0.5-linux-x64.tar.gz)

**配置文件**：
编辑 `${NEO4J}/conf/neo4j.conf`：
```config
# 允许外部访问
server.default_listen_address=0.0.0.0

# 设置内存
server.memory.heap.initial_size=512m
server.memory.heap.max_size=2G
server.memory.pagecache.size=1G

# 设置数据目录
server.directories.data=/var/lib/neo4j/data
server.directories.logs=/var/log/neo4j

# 启用 APOC 插件
dbms.security.procedures.unrestricted=apoc.*
dbms.security.procedures.allowlist=apoc.*
```

**启动服务**：
```bash
# 控制台模式启动
${NEO4J}/bin/neo4j console

# 后台服务模式
${NEO4J}/bin/neo4j start
${NEO4J}/bin/neo4j stop
${NEO4J}/bin/neo4j restart

# 查看状态
${NEO4J}/bin/neo4j status
```

**Web 界面访问**：
- 浏览器访问：`http://<ip>:7474`
- 默认用户名/密码：`neo4j/neo4j`

#### Python 接口详解

**安装依赖**：
```bash
pip install py2neo neo4j-driver
```

**基础操作示例**：
```python
from py2neo import Graph, Node, Relationship, Subgraph, NodeMatcher
import json

class Neo4jManager:
    def __init__(self, uri="neo4j://localhost:7687", auth=("neo4j", "neo4j")):
        self.graph = Graph(uri, auth=auth)
    
    def clear_database(self):
        """清空数据库"""
        self.graph.delete_all()
        print("数据库已清空")
    
    def create_person_network(self):
        """创建人员关系网络"""
        # 创建人员节点
        alice = Node("Person", name="Alice", age=30, city="Beijing")
        bob = Node("Person", name="Bob", age=25, city="Shanghai")
        charlie = Node("Person", name="Charlie", age=35, city="Guangzhou")
        
        # 创建公司节点
        company_a = Node("Company", name="TechCorp", industry="Technology")
        company_b = Node("Company", name="DataInc", industry="Analytics")
        
        # 创建关系
        alice_knows_bob = Relationship(alice, "KNOWS", bob, since="2020")
        bob_knows_charlie = Relationship(bob, "KNOWS", charlie, since="2019")
        alice_works_for = Relationship(alice, "WORKS_FOR", company_a, position="Engineer")
        bob_works_for = Relationship(bob, "WORKS_FOR", company_b, position="Analyst")
        
        # 批量创建
        subgraph = Subgraph(
            nodes=[alice, bob, charlie, company_a, company_b],
            relationships=[alice_knows_bob, bob_knows_charlie, 
                          alice_works_for, bob_works_for]
        )
        
        self.graph.create(subgraph)
        print("人员关系网络创建完成")
    
    def query_examples(self):
        """查询示例"""
        # Cypher 查询
        results = self.graph.run("""
            MATCH (p:Person)-[:KNOWS]->(friend:Person)
            RETURN p.name as person, friend.name as friend
        """)
        
        print("朋友关系:")
        for record in results:
            print(f"{record['person']} 认识 {record['friend']}")
        
        # 使用 NodeMatcher
        matcher = NodeMatcher(self.graph)
        
        # 查找所有人员
        persons = list(matcher.match("Person"))
        print(f"\n找到 {len(persons)} 个人员:")
        for person in persons:
            print(f"- {person['name']}, {person['age']}岁, 来自{person['city']}")
        
        # 查找特定条件的节点
        tech_workers = list(matcher.match("Person").where(age__gte=30))
        print(f"\n30岁以上的人员:")
        for person in tech_workers:
            print(f"- {person['name']}")
    
    def update_nodes(self):
        """批量更新节点"""
        tx = self.graph.begin()
        
        try:
            matcher = NodeMatcher(self.graph)
            persons = list(matcher.match("Person"))
            
            # 为所有人员添加新属性
            for person in persons:
                person['updated'] = True
                person['last_modified'] = "2025-06-16"
            
            # 创建子图并推送更新
            subgraph = Subgraph(nodes=persons)
            tx.push(subgraph)
            self.graph.commit(tx)
            
            print("节点更新完成")
        except Exception as e:
            self.graph.rollback(tx)
            print(f"更新失败: {e}")
    
    def advanced_queries(self):
        """高级查询示例"""
        # 路径查询
        paths = self.graph.run("""
            MATCH path = (start:Person)-[:KNOWS*1..3]-(end:Person)
            WHERE start.name = 'Alice'
            RETURN path, length(path) as path_length
            ORDER BY path_length
        """)
        
        print("从 Alice 开始的关系路径:")
        for record in paths:
            print(f"路径长度: {record['path_length']}")
        
        # 聚合查询
        stats = self.graph.run("""
            MATCH (p:Person)
            RETURN 
                count(p) as total_persons,
                avg(p.age) as avg_age,
                min(p.age) as min_age,
                max(p.age) as max_age
        """).data()[0]
        
        print(f"\n统计信息:")
        print(f"总人数: {stats['total_persons']}")
        print(f"平均年龄: {stats['avg_age']:.1f}")
        print(f"年龄范围: {stats['min_age']} - {stats['max_age']}")

# 使用示例
if __name__ == "__main__":
    neo4j = Neo4jManager()
    
    # 清空并创建示例数据
    neo4j.clear_database()
    neo4j.create_person_network()
    
    # 执行查询
    neo4j.query_examples()
    neo4j.update_nodes()
    neo4j.advanced_queries()
```

#### Cypher 查询语言

**基本语法**：
```cypher
-- 创建节点
CREATE (p:Person {name: 'John', age: 30})

-- 创建关系
MATCH (a:Person {name: 'Alice'}), (b:Person {name: 'Bob'})
CREATE (a)-[:KNOWS {since: '2020'}]->(b)

-- 查询
MATCH (p:Person)
WHERE p.age > 25
RETURN p.name, p.age
ORDER BY p.age DESC

-- 更新
MATCH (p:Person {name: 'Alice'})
SET p.age = 31, p.city = 'Shanghai'

-- 删除
MATCH (p:Person {name: 'Bob'})
DELETE p
```

---

## 关系型数据库

### SQLite3 高级使用

> 参考：[[SQLite3 使用]]

#### Python SQLite3 封装

```python
import sqlite3
import json
from datetime import datetime
from contextlib import contextmanager

class SQLiteManager:
    def __init__(self, db_path):
        self.db_path = db_path
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        """上下文管理器，自动处理连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """初始化数据库表"""
        with self.get_connection() as conn:
            # 创建用户表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建日志表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    details TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_logs_user_id ON logs(user_id)')
            
            conn.commit()
    
    def create_user(self, username, email, password_hash):
        """创建用户"""
        with self.get_connection() as conn:
            try:
                cursor = conn.execute(
                    'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
                    (username, email, password_hash)
                )
                conn.commit()
                return cursor.lastrowid
            except sqlite3.IntegrityError as e:
                raise ValueError(f"用户创建失败: {e}")
    
    def get_user(self, username=None, user_id=None):
        """获取用户信息"""
        with self.get_connection() as conn:
            if user_id:
                cursor = conn.execute('SELECT * FROM users WHERE id = ?', (user_id,))
            elif username:
                cursor = conn.execute('SELECT * FROM users WHERE username = ?', (username,))
            else:
                raise ValueError("必须提供 username 或 user_id")
            
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def log_action(self, user_id, action, details=None):
        """记录用户操作"""
        with self.get_connection() as conn:
            details_json = json.dumps(details) if details else None
            conn.execute(
                'INSERT INTO logs (user_id, action, details) VALUES (?, ?, ?)',
                (user_id, action, details_json)
            )
            conn.commit()
    
    def get_user_logs(self, user_id, limit=100):
        """获取用户操作日志"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT l.*, u.username 
                FROM logs l
                JOIN users u ON l.user_id = u.id
                WHERE l.user_id = ?
                ORDER BY l.timestamp DESC
                LIMIT ?
            ''', (user_id, limit))
            
            logs = []
            for row in cursor.fetchall():
                log_dict = dict(row)
                if log_dict['details']:
                    log_dict['details'] = json.loads(log_dict['details'])
                logs.append(log_dict)
            
            return logs
    
    def get_statistics(self):
        """获取数据库统计信息"""
        with self.get_connection() as conn:
            # 用户统计
            user_count = conn.execute('SELECT COUNT(*) FROM users').fetchone()[0]
            
            # 日志统计
            log_count = conn.execute('SELECT COUNT(*) FROM logs').fetchone()[0]
            
            # 最近活跃用户
            recent_active = conn.execute('''
                SELECT u.username, COUNT(l.id) as action_count
                FROM users u
                LEFT JOIN logs l ON u.id = l.user_id
                WHERE l.timestamp > datetime('now', '-7 days')
                GROUP BY u.id, u.username
                ORDER BY action_count DESC
                LIMIT 10
            ''').fetchall()
            
            return {
                'total_users': user_count,
                'total_logs': log_count,
                'recent_active_users': [dict(row) for row in recent_active]
            }

# 使用示例
db = SQLiteManager('app.db')

# 创建用户
user_id = db.create_user('alice', '<EMAIL>', 'hashed_password')
print(f"创建用户，ID: {user_id}")

# 记录操作
db.log_action(user_id, 'login', {'ip': '*************', 'user_agent': 'Chrome'})
db.log_action(user_id, 'view_page', {'page': '/dashboard'})

# 查询用户
user = db.get_user(username='alice')
print(f"用户信息: {user}")

# 查询日志
logs = db.get_user_logs(user_id)
print(f"用户操作日志: {logs}")

# 统计信息
stats = db.get_statistics()
print(f"数据库统计: {stats}")
```

---

## 证书管理

### ACME 自动化证书

> 参考：[[ACME 证书管理]]

#### acme.sh 详细配置

**环境变量配置**：
```bash
# DNSPod API 配置
export DP_Id="xxxxxx"
export DP_Key="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Cloudflare API 配置
export CF_Token="your_cloudflare_api_token"
export CF_Account_ID="your_account_id"
export CF_Zone_ID="your_zone_id"

# 阿里云 DNS 配置
export Ali_Key="your_access_key"
export Ali_Secret="your_access_secret"
```

**基本使用**：
```bash
# 设置 acme.sh 路径
ACME_SH=${HOME}/.acme.sh/acme.sh

# 设置默认 CA
${ACME_SH} --set-default-ca --server letsencrypt

# 注册账户
${ACME_SH} --register-account -m <EMAIL>

# 申请证书（DNS 验证）
${ACME_SH} \
    --issue \
    --dns dns_dp \
    -d example.com \
    -d *.example.com \
    --days 90 \
    --keylength 2048 \
    --dnssleep 30 \
    --home /home/<USER>/cert \
    -m <EMAIL>

# 安装证书
${ACME_SH} --install-cert -d example.com \
    --key-file /etc/nginx/ssl/example.com.key \
    --fullchain-file /etc/nginx/ssl/example.com.crt \
    --reloadcmd "systemctl reload nginx"
```

#### Docker 部署

**docker-compose.yml**：
```yaml
version: "3"
services:
  acme.sh:
    image: neilpang/acme.sh
    container_name: acme.sh
    restart: always
    command: daemon
    environment:
      - DP_Id=xxxxxx
      - DP_Key=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
      - AUTO_UPGRADE=1
    volumes:
      - /etc/nginx/acme/:/acme.sh
      - /etc/nginx/ssl/:/ssl
    network_mode: host
```

**自动化脚本**：
```bash
#!/bin/bash
# 证书自动申请和部署脚本

DOMAIN="example.com"
EMAIL="<EMAIL>"
NGINX_SSL_DIR="/etc/nginx/ssl"
ACME_HOME="/home/<USER>"

# 申请证书
docker exec acme.sh --issue \
    --dns dns_dp \
    -d ${DOMAIN} \
    -d *.${DOMAIN} \
    --home ${ACME_HOME} \
    -m ${EMAIL}

# 安装证书
docker exec acme.sh --install-cert -d ${DOMAIN} \
    --key-file ${NGINX_SSL_DIR}/${DOMAIN}.key \
    --fullchain-file ${NGINX_SSL_DIR}/${DOMAIN}.crt \
    --reloadcmd "nginx -s reload"

echo "证书申请和安装完成"
```

---

## 🔗 相关链接

- [[系统管理与运维]]
- [[开发工具详细指南]]
- [[网络与安全工具]]
- [[编程语言与框架]]

---

*最后更新：2025-06-16*
