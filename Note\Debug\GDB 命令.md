# GDB 命令

---



> 使用 `VSCode` 调试时, 需要通过 `-exec <command>` 进行



## 1. 安装

**Ubuntu**

```shell
sudo apt-get install gdb -y
```

GDB-Peda

```shell
git clone https://gitee.com/lhaisu/peda.git ~/peda
echo "source ~/peda/peda.py" >> ~/.gdbinit
```



### 1.1 一些宝藏工具

- 可视化调试工具: https://github.com/cs01/gdbgui
- GDB调试插件: https://github.com/longld/peda
- GDB 脚本: https://jasonblog.github.io/note/gdb/181.html

## 2. 编译

###  2.1 64位ubuntu交叉编译32位GDB

1. `wget https://ftp.gnu.org/gnu/gdb/gdb-9.2.tar.gz`下载GDB
可以在`https://ftp.gnu.org/gnu/gdb/`处选择更多版本

2. 安装gcc多架构依赖`sudo apt-get install gcc-multilib g++-multilib`

3. 解压并在源码目录外创建编译目录

4. 在编译目录中configure, 跨架构编译需要设置`--build`, `--host`以及`--target`; 常见的架构有: `i386-linux`, `TODO`

5. 使用 `make -j8 && make install` 完成编译安装

```shell
sudo apt-get install gcc-multilib g++-multilib
cd ~/Downloads
wget https://ftp.gnu.org/gnu/gdb/gdb-9.2.tar.gz
tar -xf gdb-9.2.tar.gz
mkdir gdbbuild
cd gdbbuild
mkdir bin
~/Downloads/gdb-9.2/configure --build=i386-linux --host=i386-linux --target=i386-linux CC="gcc -m32" CXX="g++ -m32" CXXFLAGS='-g3 -O0' CFLAGS='-g3 -O0' --prefix=`pwd`/bin
make -j8
make install
```

## 3. 常用命令

**一次性执行多行 GDB 命令**

```shell
gdb -ex "file ffmpeg"
	-ex "dir ffmpeg-src/"
	-ex "set args -i 1.mp4"
	-ex "break open_input_file"
	-ex "run"
```

**GDB Python**

```shell
# 在 GDB 中调用 Python 脚本
(gdb) python
>print("Hello World")
>end

# 一个示例
(gdb) python
>l = []
>x = gdb.parse_and_eval('tcg_ctx->code_buf')
>for i in range(40):
>    l.append(int(str(x[i].const_value()).split(' ')[0]))
>print(b''.join([int(_).to_bytes(length=1, byteorder='little') for _ in l]))
>end
```



### 3.1 常用命令



| 命令名称        | 命令缩写 | 命令说明                                                 | 示例               |
| --------------- | :------: | -------------------------------------------------------- | ------------------ |
| run             |    r     | 运行程序/重新运行程序                                    | r                  |
| continue        |    c     | 让处于暂停的程序继续                                     | c                  |
| next            |    n     | 运行到下一行代码, 不会进入当前函数调用的代码             | n                  |
| step            |    s     | 单步执行, 会进入当前调用的函数内部                       | s                  |
| until           |    u     | 运行到指定行停下来                                       |                    |
| finish          |    fi    | 结束当前调用函数, 回到上一层函数调用出                   | fi                 |
| return          |  return  | 结束当前调用函数, **并返回指定值**, 回到上一层函数调用处 |                    |
| jump            |    j     | 将当前程序执行流跳转到指定`行`或指定`地址`               |                    |
| print           |    p     | 打印变量或寄存器的值                                     |                    |
| backtrace       |    bt    | 查看当前线程调用堆栈信息                                 |                    |
| break           |    b     | 添加断点                                                 |                    |
| tbreak          |    tb    | 添加临时断点                                             |                    |
| delete          |   del    | 删除断点                                                 |                    |
| enable          |  enable  | 启用某个断点                                             |                    |
| disable         | disable  | 禁用某个断点                                             |                    |
| watch           |  watch   | 监视某一个变量或内存地址的值是否发生变化                 |                    |
| list            |    l     | 显示源码                                                 |                    |
| info            |   info   | 查看断点/线程等调试信息                                  |                    |
| ptype           |  ptype   | 查看变量类型                                             |                    |
| disassemble     |   dis    | 查看汇编代码                                             |                    |
| set args        |          | 设置程序启动命令行参数                                   |                    |
| show args       |          | 查看设置的程序启动命令行参数                             |                    |
| ignore N count  |          | 条件断点, 对指定编号的断点设置失效次数                   | ignore 1 50        |
| condition N exp |          | 条件断点, 当断点符合条件表达式时生效                     | condition 1 num==5 |
| help CMD        |          | 查询某个表达式的说明                                     |                    |
|                 |          |                                                          |                    |

### 3.2 在 QEMU 中远程调试

**服务端**

```shell
# 在 QEMU Usermode 下远程调试
## LD_LIBRARY_PATH=<LIB_PATH>  	-- 设置软件依赖但是找不到的动态链接库目录
## qemu64-i386                 	-- 启动 QEMU 
## -L <LIB_PATH>			   -- UNKNOWN
## -g <PORT>				   -- 设置远程调试端口号
## ./ffmpeg -i 1.mp4            -- 目标程序以及运行参数
LD_LIBRARY_PATH=<LIB_PATH> qemu64-i386 -L <LIB_PATH> -g 1234 ./ffmpeg -i 1.mp4
```

**客户端**

```shell
# 打开 gdb
$ gdb
# 设置目标调试文件
# file <ELF Binary Executable File>
(gdb) file ffmpeg
# 设置源代码目录
# dir <Source Directory>
(gdb) dir ffmpeg-src/
# 设置远程调试地址
# target remote <Remote Address>
(gdb) target remote localhost:1234
```



### 3.3 使用 GDB 脚本

```shell
gdb -x afl-cc.gdb /home/<USER>/Repositories/AFLplusplus/afl-clang  
```



```text
# afl-cc.gdb

set args test.c -o test.elf -O0
break 2565
run

set print elements unlimited

set $i = 0
while cc_params[$i] != 0
    print cc_params[$i]
    set $i = $i + 1
end
```



### 导出一段内存

```shell
dump binary memory memory_dump.bin 0x8048000 0x080EE4A0
```



### gdb 退出时不确认 | 自动搜索指定进程并attach

```shell
gdb -ex "set confirm off" attach $(ps aux | grep 'afl-fuzz-llvm' | awk 'NR==1{print $2}')
```



### 设置一大段内存

```
set {char[1024]}0x4017c0 = "\x48\x83\xEC\x40\x48\xC7\xC2\x2F\x62\x69\x6E\x48\x89\x14\x24\x48\xC7\xC2\x2F\x62\x61\x73\x48\x89\x54\x24\x04\x48\xC7\xC2\x68\x00\x00\x00\x48\x89\x54\x24\x08\x48\x89\xE2\x48\x83\xC2\x30\x48\x89\x54\x24\x10\x48\x31\xD2\x48\x89\x54\x24\x18\x48\xC7\xC2\x62\x61\x73\x68\x48\x89\x54\x24\x30\x48\x31\xD2\x48\x89\x54\x24\x34\x48\x31\xC0\xB0\x0B\x48\x89\xE2\x48\x89\xD3\x48\x83\xC2\x10\x48\x89\xD1\x48\xC7\xC2\x00\x00\x00\x00\xCD\x80\x90\x90\x90\x90\x90"
```



### 在 GDB 中调用函数查看复杂结构体

```gdb
print (gchar*)g_date_time_format(now, "%Y-%m-%d %H:%M:%S")
```



### 显示结构体

> value of type `VncState` requires 66312 bytes, which is more than max-value-size

```gdb
set max-value-size unlimited
```



### 在 vscode 中显示 char** 类型的字符数组

```gdb
# 调大 6 表示查看更多项
*(char (*(*)[6]))argv
```

