# 调试 NSPPE

---







```shell
# 进入FreeBSD的TTY终端, 不要使用ssh连接
# Alt+F2(~F6)进行切换, 不要在TTY1进行调试
# 在 NS Console 下, 输入 shell 进入 bash
> shell
# 在bash下, 输入`/netscaler/pb_policy -h nothing`命令关闭pitboss
$ /netscaler/pb_policy -h nothing
# `ps aux | grep nsppe` 锁定进程号
$ ps aux | grep nsppe
# `gdb attach <进程号>`注入进程
$ gdb attach 1206
# Quit this debugging session? >>> 此处选 n
# Create a core file of GDB? >>> 此处也选 n

# 结束调试
# detach

```

