# Panda 动态分析器
---

```
# 运行镜像
# disk-1.qcow 是 Ubuntu 16.04 x86 镜像
docker run -v `pwd`:/workspace -p 5900:5925 -it -w /workspace --rm pandare/panda panda-system-i386 -m 2G -hda disk-1.qcow -monitor stdio -snapshot -vnc :25

# 制作快照
(qemu): migrate "exec: gzip -c > snapshot-1.gz"


# 使用快照加载镜像; 必须与制作镜像时使用相同的启动参数
docker run -v `pwd`:/workspace -p 5900:5925 -it -w /workspace --rm pandare/panda panda-system-i386 -m 2G -hda disk-1.qcow -monitor stdio -snapshot -incoming "exec: gzip -c -d snapshot-1.gz" -vnc :25


# 开始记录
(qemu): begin_record replay-1
# 停止记录
(qemu): end_record


# 使用 replay 模式重放指令
docker run -v `pwd`:/workspace -p 5900:5925 -it -w /workspace --rm pandare/panda panda-system-i386 -m 2G -hda disk-1.qcow -monitor stdio -replay replay-1 -vnc :25


# 系统级记录运行指令
docker run -v `pwd`:/workspace -p 5900:5925 -p 1234:1234 -it -w /workspace --rm pandare/panda panda-system-i386 -m 2G -hda disk-1.qcow -monitor stdio -replay replay-1 -panda 'trace:log=trace.txt'

# 使用每一个 OSI 模块, 需要为具体的镜像生成 kernelinfo.conf 配置文件, 教程:
# https://github.com/panda-re/panda/tree/dev/panda/plugins/osi_linux
# sudo apt-get install build-essential linux-headers-`uname -r`
# sudo insmod kernelinfo.ko # 这里会报一个错, 但是没关系
# dmesg | grep BEGIN
# dmesg > ~/kernelinfo_append.txt

# 进程级记录运行指令
# 进程名最大长度为15, 过长只需要保留前15位
docker run -v `pwd`:/workspace -p 5900:5925 -p 1234:1234 -it -w /workspace --rm pandare/panda panda-system-i386 -m 2G -hda disk-1.qcow -monitor stdio -replay replay-1 -os linux-32-ubuntu -panda 'trace:log=trace_process.txt,target=testOnDemandRTS;osi_linux:kconf_group=ubuntu:16.04-RH:32,kconf_file=/workspace/kernelinfo.conf,load_now=true;osi_test'


# 默认的开启 OSI 的 Replay 命令
docker run -v `pwd`:/workspace -p 5900:5925 -it -w /workspace --rm pandare/panda panda-system-i386 -m 2G -hda disk-1.qcow -monitor stdio -replay replay-1 -os linux-32-ubuntu -panda 'osi;osi_linux:kconf_group=ubuntu:16.04-RH:32,kconf_file=/workspace/kernelinfo.conf,load_now=true'

```



## 什么是 Panda

`PANDA`（Platform for Architecture-Neutral Dynamic Analysis）是一个用于架构中性动态分析的开源平台。它是基于QEMU 2.9.1的全系统动态分析引擎，可以模拟多种体系结构的虚拟机，并在其中执行任意代码。panda支持所有基于Qemu体系的模拟器，所以在LLVM IR中每一条指令都能被有效执行。

## 为什么要使用 Panda

`panda`有以下几个优势：

- panda可以访问虚拟机中所有正在执行的代码和数据，无论是用户空间还是内核空间，无论是明文还是加密。
- panda可以通过插件机制扩展功能，提供了多种动态分析技术，如记录重放、污点分析、符号执行、逆向调试等。
- panda可以支持多种操作系统和应用程序的分析，如Windows、Linux、Android等。
- panda可以利用QEMU的快照功能实现快速部署和恢复虚拟机状态。

## 如何安装 Panda

panda的安装步骤如下：

1. 克隆panda的源码仓库：`git clone https://github.com/panda-re/panda.git`
2. 进入panda目录：`cd panda`
3. 安装依赖包：`sudo apt-get install -y protobuf-compiler protobuf-c-compiler libprotobuf-c0-dev libprotoc-dev python3-pip python3-pil python3-numpy libcapstone-dev libdwarf-dev python3-setuptools llvm-10 clang-10 libc++abi-dev libc++-dev cmake git wget zlib1g-dev pkg-config`
4. 安装python依赖包：`pip3 install -r requirements.txt`
5. 构建并安装panda：`./build.sh`

## 如何使用 Panda

panda的使用方法如下：

1. 准备一个虚拟机镜像文件，例如ubuntu.qcow2
2. 启动虚拟机并加载插件：`./qemu/x86_64-softmmu/qemu-system-x86_64 -m 2048 -hda ubuntu.qcow2 -monitor stdio -loadvm root -os linux-64-lava32 -nographic -net user,hostfwd=tcp::2222-:22 -net nic,model=e1000 -replay foo`
3. 在虚拟机中执行目标程序或操作
4. 在监视器中输入命令控制插件或停止虚拟机：`begin_record foo`, `end_record`, `quit`

以上是一个简单的示例，更多详细的使用方法请参考[panda文档](https://github.com/panda-re/panda/blob/master/docs/manual.md)。



## 关键插件 OSI 与相关插件

> https://github.com/panda-re/panda/tree/dev/panda/plugins/osi
>
> https://github.com/panda-re/panda/tree/dev/panda/plugins/osi_linux



**`OSI` 模块**

"osi"模块是`PANDA`操作系统特定的内省技术支持的核心组成部分. "osi"插件本身充当了粘合层, 提供了一个统一的API, 可以由其他插件调用, 而无需该了解底层操作系统的具体信息.

`OSI` 模块会读取命令行参数`-os`, 用于表示运行镜像的具体操作系统信息. 操作系统信息需要匹配 [`common.c `](https://github.com/panda-re/panda/blob/dev/panda/src/common.c#L169)中的正则表达式, 具体的匹配规则如下. 对于 Linux 系统而言, 需要通过 `osi_linux` 插件指定相关的内核配置文件, 详情请看 `osi_linux` 介绍.

```c
  const char * valid_os_re[] = {
      "windows[-_]32[-_]xpsp[23]",
      "windows[-_]32[-_]2000",
      "windows[-_]32[-_]7sp[01]",
      "windows[-_]64[-_]7sp[01]",
      "linux[-_]32[-_].+",
      "linux[-_]64[-_].+",
      "freebsd[-_]32[-_].+",
      "freebsd[-_]64[-_].+",
      NULL
  };
```



**`OSI_Linux` 模块**



`Linux` 内核数据结构中字段的偏移量经常发生改变，因此osi_linux使用`配置文件`来指定关键数据结构的偏移量。`OSI_Linux` 采用GLib 键值格式（类似于.ini文件）表示内核数据结构：

```ini
[ubuntu:4.4.0-98-generic:32]
name = 4.4.0-98-generic|#121-Ubuntu SMP Tue Oct 10 14:23:20 UTC 2017|i686
version.a = 4
version.b = 4
version.c = 90
task.init_addr = 3249445504
#task.init_addr = 0xC1AE9A80
#task.per_cpu_offset_0 = 0x34B42000
task.per_cpu_offset_0 = 884219904
#task.current_task_addr = 0xC1C852A8
task.current_task_addr = 3251131048
task.size = 5824
task.tasks_offset = 624
task.pid_offset = 776
```



手动生成这样的文件是十分费时费力的, 因此可以通过在 `qemu guest` 中通过加载内核模块的方式来生成此配置文件. **在生成此配置文件之前, 需要禁用系统的 ASLR 地址随机化选项**

在生成内核配置文件之前, 需要安装 `内核头文件` 与 `编译器`, 可以使用如下命令进行安装:

```shell
sudo apt-get install build-essential linux-headers-`uname -r`
```

接下来, 需要拷贝 `https://github.com/panda-re/panda/tree/dev/panda/plugins/osi_linux/utils/kernelinfo` 目录到 `qemu guest` 中, 然后使用 `make` 命令编译得到插件 `kernelinfo.ko`. 通过 `sudo insmod kernelinfo.ko` 安装插件后, 即可通过 `dmesg` 命令导出内核信息.

```shell
# 拷贝 kernelinfo 目录
# scp xxx xxx
cd kernelinfo/
make
sudo insmod kernelinfo.ko
# 这里会收到一个保存信息, 不用管, 属于正常现象
# Error: could not insert module kernelinfo.ko: Operation not permitted
dmesg
# 结果位于以下两个字段之间
# KERNELINFO-BEGIN
# KERNELINFO-END
```

导出内核信息后, `OSI_Linux` 插件即可识别其内核结构了.



`osi_linux` 查看包含以下三个参数:

- `kconf_file`, string类型, 表示配置文件的文件路径. 缺省值为 `${PANDA_HOME}/panda/plugins/osi_linux/kernelinfo.conf`, 提供了一些常见版本的Linux的配置, ~~但是基本都不能用;~~
- `kconf_group`, 字符串, 缺省值为 `debian-3.2.65-i686`, 用于在配置文件中选择具体的配置项;
- `load_now`, 布尔值, 如果为 `true` 则在加载 osi 模块是立即加载配置, 否则会在出现第一个系统调用时加载模块.



一个使用的示例命令为:

```shell
panda-system-i386 -hda ubuntu_1604_i386.qcow2 -monitor stdio -usb -device usb-tablet -net nic,model=rtl8139 -net user -m 2G -replay my_rcd \
-os linux_32_ubuntu1604 \
-panda osi \
-panda osi_linux:kconf_file=my_kernelinfo.conf,kconf_group=ubuntu:4.15.0-45-generic:32 \
-panda osi_test
```





## Panda 的污点分析能力插件



| 插件名称                                                     | 功能                                                         | 依赖                        | 其他                                                         |
| ------------------------------------------------------------ | ------------------------------------------------------------ | --------------------------- | ------------------------------------------------------------ |
| [file_taint](https://github.com/panda-re/panda/tree/dev/panda/plugins/file_taint) | 获取文件类型污点, 通过打开文件系统调用自动标记               | `syscalls2`, `osi`          | 检查`seek`, `open`, `read` 系统调用, 需要特别注意参数 `filename` 标记的文件名. |
| [ida_taint2](https://github.com/panda-re/panda/tree/dev/panda/plugins/ida_taint2) | 本插件能够导出被污染的指令csv, 需要与对应的IDAPython插件`ida_taint2.py`一起使用, 用于在IDA中显示被污染的指令, 并且使用不同的颜色进行标记. | `osi`, `taint2`             |                                                              |
| [pri_taint](https://github.com/panda-re/panda/tree/dev/panda/plugins/pri_taint) |                                                              |                             | DWARF插件                                                    |
| [serial_taint](https://github.com/panda-re/panda/tree/dev/panda/plugins/serial_taint) | 将输入的串行端口数据标记为污点                               | `taint2`                    |                                                              |
| [taint2](https://github.com/panda-re/panda/tree/dev/panda/plugins/taint2) | 污点分析的核心插件, 能够对已经标记的污点, 对正在运行的程序进行数据流追踪. 但是其本身依赖其他插件进行原始污点源标记. |                             |                                                              |
| [tainted_branch](https://github.com/panda-re/panda/tree/dev/panda/plugins/tainted_branch) | 用于输出污点分析报告, 能够获取污点分析过程中所有的分支指令信息. | `taint2`, `callstack_instr` | 可以通过`csvfile`参数显示输出报告                            |
| [tainted_instr](https://github.com/panda-re/panda/tree/dev/panda/plugins/tainted_instr) | 能够分析 qemu replay 中的污点指令数据, 输出的结果通过`-pandalog`进行保存 | `taint2`, `callstack_instr` |                                                              |
| [tainted_mmio](https://github.com/panda-re/panda/tree/dev/panda/plugins/tainted_mmio) |                                                              |                             |                                                              |
| [tainted_net](https://github.com/panda-re/panda/tree/dev/panda/plugins/tainted_net) | 获取网络类型的污点源, 能够查询网络发送或接受的数据污点.      | `taint2`                    |                                                              |



一个污点分析的命令样本如下:

```shell
LD_PRELOAD=/usr/local/bin/libz3.so panda-system-i386 -hda ubuntu_1604_i386.qcow2 -monitor stdio -usb -device usb-tablet -net nic,model=rtl8139 -net user -m 2G -replay my_record \
-os linux_32_ubuntu1604 \
-panda osi \
-panda osi_linux:kconf_file=my_kernelinfo.conf,kconf_group=ubuntu:4.15.0-45-generic:32 \
-panda syscalls2:profile=linux_x86 \
-panda file_taint:filename=/home/<USER>/Desktop/test.html \
-panda tainted_instr:summary=false \
-pandalog ./pandalog_firefox
```





## 常用的命令样本

```shell
# 使用 PANDA 打开镜像
panda-system-i386 -hda ubuntu_1604_i386_desktop.qcow2 -monitor stdio -usb -device usb-tablet -net nic,model=rtl8139 -net user -m 2G -snapshot

# 设置网卡信息(客户端中)
$ ifconfig -a
$ sudo ifconfig ens3 *********
$ route -n
$ sudo route add default gw ********

# 制作快照
(qemu) migrate "exec: gzip -c > <snapshot_name>"

# 加载快照
panda-system-i386 -hda ubuntu_1604_i386_desktop.qcow2 -monitor stdio -usb -device usb-tablet -net nic,model=rtl8139 -net user -m 2G -snapshot -incoming "exec: gzip -c -d <snapshot_name>"

# 使用replay记录
(qemu) begin_record <record_name>
(qemu) end_record

# 使用panda进行污点分析
LD_PRELOAD=/usr/local/bin/libz3.so panda-system-i386 -hda ubuntu_1604_i386_desktop.qcow2 -monitor stdio -usb -device usb-tablet -net nic,model=rtl8139 -net user -m 2G -replay rcd_firefox \
-os linux_32_ubuntu1604 \
-panda osi \
-panda osi_linux:kconf_file=1.conf,kconf_group=ubuntu:4.15.0-45-generic:32 \
-panda syscalls2:profile=linux_x86 \
-panda file_taint:filename=/home/<USER>/Desktop/test.html \
-panda tainted_instr:summary=false \
-pandalog ./pandalog_firefox
```

































