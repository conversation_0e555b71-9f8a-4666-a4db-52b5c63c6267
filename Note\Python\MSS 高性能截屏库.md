# MSS 

[Github Python-mss](https://github.com/BoboTiG/python-mss)

[Pypi](https://pypi.org/project/mss/)

[Read-The-Doc](https://python-mss.readthedocs.io/)



## 安装

```bash
(bash) pip install mss opencv-python
```

```requirements
# requirements.txt
mss == 7.0.1
opencv-python == ********
```



## 使用

```python
import cv2
import time
from mss import mss

with mss() as sct:
    monitor = sct.monitors[1]
    last_time = time.time()
    while True:
        img = numpy.asarray(sct.grab(monitor))
        current_time = time.time()
        fps = 1 / (current_time-last_time)
        cv2.imshow(f"[FPS Test]", img)
        last_time = current_time
        if cv2.waitKey(25) & 0xFF == ord("q"):
            cv2.destroyAllWindows()
            break
cv2.destroyAllWindows()
```













