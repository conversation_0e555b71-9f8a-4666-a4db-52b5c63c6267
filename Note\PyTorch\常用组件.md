# PyTorch 常用组件

---



### 在 Debugger 中显示更多关于 Tensor 的信息

```python
normal_repr = torch.Tensor.__repr__
torch.Tensor.__repr__ = lambda self: f"{self.shape} {normal_repr(self)}"
```





### Safetensors 常用操作

> https://huggingface.co/docs/safetensors/en/index

```bash
pip install safetensors
```

保存 Tensors

```python
import torch
from safetensors.torch import save_file

tensors = {
    "embedding": torch.zeros((2, 2)),
    "attention": torch.zeros((2, 3))
}
save_file(tensors, "model.safetensors")
```

加载 Tensors

```python
from safetensors import safe_open

tensors = {}
with safe_open("model.safetensors", framework="pt", device=0) as f:
    for k in f.keys():
        tensors[k] = f.get_tensor(k)
```

加载部分 Tensors

```python
from safetensors import safe_open

tensors = {}
with safe_open("model.safetensors", framework="pt", device=0) as f:
    tensor_slice = f.get_slice("embedding")
    vocab_size, hidden_dim = tensor_slice.get_shape()
    tensor = tensor_slice[:, :hidden_dim]
```

常用的操作流程

```python
from safetensors.torch import save_file
model_state_dict = model.state_dict()
save_file(model_state_dict, "model.safetensors")

######################################################

from safetensors.torch import load_file
model = MyModel()
state_dict = load_file("model.safetensors")
model.load_state_dict(state_dict)
```



