# 内核内存初始化

## 概述

<GeminiOptimizationFrom> Note/Linux/理解 Linux 系统内存初始化.md </GeminiOptimizationFrom>

Linux 内核的内存初始化是系统启动过程中的关键步骤，涉及虚拟内存布局、地址空间管理和安全机制的建立。理解这些机制对于系统编程和安全研究至关重要。

## x86 架构内存布局

### 用户态虚拟内存限制

在 Linux 中，针对 x86 架构，用户态虚拟内存最大值为 `0x7fffffffffff`。

> 参考文档：
> - https://www.kernel.org/doc/Documentation/x86/x86_64/mm.txt
> - https://stackoverflow.com/questions/61561331/why-does-linux-favor-0x7f-mappings

### 栈地址分配机制

在不开启 `ASLR` 的情况下，程序的栈地址总是从 `0x7ffffffff000` 开始增长。如果开启了 `ASLR`，则栈的初始地址会随机减去某些值。

> 相关源码：
> - https://github.com/torvalds/linux/blob/b18cb64ead400c01bf1580eeba330ace51f8087d/arch/x86/include/asm/processor.h#L757
> - https://simonis.github.io/Memory/

### 用户空间大小定义

```c
/*
 * User space process size. 47bits minus one guard page.  The guard
 * page is necessary on Intel CPUs: if a SYSCALL instruction is at
 * the highest possible canonical userspace address, then that
 * syscall will enter the kernel with a non-canonical return
 * address, and SYSRET will explode dangerously.  We avoid this
 * particular problem by preventing anything from being mapped
 * at the maximum canonical address.
 */
#define TASK_SIZE_MAX	((1UL << 47) - PAGE_SIZE)
```

即 **47 位用户态有效地址** 减去一个 **PAGE_SIZE(4K)**，得到 `0x7ffffffff000`。这样操作的原因是为了防止 SYSCALL 以非法的操作进入内核空间。

### 内核保护页机制

内核会映射一个隐藏的保护页 (0x7ffffffff000-0x7fffffffffff ---p)，以防止某些特殊情况下内存被映射到最高地址：

```
7ffffffff000 ---p 00000000  00:00     0    1    0   0          0         0    0      0 [kernel-guard-page]
```

### 栈溢出防护

内核防止出现栈溢出的实现：

> 参考资料：
> - https://github.com/torvalds/linux/blob/b18cb64ead400c01bf1580eeba330ace51f8087d/arch/x86/mm/fault.c#L756
> - https://samsung.github.io/kspp-study/
> - https://chatgpt.com/share/9554e28a-f229-4780-bdbc-ea4e9627f0dc

## 页面大小定义

`PAGE_SIZE` 的定义如下：

> 源码位置：https://github.com/torvalds/linux/blob/d71ec0ed03ae4318746ad379ab2477e5da853c7d/arch/x86/include/asm/page_types.h#L11

```c
/* PAGE_SHIFT determines the page size */
#define PAGE_SHIFT		CONFIG_PAGE_SHIFT
#define PAGE_SIZE		(_AC(1,UL) << PAGE_SHIFT)
#define PAGE_MASK		(~(PAGE_SIZE-1))
```

## 安全考虑

1. **地址空间隔离**: 通过虚拟内存机制确保进程间的内存隔离
2. **ASLR 支持**: 地址空间布局随机化增强安全性
3. **保护页机制**: 防止恶意代码利用边界条件
4. **栈溢出防护**: 内核级别的栈保护机制

## 相关主题

- [[进程调度机制]] - 进程调度和内存管理
- [[ELF_文件格式]] - 可执行文件的内存映射
- [[编译工具链]] - 编译时的内存布局考虑
