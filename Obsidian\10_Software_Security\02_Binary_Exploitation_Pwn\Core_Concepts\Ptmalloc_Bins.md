# Ptmalloc Bins

## 概述

<GeminiOptimizationFrom> Note/CTF-PWN/ptmalloc-bins.md </GeminiOptimizationFrom>

Ptmalloc 是 glibc 中使用的内存分配器，它使用多种不同类型的 bins 来管理不同大小的内存块。理解 bins 的结构和工作原理对于堆漏洞利用和内存管理优化至关重要。

## 参考资料

> CTF Wiki: https://ctf-wiki.org/pwn/linux/user-mode/heap/ptmalloc2/heap-structure/#bin

## 通用 Bins 结构

### malloc_state 结构

```c
#define NBINS             128
struct malloc_state
{
  ...
  /* Normal bins packed as described above */
  mchunkptr bins[NBINS * 2 - 2];
  ...
}
```

### malloc_chunk 结构

通用的 Chunk 结构如下：

```c
struct malloc_chunk {

  INTERNAL_SIZE_T      mchunk_prev_size;  /* Size of previous chunk (if free).  */
  INTERNAL_SIZE_T      mchunk_size;       /* Size in bytes, including overhead. */

  struct malloc_chunk* fd;         /* double links -- used only if free. */
  struct malloc_chunk* bk;

  /* Only used for large blocks: pointer to next larger size.  */
  struct malloc_chunk* fd_nextsize; /* double links -- used only if free. */
  struct malloc_chunk* bk_nextsize;
};
```

## Bins 类型概览

| Bin Type     | Arch 32                        | Arch x64                          | 描述                     |
| ------------ | ------------------------------ | --------------------------------- | ------------------------ |
| Fast Bin     | 0 ~ 64(Default) or 80(Maximum) | 0 ~ 128(Default) or 160 (Maximum) | 快速分配小块内存         |
| Small Bin    | 16 ~ 504                       | 32 ~ 1016                         | 小块内存管理             |
| Large Bin    | > 504                          | > 1016                            | 大块内存管理             |
| Unsorted Bin | 所有大小                       | 所有大小                          | 临时存储释放的内存块     |

## Fast Bin 详解

### 结构定义

```c
#define NFASTBINS  (fastbin_index (request2size (MAX_FAST_SIZE)) + 1)
struct malloc_state
{
  ...
  /* Fastbins */
  // 不论 32 位还是 64 位 NFASTBINS 长度都是 10
  // https://www.cnblogs.com/husterlong/p/14674040.html
  mfastbinptr fastbinsY[NFASTBINS];
  ...
}

#ifndef DEFAULT_MXFAST
#define DEFAULT_MXFAST     (64 * SIZE_SZ / 4)
#endif

/* The maximum fastbin request size we support */
#define MAX_FAST_SIZE     (80 * SIZE_SZ / 4)
```

### Fast Bin 特性

- **fast bin** 不会发生 `分割`、`合并`以及中间检查
- **LIFO 策略**：即最后释放的 Chunk 会被最早的分配
- **最多支持 10 个** (NFASTBINS) Fast bins
- ptmalloc 会调用 `#define set_max_fast(s)` 宏设置 `global_max_fast` 大小，默认大小为 64 或 128
- **fast bin chunk** 中的 `inuse` 字段始终被设置为 `1`，因此不会发生 **chunk 合并**

### Fast Bin 安全机制

1. **Double Free 检测**：检查释放的 chunk 是否已经在 fastbin 链表头部
2. **大小检查**：确保 chunk 大小在 fastbin 范围内
3. **对齐检查**：验证 chunk 地址的对齐性

## Small Bin 详解

### 基本特性

- `chunk_size = 2 * SIZE_SZ * index`
- **Small Bins** 中有 62 个循环双向链表，每个链表中的 chunk_size 都相同
- **Small Bins** 中的 bin 采用 **FIFO** 的规则

### 大小映射表

| 下标 | SIZE_SZ=4（32 位） | SIZE_SZ=8（64 位） |
| :--- | :----------------- | :----------------- |
| 2    | 16                 | 32                 |
| 3    | 24                 | 48                 |
| 4    | 32                 | 64                 |
| 5    | 40                 | 80                 |
| x    | 2*4*x              | 2*8*x              |
| 63   | 504                | 1008               |

### 相关宏定义

```c
#define NSMALLBINS 64

#define smallbin_index(sz)                                                     \
    ((SMALLBIN_WIDTH == 16 ? (((unsigned) (sz)) >> 4)                          \
                           : (((unsigned) (sz)) >> 3)) +                       \
     SMALLBIN_CORRECTION)
```

## Large Bin

### 特性

- 管理大于 small bin 最大值的内存块
- 使用更复杂的数据结构，包含 `fd_nextsize` 和 `bk_nextsize` 指针
- 按大小排序，支持最佳匹配算法

## Unsorted Bin

### 特性

- 作为其他 bins 的缓存
- 新释放的 chunk 首先进入 unsorted bin
- 在下次分配时会尝试重新分类到合适的 bin

## 安全考虑

### 常见攻击向量

1. **Fast Bin Attack**：利用 fastbin 的单链表结构
2. **Unlink Attack**：利用 small/large bin 的双链表结构
3. **Unsorted Bin Attack**：利用 unsorted bin 的特殊性质

### 防护机制

1. **Safe Linking**：在新版本中对指针进行异或加密
2. **Chunk 完整性检查**：验证 chunk 的元数据
3. **Double Free 检测**：防止重复释放

## 相关主题

- [[Libc_Malloc_内部机制]] - malloc 内部实现详解
- [[Fastbin_Attack]] - Fast Bin 攻击技术
- [[堆漏洞利用总结]] - 堆漏洞利用方法总结
