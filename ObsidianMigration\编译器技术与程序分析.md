# 编译器技术与程序分析

> LLVM/Clang 生态系统、编译原理与程序分析技术

## 📋 目录

- [[#LLVM 生态系统]]
- [[#Clang 工具链]]
- [[#LLVM Pass 开发]]
- [[#编译与构建]]
- [[#程序分析技术]]

---

## LLVM 生态系统

### LLVM 概述

> 参考：[[关于 LLVM]]

**LLVM 定义**：
LLVM（Low Level Virtual Machine）是一个现代的**可重用编译器**和**工具链**技术集合。虽然名称包含"虚拟机"，但现在已经与虚拟机没有任何关系。

**核心组件**：

| 组件 | 说明 |
|------|------|
| **LLVM Core libraries** | 提供独立于源码与目标架构的现代化优化器，围绕 LLVM IR 构建 |
| **Clang** | LLVM 原生 C/C++/Objective-C 编译器，包含静态分析器和 clang-tidy |
| **LLDB** | 高性能调试器，基于 LLVM 与 Clang，比 GDB 更快 |
| **libc++** | 提供标准库的高性能实现，全面支持 C++11 和 C++14 |
| **compiler-rt** | 提供底层代码生成器支持和运行时测试工具（AddressSanitizer 等） |
| **LLD** | 新的链接器，是系统默认链接器的高速替代品 |
| **KLEE** | 符号虚拟机，实现符号执行技术，能生成错误检测的测试用例 |
| **BOLT** | 链接后端优化器（post-link optimizer） |

**重要资源**：
- 官方网站：https://llvm.org/
- 文档：https://llvm.org/docs/
- GitHub：https://github.com/llvm/llvm-project
- Gitee 镜像：https://gitee.com/mirrors/llvm-project

### LLVM 安装与配置

> 参考：[[LLVM 安装与基础使用]]、[[LLVM Pass 安装]]

**从源码编译**：
```bash
# 克隆源码
git clone https://github.com/llvm/llvm-project.git
cd llvm-project

# 创建构建目录
mkdir build && cd build

# 配置编译选项
cmake -G "Unix Makefiles" \
      -DCMAKE_BUILD_TYPE=Release \
      -DLLVM_ENABLE_PROJECTS="clang;clang-tools-extra" \
      ../llvm

# 编译（需要大量时间和内存）
make -j$(nproc)
```

**包管理器安装**：
```bash
# Ubuntu/Debian
sudo apt install llvm clang

# macOS
brew install llvm

# Arch Linux
sudo pacman -S llvm clang
```

---

## Clang 工具链

### Clang 基本组件

> 参考：[[Clang 基本组件]]

**核心工具**：
- **clang**：C/C++ 编译器前端
- **clang++**：C++ 编译器前端
- **clang-format**：代码格式化工具
- **clang-tidy**：静态分析和代码检查工具
- **clang-analyzer**：静态分析器

### AST 导出与分析

> 参考：[[Clang 导出 AST]]

**导出 AST**：
```bash
# 导出为文本格式
clang -Xclang -ast-dump -fsyntax-only source.c

# 导出为 JSON 格式
clang -Xclang -ast-dump=json -fsyntax-only source.c > ast.json

# 导出为图形化格式
clang -Xclang -ast-view -fsyntax-only source.c
```

**AST 分析应用**：
- 代码重构工具开发
- 静态分析工具构建
- 代码生成和转换
- 程序理解和文档生成

### CMake 与 Clang 集成

> 参考：[[CMake and Clang Commands]]

**CMakeLists.txt 配置**：
```cmake
cmake_minimum_required(VERSION 3.10)
project(MyProject)

# 设置 Clang 为编译器
set(CMAKE_C_COMPILER clang)
set(CMAKE_CXX_COMPILER clang++)

# 设置编译标志
set(CMAKE_CXX_FLAGS "-Wall -Wextra -std=c++17")

# 添加可执行文件
add_executable(myapp main.cpp)
```

---

## LLVM Pass 开发

### Pass 开发入门

> 参考：[[LLVM Pass 入门教程]]系列

#### 1. LLVM Pass 基础

**Pass 类型**：
- **Analysis Pass**：分析程序但不修改
- **Transform Pass**：修改程序结构
- **Utility Pass**：提供辅助功能

**基本 Pass 结构**：
```cpp
#include "llvm/Pass.h"
#include "llvm/IR/Function.h"
#include "llvm/Support/raw_ostream.h"

using namespace llvm;

namespace {
    struct HelloPass : public FunctionPass {
        static char ID;
        HelloPass() : FunctionPass(ID) {}
        
        bool runOnFunction(Function &F) override {
            errs() << "Hello: " << F.getName() << "\n";
            return false; // 没有修改函数
        }
    };
}

char HelloPass::ID = 0;
static RegisterPass<HelloPass> X("hello", "Hello World Pass");
```

#### 2. 静态插桩技术

> 参考：[[LLVM Pass 静态插桩]]

**插桩示例**：
```cpp
bool runOnFunction(Function &F) override {
    LLVMContext &Context = F.getContext();
    IRBuilder<> Builder(Context);
    
    for (auto &BB : F) {
        for (auto &I : BB) {
            if (auto *Call = dyn_cast<CallInst>(&I)) {
                // 在函数调用前插入代码
                Builder.SetInsertPoint(Call);
                
                // 创建插桩函数调用
                FunctionType *FT = FunctionType::get(
                    Type::getVoidTy(Context), {}, false);
                Function *LogFunc = Function::Create(FT, 
                    Function::ExternalLinkage, "log_call", 
                    F.getParent());
                
                Builder.CreateCall(LogFunc);
            }
        }
    }
    return true;
}
```

#### 3. 获取程序结构信息

> 参考：[[LLVM Pass 获取结构信息]]

**遍历程序结构**：
```cpp
void analyzeFunction(Function &F) {
    errs() << "Function: " << F.getName() << "\n";
    errs() << "  Arguments: " << F.arg_size() << "\n";
    errs() << "  Basic Blocks: " << F.size() << "\n";
    
    for (auto &BB : F) {
        errs() << "  BB: " << BB.getName() << "\n";
        for (auto &I : BB) {
            errs() << "    " << I.getOpcodeName() << "\n";
        }
    }
}
```

#### 4. 控制流图分析

> 参考：[[LLVM Pass 获取 CFG]]

**CFG 分析**：
```cpp
#include "llvm/Analysis/CFG.h"

void analyzeCFG(Function &F) {
    for (auto &BB : F) {
        errs() << "Basic Block: " << BB.getName() << "\n";
        
        // 前驱基本块
        for (auto *Pred : predecessors(&BB)) {
            errs() << "  Predecessor: " << Pred->getName() << "\n";
        }
        
        // 后继基本块
        for (auto *Succ : successors(&BB)) {
            errs() << "  Successor: " << Succ->getName() << "\n";
        }
    }
}
```

### Pass 开发注意事项

> 参考：[[LLVM Pass 开发踩坑]]

**常见问题**：
1. **内存管理**：正确使用 LLVM 的内存管理机制
2. **类型系统**：理解 LLVM IR 的类型系统
3. **调试技巧**：使用 `errs()` 和 `dbgs()` 进行调试
4. **版本兼容**：不同 LLVM 版本的 API 差异

---

## 编译与构建

### 构建系统基础

> 参考：[[构建系统基本概念]]

**编译过程**：
1. **预处理**：处理宏定义和头文件包含
2. **编译**：将源码转换为汇编代码
3. **汇编**：将汇编代码转换为目标文件
4. **链接**：将目标文件链接为可执行文件

### Makefile 编写

> 参考：[[Makefile 使用指南]]

**基本 Makefile**：
```makefile
CC = clang
CXX = clang++
CFLAGS = -Wall -Wextra -O2
CXXFLAGS = -Wall -Wextra -O2 -std=c++17

SRCDIR = src
OBJDIR = obj
SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)
TARGET = myapp

.PHONY: all clean

all: $(TARGET)

$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $@

$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) -c $< -o $@

$(OBJDIR):
	mkdir -p $(OBJDIR)

clean:
	rm -rf $(OBJDIR) $(TARGET)
```

### 编译器配置

**GCC 头文件查看**：
> 参考：[[gcc 查看头文件引用]]

```bash
# 查看预定义宏
gcc -dM -E - < /dev/null

# 查看头文件搜索路径
gcc -v -E - < /dev/null

# 查看链接库路径
gcc -print-search-dirs
```

---

## 程序分析技术

### 静态分析

**代码质量检查**：
```bash
# 使用 clang-tidy
clang-tidy source.cpp -- -std=c++17

# 使用 clang 静态分析器
clang --analyze source.cpp
```

### 动态分析

**Sanitizer 工具**：
> 参考：[[Sanitizer 工具集]]

```bash
# AddressSanitizer
clang -fsanitize=address -g source.cpp

# ThreadSanitizer
clang -fsanitize=thread -g source.cpp

# MemorySanitizer
clang -fsanitize=memory -g source.cpp
```

### LLVM Pass 详细教程

#### Pass 入门实践

> 参考：[[LLVM Pass 入门]]

**基础 Pass 实现**：
```cpp
#include "llvm/Pass.h"
#include "llvm/IR/Function.h"
#include "llvm/ADT/Statistic.h"
#include "llvm/Support/raw_ostream.h"
#include "llvm/IR/LegacyPassManager.h"
#include "llvm/Transforms/IPO/PassManagerBuilder.h"
using namespace llvm;

#define DEBUG_TYPE "hello"

STATISTIC(HelloCounter, "Counts number of functions greeted");

namespace {
  struct Hello : public FunctionPass {
    static char ID;
    Hello() : FunctionPass(ID) {}

    ~Hello() {
        errs() << "\nCompile Finish!\n";
        errs() << "Functions count: " << HelloCounter;
    }

    bool runOnFunction(Function &F) override {
      ++HelloCounter;
      errs() << "Got Function> ";
      errs().write_escaped(F.getName()) << '\n';
      return false;
    }
  };
}

char Hello::ID = 0;
// 为 opt 注册
static RegisterPass<Hello> X("funcname", "Hello World Pass");
// 为 clang 注册
static RegisterStandardPasses Y(PassManagerBuilder::EP_EarlyAsPossible,
                                [](const PassManagerBuilder& Builder,
                                   legacy::PassManagerBase& PM) {
                                  PM.add(new Hello());
                                });
```

**编译和使用**：
```bash
# 编译 Pass
clang `llvm-config --cxxflags` -Wl,-znodelete -fno-rtti -fPIC -shared Hello.cpp -o LLVMHello.so `llvm-config --ldflags`

# 使用 opt
opt -enable-new-pm=0 -load ./LLVMHello.so -funcname main.ll

# 使用 clang
clang -flegacy-pass-manager -g -Xclang -load -Xclang ./LLVMHello.so main.c -o main.elf
```

#### 静态插桩技术详解

> 参考：[[LLVM Pass 静态插桩]]

**插桩示例：获取 posix_memalign 参数**：

项目结构：
```
root/
    |---- Hello.cpp     # LLVM Pass 函数插桩代码
    |---- main.c        # 样本程序
    |---- runtime.c     # 运行时库
```

**样本程序 main.c**：
```c
#include <stdio.h>
#include <stdlib.h>

int main(int argc, const char** argv) {
    char *s=NULL;
    printf("LLVM Test posix_memalign\n");
    posix_memalign((void **)&s, 16, 1024);
    printf("====== Finish ======\n");
    return 0;
}
```

**插桩 Pass Hello.cpp**：
```cpp
#include "llvm/IR/Function.h"
#include "llvm/IR/IRBuilder.h"
#include "llvm/IR/InstrTypes.h"
#include "llvm/IR/LegacyPassManager.h"
#include "llvm/IR/Module.h"
#include "llvm/IR/TypeFinder.h"
#include "llvm/Pass.h"
#include "llvm/Support/raw_ostream.h"
#include "llvm/Transforms/IPO/PassManagerBuilder.h"
#include "llvm/Transforms/Utils/BasicBlockUtils.h"

using namespace llvm;

namespace {
struct SkeletonPass : public FunctionPass {
  static char ID;
  SkeletonPass() : FunctionPass(ID) {}

  virtual bool runOnFunction(Function& F) {
    // 获取函数上下文
    LLVMContext& Ctx = F.getContext();

    // 定义插桩函数原型
    Type* retType = Type::getVoidTy(Ctx);
    std::vector<Type*> rt_posix_memalign_param_types = {
        Type::getHalfPtrTy(Ctx), Type::getInt32Ty(Ctx), Type::getInt32Ty(Ctx)};
    FunctionType* rt_posix_memalign_type =
        FunctionType::get(retType, rt_posix_memalign_param_types, false);
    FunctionCallee rt_posix_memalign = F.getParent()->getOrInsertFunction(
        "rt_posix_memalign", rt_posix_memalign_type);

    // 迭代基本块
    for (auto& B : F) {
      // 迭代指令
      for (auto& I : B) {
        // 判断指令是否是函数调用指令
        if (auto* call = dyn_cast<CallInst>(&I)) {
          Function* fun = call->getCalledFunction();
          if (!fun) continue;

          // 判断函数是否为 posix_memalign
          if (0 == fun->getName().compare(StringRef("posix_memalign"))) {
            // 创建 IRBuilder
            IRBuilder<> builder(call);
            builder.SetInsertPoint(&B, builder.GetInsertPoint());

            // 设置插桩点（函数调用后）
            auto* call2 = dyn_cast<CallInst>(&I);
            IRBuilder<> builder2(call2);
            builder2.SetInsertPoint(&B, ++builder2.GetInsertPoint());

            // 获取参数
            std::vector<Value*> args;
            for (auto arg = call->arg_begin(); arg != call->arg_end(); ++arg) {
              args.push_back(*arg);
            }

            // 插入插桩函数调用
            builder.CreateCall(rt_posix_memalign, args);   // 调用前
            builder2.CreateCall(rt_posix_memalign, args);  // 调用后
          }
        }
      }
    }
    return false;
  }
};
}

char SkeletonPass::ID = 0;
static RegisterPass<SkeletonPass> X("rhpass", "Hello World Pass");
static void registerSkeletonPass(const PassManagerBuilder&,
                                 legacy::PassManagerBase& PM) {
  PM.add(new SkeletonPass());
}
static RegisterStandardPasses RegisterMyPass(
    PassManagerBuilder::EP_EarlyAsPossible, registerSkeletonPass);
```

**运行时库 runtime.c**：
```c
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

void rt_posix_memalign(void **p, size_t align, size_t size) {
    printf("%p -- %p -- %zu -- %zu\n", p, *p, align, size);
    return;
}
```

**编译和测试**：
```bash
# 构建 LLVM Pass
clang `llvm-config --cxxflags` -Wl,-znodelete -fno-rtti -fPIC -shared Hello.cpp -o LLVMHello.so `llvm-config --ldflags`

# 使用 Pass 编译样本
clang -flegacy-pass-manager -g -Xclang -load -Xclang ./LLVMHello.so -c main.c -o main.o

# 构建运行时函数
clang -c runtime.c -o runtime.o

# 链接生成可执行文件
clang main.o runtime.o -o main.elf

# 运行样本
./main.elf
```

**输出结果**：
```
LLVM Test posix_memalign
0x7ffec9cef698 -- (nil) -- 16 -- 1024
0x7ffec9cef698 -- 0x17516b0 -- 16 -- 1024
====== Finish ======
```

#### 获取程序结构信息

> 参考：[[LLVM Pass 获取结构信息]]

**TypeFinder 使用示例**：
```cpp
#include "llvm/IR/Function.h"
#include "llvm/IR/LegacyPassManager.h"
#include "llvm/IR/Module.h"
#include "llvm/Pass.h"
#include "llvm/Transforms/IPO/PassManagerBuilder.h"
#include "llvm/Transforms/Utils/BasicBlockUtils.h"
#include "llvm/IR/TypeFinder.h"

using namespace llvm;

namespace {
struct SkeletonPass : public FunctionPass {
  static char ID;
  SkeletonPass() : FunctionPass(ID) {}

  virtual bool runOnFunction(llvm::Function& F) {
    // 解析 AST
    llvm::TypeFinder StructTypes;
    const Module& M = *F.getParent();
    StructTypes.run(M, true);

    // 迭代所有的结构体
    for (auto* STy : StructTypes) {
      errs() << "结构体: " << *STy << "\n";

      // 迭代结构体中的成员
      for (auto field : STy->elements()) {
        errs() << "  成员: " << *field << "\n";
      }
    }
    return false;
  }
};
}

char SkeletonPass::ID = 0;
static RegisterPass<SkeletonPass> X("rhpass", "Structure Analysis Pass");
```

**测试用的结构体程序**：
```c
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef struct Human {
    size_t age;
    char name[32];
} Human;

typedef struct Student {
    Human man;
    size_t score;
} Student;

typedef struct Teacher {
    Human man;
    char title[32];
    size_t salary;
} Teacher;

typedef struct ClassRoom {
    size_t id;
    Teacher teacher;
    Student student[50];
} ClassRoom;

typedef struct School {
    char name[100];
    ClassRoom classrooms[100];
} School;

int main(int argc, const char** argv) {
    printf("======  Start  ======\n");
    Human p1, p2;
    Student s1;
    Teacher t1;
    ClassRoom c;
    School s;

    p1.age = 18;
    strncpy(p1.name, "JERRY", sizeof("JERRY"));
    p2.age = 42;
    strncpy(p2.name, "TOM", sizeof("TOM"));

    s1.man = p1;
    s1.score = 90;

    t1.man = p2;
    strncpy(t1.title, "Professor", sizeof("Professor"));
    t1.salary = 500000;

    c.id = 5;
    c.teacher = t1;
    c.student[0] = s1;

    strncpy(s.name, "Harvard University", sizeof("Harvard University"));
    s.classrooms[0] = c;

    printf("====== Finish ======\n");
    return 0;
}
```

#### 控制流图分析

> 参考：[[LLVM Pass 获取 CFG]]

**CFG 分析 Pass**：
```cpp
#include "llvm/IR/Function.h"
#include "llvm/IR/CFG.h"
#include "llvm/Analysis/CFG.h"
#include "llvm/Pass.h"
#include "llvm/Support/raw_ostream.h"

using namespace llvm;

namespace {
struct CFGAnalysisPass : public FunctionPass {
  static char ID;
  CFGAnalysisPass() : FunctionPass(ID) {}

  bool runOnFunction(Function &F) override {
    errs() << "分析函数: " << F.getName() << "\n";
    errs() << "基本块数量: " << F.size() << "\n";

    // 分析每个基本块
    for (auto &BB : F) {
      errs() << "\n基本块: " << BB.getName() << "\n";
      errs() << "指令数量: " << BB.size() << "\n";

      // 分析前驱基本块
      errs() << "前驱基本块: ";
      for (auto *Pred : predecessors(&BB)) {
        errs() << Pred->getName() << " ";
      }
      errs() << "\n";

      // 分析后继基本块
      errs() << "后继基本块: ";
      for (auto *Succ : successors(&BB)) {
        errs() << Succ->getName() << " ";
      }
      errs() << "\n";

      // 分析基本块中的指令
      for (auto &I : BB) {
        errs() << "  指令: " << I.getOpcodeName() << "\n";

        // 如果是分支指令，分析分支条件
        if (auto *BI = dyn_cast<BranchInst>(&I)) {
          if (BI->isConditional()) {
            errs() << "    条件分支，条件: " << *BI->getCondition() << "\n";
          } else {
            errs() << "    无条件分支\n";
          }
        }
      }
    }

    return false;
  }
};
}

char CFGAnalysisPass::ID = 0;
static RegisterPass<CFGAnalysisPass> X("cfg-analysis", "CFG Analysis Pass");
```

#### 构建大型程序

> 参考：[[LLVM 构建大型程序]]

**CMakeLists.txt 配置**：
```cmake
cmake_minimum_required(VERSION 3.13.4)
project(LLVMPassProject)

find_package(LLVM REQUIRED CONFIG)

message(STATUS "Found LLVM ${LLVM_PACKAGE_VERSION}")
message(STATUS "Using LLVMConfig.cmake in: ${LLVM_DIR}")

include_directories(${LLVM_INCLUDE_DIRS})
separate_arguments(LLVM_DEFINITIONS_LIST NATIVE_COMMAND ${LLVM_DEFINITIONS})
add_definitions(${LLVM_DEFINITIONS_LIST})

add_library(LLVMPassProject MODULE
    # List your source files here.
    Hello.cpp
)

# Use C++14 to compile our pass (required by LLVM).
target_compile_features(LLVMPassProject PRIVATE cxx_std_14)

# LLVM is (typically) built with no C++ RTTI. We need to match that;
# otherwise, we'll get linker errors about missing RTTI data.
set_target_properties(LLVMPassProject PROPERTIES
    COMPILE_FLAGS "-fno-rtti"
)

# Get proper shared-library behavior (where symbols are not necessarily
# resolved when the shared library is linked) on OS X.
if(APPLE)
    set_target_properties(LLVMPassProject PROPERTIES
        LINK_FLAGS "-undefined dynamic_lookup"
    )
endif(APPLE)
```

#### 关键 API 总结

**函数原型定义**：
```cpp
// 获取上下文环境
LLVMContext& Ctx = F.getContext();

// 定义返回值类型
Type* retType = Type::getVoidTy(Ctx);

// 定义形参类型
std::vector<Type*> param_types = {
    Type::getHalfPtrTy(Ctx),
    Type::getInt32Ty(Ctx),
    Type::getInt32Ty(Ctx)
};

// 定义函数类型
FunctionType* func_type = FunctionType::get(retType, param_types, false);

// 构建函数原型
FunctionCallee func_callee = F.getParent()->getOrInsertFunction(
    "function_name", func_type);
```

**指令分析**：
```cpp
// 转换为 call 指令
auto* call = dyn_cast<CallInst>(&I);

// 获取被调用的函数
Function* f = call->getCalledFunction();

// 判断函数名
if (0 == f->getName().compare(StringRef("target_function"))) {
    // 获取参数
    std::vector<Value*> args;
    for (auto arg = call->arg_begin(); arg != call->arg_end(); ++arg) {
        args.push_back(*arg);
    }
}
```

**IRBuilder 使用**：
```cpp
// 创建 IRBuilder
IRBuilder<> builder(call);

// 设置插桩点
builder.SetInsertPoint(&B, builder.GetInsertPoint());

// 插入函数调用
builder.CreateCall(func_callee, args);
```

---

## 🔗 相关链接

- [[二进制安全与逆向工程]]
- [[系统虚拟化与仿真]]
- [[开发工具链]]
- [[调试工具集合]]

---

*最后更新：2025-06-16*
