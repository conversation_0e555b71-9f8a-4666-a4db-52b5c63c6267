# Linux 内核内存初始化

---



**Linux 中, 针对 x86 架构**

  Linux 中, 用户态虚拟内存最大值为 `0x7fffffffffff`

> https://www.kernel.org/doc/Documentation/x86/x86_64/mm.txt
>
> https://stackoverflow.com/questions/61561331/why-does-linux-favor-0x7f-mappings



其中, 在不开启 `ASLR` 的情况下, 程序的栈地址总是从 `0x7ffffffff000` 开始增长. 如果开启了 `ASLR`, 则栈的初始地址会随机减去某些值. 该地址的获取方式如下:

> https://github.com/torvalds/linux/blob/b18cb64ead400c01bf1580eeba330ace51f8087d/arch/x86/include/asm/processor.h#L757
>
> https://simonis.github.io/Memory/

```c
/*
 * User space process size. 47bits minus one guard page.  The guard
 * page is necessary on Intel CPUs: if a SYSCALL instruction is at
 * the highest possible canonical userspace address, then that
 * syscall will enter the kernel with a non-canonical return
 * address, and SYSRET will explode dangerously.  We avoid this
 * particular problem by preventing anything from being mapped
 * at the maximum canonical address.
 */
#define TASK_SIZE_MAX	((1UL << 47) - PAGE_SIZE)
```

即 **47 位用户态有效地址** 减去一个 **PAGE_SIZE(4K)**, 得到 `0x7ffffffff000` . 这样操作的原因是为了防止 SYSCALL 以非法的操作进入内核空间. 内核会映射一个隐藏的保护页(0x7ffffffff000-0x7fffffffffff ---p), 以防止某些特殊情况下内存被映射到最高地址. 

```
    7ffffffff000 ---p 00000000  00:00     0    1    0   0          0         0    0      0 [kernel-guard-page]
```



内核防止出现栈溢出的实现如下:

> https://github.com/torvalds/linux/blob/b18cb64ead400c01bf1580eeba330ace51f8087d/arch/x86/mm/fault.c#L756
>
> https://samsung.github.io/kspp-study/
>
> https://chatgpt.com/share/9554e28a-f229-4780-bdbc-ea4e9627f0dc



`PAGE_SIZE` 的定义如下:

> https://github.com/torvalds/linux/blob/d71ec0ed03ae4318746ad379ab2477e5da853c7d/arch/x86/include/asm/page_types.h#L11

```c
/* PAGE_SHIFT determines the page size */
#define PAGE_SHIFT		CONFIG_PAGE_SHIFT
#define PAGE_SIZE		(_AC(1,UL) << PAGE_SHIFT)
#define PAGE_MASK		(~(PAGE_SIZE-1))
```

