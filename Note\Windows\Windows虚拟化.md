# Windows 虚拟化

---



## 强制开启 Hyper-V 服务

如果"启用或关闭 Windows 功能"中没有"Hyper-V"选项, 可以通过命令行强制开启此功能:

创建bat脚本, 写入以下内容, 然后在管理员权限下执行

```cmd
pushd "%~dp0"
dir /b %SystemRoot%\servicing\Packages\*Hyper-V*.mum >hyper-v.txt
for /f %%i in ('findstr /i . hyper-v.txt 2^>nul') do dism /online /norestart /add-package:"%SystemRoot%\servicing\Packages\%%i"
del hyper-v.txt
Dism /online /enable-feature /featurename:Microsoft-Hyper-V-All /LimitAccess /ALL
```

