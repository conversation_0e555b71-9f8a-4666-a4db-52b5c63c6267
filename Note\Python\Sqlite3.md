# Python Sqlite3

---



### 1. 打开数据库

```python
import sqlite3
# 连接到 SQLite3 数据库（如果不存在则会创建）
conn = sqlite3.connect('my_database.sqlite3')
# 创建一个游标对象，用于执行 SQL 语句
cursor = conn.cursor()
```



### 2. 创建数据库

```python
# 创建表格
cursor.execute('''
    CREATE TABLE IF NOT EXISTS my_table (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        address INTEGER,
        segment TEXT,
        code TEXT,
        result TEXT
    )
''')
conn.commit()
```



### 3. 向数据库中添加数据

```python
sq_line = ("printf", 0x80487500, ".text", "void printf(const char* __fmt, ...)")
cursor.execute("INSERT INTO my_table (name, address, segment, code) VALUES (?, ?, ?, ?)", sq_line)
conn.commit()
```



### 4. 根据 ID 逐行遍历数据

```python
current_id = -1
cursor.execute('SELECT * FROM my_table WHERE id > ? ORDER BY id LIMIT 1', (current_id,))
row = cursor.fetchone()

while row:
    rid, name, address, segment, code, result = row
    print(rid, name)
    # 搜索下一行
    current_id = rid
    cursor.execute('SELECT * FROM my_table WHERE id > ? ORDER BY id LIMIT 1', (current_id,))
    row = cursor.fetchone()

conn.close()
```



### 5. 更新数据

```python
cursor.execute('UPDATE my_table SET name = ? WHERE id = ?', (new_name, rid))
conn.commit()
```





















