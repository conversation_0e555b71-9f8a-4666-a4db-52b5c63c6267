# Install Mega

---



```shell
#!/bin/bash

apt-get update
apt-get install -y wget lsb-release

export DEBIAN_FRONTEND=noninteractive
# Get Ubuntu Release Version
UBUNTU_VERSION=$(lsb_release -rs)

# Build url and filename
DEB_FILE="megacmd-xUbuntu_${UBUNTU_VERSION}_amd64.deb"
URL="https://mega.nz/linux/repo/xUbuntu_${UBUNTU_VERSION}/amd64/${DEB_FILE}"

# 下载并安装
echo "Downloading from $URL"
wget "$URL" -O "/tmp/$DEB_FILE"
if [ $? -eq 0 ]; then
    apt install "/tmp/$DEB_FILE" -y
    if [ $? -eq 0 ]; then
        echo "Installation successful!"
    else
        echo "Installation failed."
        exit 1
    fi
else
    echo "Failed to download $DEB_FILE. Please check the URL or your internet connection."
    exit 1
fi
```

