# OpenSSL 编译

----




1. 克隆 OpenSSL 仓库
```shell
https://github.com/openssl/openssl.git
```

2. 编译 OpenSSL 仓库
```shell
cd openssl
git checkout OpenSSL_1_1_1-stable
./config --prefix=<安装绝对路径> --openssldir=<安装绝对路径> -Wl,-rpath=<安装绝对路径>/lib
make -j
make test
make install
```

3. 使用 openssl 生成一个 RSA 密钥对
```shell
openssl genpkey -algorithm RSA -out private_key.pem -pkeyopt rsa_keygen_bits:4096
openssl rsa -pubout -in private_key.pem -out public_key.pem
```

4. 编译一个 C 语言程序, 使用指定的 OpenSSL 库
```shell
# -o 选项应当放在最前
gcc -o main.elf main.c -g -ggdb -I/workspaces/codespaces-blank/openssl-build/include/ -L/workspaces/codespaces-blank/openssl-build/lib/ -Wl,-rpath=/workspaces/codespaces-blank/openssl-build/lib -lssl -lcrypto
```



---



### 一键编译脚本

```shell
# 克隆仓库
git clone https://github.com/openssl/openssl.git

# 编译安装
OPENSSL_DIR=/workspaces/test/openssl-build
cd openssl
git checkout OpenSSL_1_1_1-stable
./config --prefix=${OPENSSL_DIR} --openssldir=${OPENSSL_DIR} -Wl,-rpath=${OPENSSL_DIR}/lib
make -j
make test
make install

# 生成公私钥对
cd ${OPENSSL_DIR}/../
${OPENSSL_DIR}/bin/openssl genpkey -algorithm RSA -out private_key.pem -pkeyopt rsa_keygen_bits:4096
${OPENSSL_DIR}/bin/openssl rsa -pubout -in private_key.pem -out public_key.pem
```





