# Python 中的日志模块

---

```python
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s \t- %(filename)s:%(lineno)d→%(funcName)s - %(message)s')

# File Handler
file_handler = logging.FileHandler('my_log_file.log')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
```



```python

import os
import sys
import logging

logger = logging.getLogger("my_logger")
logger.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s \t- %(filename)s:%(lineno)d→%(funcName)s - %(message)s')

# Stream Handler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# File Handler
file_handler = logging.FileHandler('my_log_file.log')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

logger.debug('这是一条调试日志。')
logger.critical('这是一条调试日志。')
logger.info('这是一条信息日志。')
logger.warning('这是一条警告日志。')
logger.error('这是一条警告日志。')
```







