# GCC 命令参考

## 概述

GCC (GNU Compiler Collection) 是一套由 GNU 开发的编程语言编译器集合，支持多种编程语言和目标架构。

## 基本编译命令

### 指定程序加载地址

<GeminiOptimizationFrom> Note/Building/Commands.md </GeminiOptimizationFrom>

使用 GCC 指定程序的加载地址：

```shell
gcc -Wl,-Ttext-segment=0x09048000 -o test.elf test.c

# 完整参数示例
gcc -Wl,-Ttext-segment=0x09048000 -m32 -g -ggdb -z execstack -fno-pie -no-pie -fPIC -z norelro -static -o test.elf test.c
```

可以使用 `readelf -l` 命令确认程序的加载地址。

#### Intel i386 架构示例输出

```text
Elf file type is EXEC (Executable file)
Entry point 0x9049140
There are 11 program headers, starting at offset 52

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  PHDR           0x000034 0x09048034 0x09048034 0x00160 0x00160 R   0x4
  INTERP         0x000194 0x09048194 0x09048194 0x00013 0x00013 R   0x1
      [Requesting program interpreter: /lib/ld-linux.so.2]
  LOAD           0x000000 0x09048000 0x09048000 0x003c8 0x003c8 R   0x1000
  LOAD           0x001000 0x09049000 0x09049000 0x004b4 0x004b4 R E 0x1000
  LOAD           0x002000 0x0904a000 0x0904a000 0x00268 0x00268 R   0x1000
  LOAD           0x002268 0x0904b268 0x0904b268 0x00128 0x0012c RW  0x1000
  DYNAMIC        0x002270 0x0904b270 0x0904b270 0x000e8 0x000e8 RW  0x4
  NOTE           0x0001a8 0x090481a8 0x090481a8 0x00060 0x00060 R   0x4
  GNU_PROPERTY   0x0001cc 0x090481cc 0x090481cc 0x0001c 0x0001c R   0x4
  GNU_EH_FRAME   0x0020f0 0x0904a0f0 0x0904a0f0 0x0004c 0x0004c R   0x4
  GNU_STACK      0x000000 0x00000000 0x00000000 0x00000 0x00000 RWE 0x10

 Section to Segment mapping:
  Segment Sections...
   00     
   01     .interp 
   02     .interp .note.gnu.build-id .note.gnu.property .note.ABI-tag .gnu.hash .dynsym .dynstr .gnu.version .gnu.version_r .rel.dyn .rel.plt 
   03     .init .plt .plt.sec .text .fini 
   04     .rodata .eh_frame_hdr .eh_frame 
   05     .init_array .fini_array .dynamic .got .got.plt .data .bss 
   06     .dynamic 
   07     .note.gnu.build-id .note.gnu.property .note.ABI-tag 
   08     .note.gnu.property 
   09     .eh_frame_hdr 
   10     
```

#### Intel x86-64 架构示例输出

```text
Elf file type is EXEC (Executable file)
Entry point 0x9049120
There are 13 program headers, starting at offset 64

Program Headers:
  Type           Offset             VirtAddr           PhysAddr
                 FileSiz            MemSiz              Flags  Align
  PHDR           0x0000000000000040 0x0000000009048040 0x0000000009048040
                 0x00000000000002d8 0x00000000000002d8  R      0x8
  INTERP         0x0000000000000318 0x0000000009048318 0x0000000009048318
                 0x000000000000001c 0x000000000000001c  R      0x1
      [Requesting program interpreter: /lib64/ld-linux-x86-64.so.2]
  LOAD           0x0000000000000000 0x0000000009048000 0x0000000009048000
                 0x0000000000000750 0x0000000000000750  R      0x1000
  LOAD           0x0000000000001000 0x0000000009049000 0x0000000009049000
                 0x0000000000000465 0x0000000000000465  R E    0x1000
  LOAD           0x0000000000002000 0x000000000904a000 0x000000000904a000
                 0x0000000000000240 0x0000000000000240  R      0x1000
  LOAD           0x0000000000002d88 0x000000000904bd88 0x000000000904bd88
                 0x0000000000000288 0x0000000000000290  RW     0x1000
  DYNAMIC        0x0000000000002d98 0x000000000904bd98 0x000000000904bd98
                 0x00000000000001f0 0x00000000000001f0  RW     0x8
  NOTE           0x0000000000000338 0x0000000009048338 0x0000000009048338
                 0x0000000000000020 0x0000000000000020  R      0x8
  NOTE           0x0000000000000358 0x0000000009048358 0x0000000009048358
                 0x0000000000000044 0x0000000000000044  R      0x4
  GNU_PROPERTY   0x0000000000000338 0x0000000009048338 0x0000000009048338
                 0x0000000000000020 0x0000000000000020  R      0x8
  GNU_EH_FRAME   0x00000000000020f4 0x000000000904a0f4 0x000000000904a0f4
                 0x0000000000000044 0x0000000000000044  R      0x4
  GNU_STACK      0x0000000000000000 0x0000000000000000 0x0000000000000000
                 0x0000000000000000 0x0000000000000000  RW     0x10
  GNU_RELRO      0x0000000000002d88 0x000000000904bd88 0x000000000904bd88
                 0x0000000000000278 0x0000000000000278  R      0x1

 Section to Segment mapping:
  Segment Sections...
   00     
   01     .interp 
   02     .interp .note.gnu.property .note.gnu.build-id .note.ABI-tag .gnu.hash .dynsym .dynstr .gnu.version .gnu.version_r .rela.dyn .rela.plt 
   03     .init .plt .plt.got .plt.sec .text .fini 
   04     .rodata .eh_frame_hdr .eh_frame 
   05     .init_array .fini_array .dynamic .got .data .bss 
   06     .dynamic 
   07     .note.gnu.property 
   08     .note.gnu.build-id .note.ABI-tag 
   09     .note.gnu.property 
   10     .eh_frame_hdr 
   11     
   12     .init_array .fini_array .dynamic .got 
```

### 查看头文件引用

<GeminiOptimizationFrom> Note/Compiler/gcc 查看头文件引用.md </GeminiOptimizationFrom>

查看 GCC 编译时的头文件引用路径：

```shell
echo "#include <unistd.h>" | gcc -E -H -o /dev/null - 
```

## 相关工具

- [[Clang_命令参考]] - Clang 编译器命令参考
- [[编译工具链]] - 完整编译工具链概述
- [[ELF_文件格式]] - ELF 文件格式详解
