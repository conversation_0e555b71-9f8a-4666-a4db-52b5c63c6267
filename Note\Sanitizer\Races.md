# Race Condition vs. Data Race

---



### 简单介绍两者定义

`Data Race`数据竞争发生在程序中的两个内存访问时, 同时发生以下内容:

1. 访问的地址相同
2. 可能由两个线程同时执行
3. 不全是读取
4. 没有进行同步操作



`Race Condition`竞态条件, 通常是这样的模式:

1. `检测条件是否满足要求->执行具体的指令` 任何可能出现不满足判断条件的执行路径都会触发静态条件





### 从简单的银行账户转账模型说起

```c
bool transfer_1 (amount, account_from, account_to) {
  // 判断账户余额是否大于转账金额
  if (account_from.balance < amount) return false;
  // 转账: 目标账户增加余额; 起始账户扣除余额
  account_to.balance += amount;
  account_from.balance -= amount;
  return true;
}
```

显然, 上述例子在并行操作时是有可能出现问题的, 因为在处理转账操作前并不能保障账户的金额未被修改.显然, 上面的例子出现问题时结果是不可预测的:

1. 执行转账语句时, 无法确定账户的余额是否发生了更改, 即语句`if (account_from.balance < amount)`条件不一定成立;
2. 转账语句并不是原子(Atomic)的, 本质上分为了`load`、`compute`、`store`命令, 分别将值从内存加载到寄存器中、计算运算结果、将计算结果存储到内存中. 很有可能两个线程同时`load`了一个数据, 计算后分别将计算结果存储到内存中. 此时计算结果与`store`命令的执行顺序有关;



为了解决上述提出的第二点问题, 可以将上述的例子改进为以下:

```c
bool transfer_2 (amount, account_from, account_to) {
  atomic {
    bal = account_from.balance;
  }
  if (bal < amount) return false;
  atomic {
    account_to.balance += amount;
  }
  atomic {
    account_from.balance -= amount;
  }
  return true;
}
```

说白了, 就是将所有非原子的指令使用某种原子性实现, 具体的实现方式在此不讨论. 显然, 这样修改后虽然不会出现由于指令执行顺序不同导致的问题, 但是上述的**问题1**问题依然存在. 这样的例子只包含`Race Condition`而不包含`Data Race`.



为了把**问题1**一起解决, 其实存在一个非常简单的改进方式:

```c
bool transfer_3 (amount, account_from, account_to) {
  atomic {
    if (account_from.balance < amount) return false;
    account_to.balance += amount;
    account_from.balance -= amount;
    return true;
  }
}
```

直接将所有的指令都进行原子化操作, 此时显然不会出现任何问题, 这是一段没有任何问题的代码!



虽然问题解决了, 但是我们还可以专门构造一个只有`Data Race`, 但没有`Race Condition`的样本:

```c
bool transfer_4 (amount, account_from, account_to) {
  account_from.activity = true;
  account_to.activity = true;
  atomic {
    if (account_from.balance < amount) return true;
    account_to.balance += amount;
    account_from.balance -= amount;
    return false;
  }
}
```



这个是可以构造的例子, 他不会产生`Race Condition`问题, 但是却存在一个`Data Race`问题. 这里可以构造了一个`activity`标志位, 表示当前账户是否处于处理状态. 在转帐前, 我们将两个账户标记为忙碌状态, 此时由于没有原子性的保护, 可能出现不符合预期的结果. 但... 这样的漏洞危害性很小. 







|                   | Data Race  | No Data Race |
| ----------------- | ---------- | ------------ |
| Race Condition    | transfer_1 | transfer_2   |
| No Race Condition | transfer_4 | transfer_3   |











