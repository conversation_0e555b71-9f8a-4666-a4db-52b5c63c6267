# Inspect 魔法

---



### Positional Only Arguments & Keyword Only Arguments

```python
# "/" 前的变量是 Positional Only 的
def f(a, b=3, /, *args, **kwargs):
    pass

# "*" 后的变量是 Keywords Only 的
def g(a, *, b=3, **kwargs):
    pass

code = f.__code__

# 显示参数数目
print(code.co_argcount)
# 显示 Positional Only Arguments 参数数目
print(code.co_posonlyargcount)
# 显示 Keyword Only Arguments 参数数目
print(code.co_kwonlyargcount)
```





### Python 指令插桩

```python
import sys
import inspect

sys.settrace()
```





### Python 获取函数参数列表

```python
import inspect

def test():
    # 获取父函数的函数名称
    frame = inspect.currentframe()
    fparent = frame.f_back
    fparent_name = fparent.f_code.co_name
    # 获取父函数的函数参数
    fparent_argv = fparent.f_code.co_argcount
    fparent_locals = fparent.f_locals
    fparent_values = inspect.getargvalues(fparent)
	print(fparent_values)
    
def main(a,b,c=0):
    test()

main(1,2,3)
```





### Python 获取栈帧

```python
import inspect

def main():
    frame = inspect.currentframe()
   
main()
```



**获取父函数的局部变量**

```python

import sys
import inspect


def ftest(*args):
    frame = inspect.currentframe()
    parent = frame.f_back
    print("==> ", parent.f_locals)

def main():
    a = 1
    b = 2
    ftest(a, b, 0xffff)

main()

# ==>  {'a': 1, 'b': 2}
```



**获取父函数的名称**

```python

import sys
import inspect


def ftest(*args):
    frame = inspect.currentframe()
    parent = frame.f_back
    print("==> ", parent.f_code.co_name)

def main():
    a = 1
    b = 2
    ftest(a, b, 0xffff)

main()


# ==> main
```



**获取父函数调用当前函数的文件名以及行号**

```python

import sys
import inspect


def ftest(*args):
    frame = inspect.currentframe()
    parent = frame.f_back
    print("==> ", parent.f_code.co_filename)
    print("==> ", parent.f_lineno)

def main():
    a = 1
    b = 2
    ftest(a, b, 0xffff)

main()


# ==> test.py
# ==> 15
```

