# QEMU Usermode Memory

---



### QEMU Linux-User 模式内存读写问题

`cpu_memory_rw_debug`函数是一个很方便的函数, 分别在`<Project Dir>/cpu.c`(Linux-User模式) 或 `<Project Dir>/softmmu/physmem.c`(softmmu模式) 中实现. 函数声明都定义在 `include/exec/cpu-common.h` 中. 

Linux-User 模式下的 QEMU, 其内存的访问权限与真实的进程内存访问权限相同. 也就是说, 此时如果使用 `cpu_memory_rw_debug` 函数修改一个 `PAGE_READ | PAGE_VALID` 权限的内存, 会由于权限问题无法修改. **如果需要修改不可写的内存片段, 可以通过先修改权限为高权限, 修改完后再改回权限的方式进行**. 

Linux-User 模式下, 内存页的权限包含两部分: 一部分是真实的内存权限, 使用 `mprotect` 函数进行修改; 另一部分是 QEMU 的内存页权限表, 使用 `page_set_flags` 函数进行修改; 一个修改内存权限的例子如下: 

```c
vaddr page;
page = <待修改的内存地址> & TARGET_PAGE_MASK;
// 获取内存页的访问权限
flags = page_get_flags(page);
// linux-user 中, 通常会有 MAP_ANON 标志位, 但是在设置的时候只能设置 READ WRITE EXEC VALID 标志位
#define PAGE_MASK (PAGE_READ | PAGE_WRITE | PAGE_EXEC | PAGE_VALID)
#define MAX_FLAGS PAGE_MASK
// 获取当前内存页的权限
setter_flags = flags & PAGE_MASK;
// 设置物理内存的保护权限
if (-1 == mprotect((void*)(page), TARGET_PAGE_SIZE, MAX_FLAGS)) {
    // 内存权限修改失败了, 需要进行错误处理
}
// 设置 QEMU 内存页权限表的权限
page_set_flags(page, page+TARGET_PAGE_SIZE, MAX_FLAGS);
```



一个将权限修改到最高, 然后修改内存的例子如下: 值得注意的是, 上述例子只能修改小段内存, 无法修改跨页的内存. 

```c
bool cpu_memory_rw_debug_force(CPUState *cpu, vaddr addr, void* ptr, size_t len, bool is_write) {
    int flags;
    int setter_flags;
    int MAX_FLAGS = (PAGE_BITS | PAGE_VALID);
    vaddr page;
    bool ret = true;

    page = addr & TARGET_PAGE_MASK;
    flags = page_get_flags(page);
    setter_flags = flags & 0xf;
    if (-1 == mprotect((void*)(page), TARGET_PAGE_SIZE, MAX_FLAGS)) {
        return false;
    }
    page_set_flags(page, page+TARGET_PAGE_SIZE, MAX_FLAGS);

    ret &= (0 == cpu_memory_rw_debug(cpu, addr, ptr, len, is_write));
    
    if (-1 == mprotect((void*)(page), TARGET_PAGE_SIZE, setter_flags)) {
        return false;
    }
    page_set_flags(page, page+TARGET_PAGE_SIZE, setter_flags);

    return ret;
}
```

