# JS OCR 库 Tesseract

---



> https://github.com/naptha/tesseract.js
>
> https://unpkg.com/tesseract.js@4.0.1/dist/tesseract.min.js



```javascript
<!DOCTYPE html>
<html>
	<body>
		<div id="app">
			<!-- 将图片设置为允许跨域 crossorigin="anonymous" -->
			<img id="target" crossorigin="anonymous" src="https://tesseract.projectnaptha.com/img/eng_bw.png" alt="text">
		</div>
		<!-- 引入库 -->
		<script src="https://unpkg.com/tesseract.js@4.0.1/dist/tesseract.min.js"></script>
		<script>
			// 将图片转换为 Base64 字符串
			function getBase64Image(img) {
				var canvas = document.createElement("canvas");
				canvas.width = img.width;
				canvas.height = img.height;
				var ctx = canvas.getContext("2d");
				ctx.drawImage(img, 0, 0);
				var dataURL = canvas.toDataURL("image/png");
				return dataURL;
			}
			
			const img = getBase64Image(document.getElementById("target"));
			
			// Tesseract.recognize(
			//     img,
			//     'eng',
			//     { logger: m => console.log(m) }
			// ).then(({ data: { text } }) => {
			//     console.log(text);
			// })
			
			// 识别图片中的文字
			(async () => {
			  const worker = await Tesseract.createWorker();
			  await worker.loadLanguage('eng');
			  await worker.initialize('eng');
			  const { data: { text } } = await worker.recognize(img);
			  // const { data: { text } } = await worker.recognize("https://tesseract.projectnaptha.com/img/eng_bw.png");
			  console.log(text);
			})();
			
		</script>
	</body>
</html>
```



