# Python 详细指南

> Python 生态系统的详细使用指南和最佳实践

## 📋 目录

- [[#核心语言特性]]
- [[#数据处理与分析]]
- [[#网络编程]]
- [[#系统编程]]
- [[#GUI 自动化]]

---

## 核心语言特性

### Python 字节码分析

> 参考：[[Python Bytecode]]

#### 字节码基础

```python
import dis
import types

def analyze_bytecode(func):
    """分析函数的字节码"""
    print(f"分析函数: {func.__name__}")
    print("=" * 50)
    
    # 显示字节码
    dis.dis(func)
    
    # 获取代码对象
    code = func.__code__
    print(f"\n代码对象信息:")
    print(f"参数数量: {code.co_argcount}")
    print(f"局部变量数量: {code.co_nlocals}")
    print(f"栈大小: {code.co_stacksize}")
    print(f"常量: {code.co_consts}")
    print(f"变量名: {code.co_varnames}")

# 示例函数
def example_function(x, y):
    z = x + y
    if z > 10:
        return z * 2
    else:
        return z

# 分析字节码
analyze_bytecode(example_function)
```

#### 动态代码生成

```python
import types

def create_dynamic_function(name, code_str, globals_dict=None):
    """动态创建函数"""
    if globals_dict is None:
        globals_dict = {}
    
    # 编译代码
    code_obj = compile(code_str, '<dynamic>', 'exec')
    
    # 执行代码获取函数
    local_dict = {}
    exec(code_obj, globals_dict, local_dict)
    
    return local_dict.get(name)

# 示例：动态创建数学函数
math_code = """
def calculate(x, y, operation='add'):
    if operation == 'add':
        return x + y
    elif operation == 'multiply':
        return x * y
    elif operation == 'power':
        return x ** y
    else:
        return None
"""

# 创建函数
calc_func = create_dynamic_function('calculate', math_code)
print(calc_func(5, 3, 'power'))  # 输出: 125
```

### 内置函数深度使用

> 参考：[[Python 内置函数]]

#### 高级内置函数

```python
from functools import reduce, partial
from itertools import chain, combinations, permutations
from collections import defaultdict, Counter, deque

class AdvancedBuiltins:
    @staticmethod
    def demonstrate_map_filter_reduce():
        """演示 map, filter, reduce 的高级用法"""
        numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        
        # map: 应用函数到每个元素
        squared = list(map(lambda x: x**2, numbers))
        print(f"平方: {squared}")
        
        # filter: 过滤元素
        evens = list(filter(lambda x: x % 2 == 0, numbers))
        print(f"偶数: {evens}")
        
        # reduce: 累积操作
        product = reduce(lambda x, y: x * y, numbers)
        print(f"乘积: {product}")
        
        # 组合使用
        result = reduce(lambda x, y: x + y, 
                       map(lambda x: x**2, 
                           filter(lambda x: x % 2 == 0, numbers)))
        print(f"偶数平方和: {result}")
    
    @staticmethod
    def demonstrate_itertools():
        """演示 itertools 的强大功能"""
        data = [1, 2, 3, 4]
        
        # 排列
        perms = list(permutations(data, 2))
        print(f"排列 P(4,2): {perms}")
        
        # 组合
        combs = list(combinations(data, 2))
        print(f"组合 C(4,2): {combs}")
        
        # 链式连接
        list1 = [1, 2, 3]
        list2 = [4, 5, 6]
        list3 = [7, 8, 9]
        chained = list(chain(list1, list2, list3))
        print(f"链式连接: {chained}")
    
    @staticmethod
    def demonstrate_collections():
        """演示 collections 模块"""
        # defaultdict
        dd = defaultdict(list)
        for i, char in enumerate("hello world"):
            dd[char].append(i)
        print(f"字符位置: {dict(dd)}")
        
        # Counter
        text = "hello world"
        counter = Counter(text)
        print(f"字符计数: {counter}")
        print(f"最常见的3个: {counter.most_common(3)}")
        
        # deque (双端队列)
        dq = deque([1, 2, 3, 4, 5])
        dq.appendleft(0)
        dq.append(6)
        print(f"双端队列: {list(dq)}")

# 演示
demo = AdvancedBuiltins()
demo.demonstrate_map_filter_reduce()
demo.demonstrate_itertools()
demo.demonstrate_collections()
```

### inspect 模块魔法

> 参考：[[inspect 魔法]]

```python
import inspect
import ast

class InspectMagic:
    def __init__(self):
        self.analysis_results = {}
    
    def analyze_function(self, func):
        """深度分析函数"""
        print(f"分析函数: {func.__name__}")
        print("=" * 50)
        
        # 获取签名
        sig = inspect.signature(func)
        print(f"函数签名: {sig}")
        
        # 获取源代码
        try:
            source = inspect.getsource(func)
            print(f"源代码:\n{source}")
        except OSError:
            print("无法获取源代码")
        
        # 获取参数信息
        for name, param in sig.parameters.items():
            print(f"参数 {name}:")
            print(f"  类型: {param.kind}")
            print(f"  默认值: {param.default}")
            print(f"  注解: {param.annotation}")
        
        # 获取返回值注解
        if sig.return_annotation != inspect.Signature.empty:
            print(f"返回值注解: {sig.return_annotation}")
        
        # 获取文档字符串
        if func.__doc__:
            print(f"文档字符串: {func.__doc__}")
    
    def get_call_stack(self):
        """获取调用栈信息"""
        stack = inspect.stack()
        print("调用栈:")
        for i, frame_info in enumerate(stack):
            print(f"  {i}: {frame_info.filename}:{frame_info.lineno} in {frame_info.function}")
    
    def analyze_class(self, cls):
        """分析类结构"""
        print(f"分析类: {cls.__name__}")
        print("=" * 50)
        
        # 获取所有成员
        members = inspect.getmembers(cls)
        
        methods = []
        properties = []
        attributes = []
        
        for name, value in members:
            if name.startswith('_'):
                continue
                
            if inspect.ismethod(value) or inspect.isfunction(value):
                methods.append(name)
            elif isinstance(value, property):
                properties.append(name)
            else:
                attributes.append(name)
        
        print(f"方法: {methods}")
        print(f"属性: {properties}")
        print(f"其他属性: {attributes}")
        
        # 获取 MRO (方法解析顺序)
        print(f"MRO: {[cls.__name__ for cls in cls.__mro__]}")

# 示例使用
def example_function(x: int, y: str = "default") -> str:
    """这是一个示例函数
    
    Args:
        x: 整数参数
        y: 字符串参数，有默认值
    
    Returns:
        格式化的字符串
    """
    return f"x={x}, y={y}"

class ExampleClass:
    def __init__(self, value):
        self._value = value
    
    @property
    def value(self):
        return self._value
    
    def method(self):
        return "method called"

# 使用 inspect 魔法
magic = InspectMagic()
magic.analyze_function(example_function)
magic.analyze_class(ExampleClass)
```

---

## 数据处理与分析

### 16进制显示与处理

> 参考：[[Python 交互模式显示 16 进制]]

```python
class HexDisplay:
    def __init__(self):
        self.setup_hex_display()
    
    def setup_hex_display(self):
        """设置16进制显示"""
        import sys
        
        # 自定义显示钩子
        def hex_displayhook(value):
            if isinstance(value, int) and value > 255:
                print(f"Dec: {value}, Hex: 0x{value:X}, Bin: 0b{value:b}")
            else:
                print(repr(value))
        
        # 在交互模式下设置
        if hasattr(sys, 'ps1'):
            sys.displayhook = hex_displayhook
    
    @staticmethod
    def hex_dump(data, width=16):
        """十六进制转储"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        for i in range(0, len(data), width):
            chunk = data[i:i+width]
            hex_part = ' '.join(f'{b:02x}' for b in chunk)
            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f'{i:08x}: {hex_part:<{width*3}} |{ascii_part}|')
    
    @staticmethod
    def analyze_bytes(data):
        """分析字节数据"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        print(f"数据长度: {len(data)} 字节")
        print(f"MD5: {hashlib.md5(data).hexdigest()}")
        print(f"SHA256: {hashlib.sha256(data).hexdigest()}")
        
        # 字节频率分析
        from collections import Counter
        freq = Counter(data)
        print("字节频率 (前10):")
        for byte, count in freq.most_common(10):
            print(f"  0x{byte:02x}: {count} 次")

# 使用示例
import hashlib

hex_display = HexDisplay()
sample_data = "Hello, World! 这是测试数据。"
hex_display.hex_dump(sample_data)
hex_display.analyze_bytes(sample_data)
```

### Ctypes 系统调用

> 参考：[[Ctypes 使用]]

```python
import ctypes
import ctypes.util
from ctypes import wintypes
import platform

class SystemInterface:
    def __init__(self):
        self.system = platform.system()
        self.setup_libraries()
    
    def setup_libraries(self):
        """设置系统库"""
        if self.system == "Windows":
            self.kernel32 = ctypes.windll.kernel32
            self.user32 = ctypes.windll.user32
            self.ntdll = ctypes.windll.ntdll
        elif self.system == "Linux":
            self.libc = ctypes.CDLL("libc.so.6")
        elif self.system == "Darwin":
            self.libc = ctypes.CDLL("libc.dylib")
    
    def get_system_info(self):
        """获取系统信息"""
        if self.system == "Windows":
            return self._get_windows_info()
        else:
            return self._get_unix_info()
    
    def _get_windows_info(self):
        """获取 Windows 系统信息"""
        # 获取系统信息
        class SYSTEM_INFO(ctypes.Structure):
            _fields_ = [
                ("wProcessorArchitecture", wintypes.WORD),
                ("wReserved", wintypes.WORD),
                ("dwPageSize", wintypes.DWORD),
                ("lpMinimumApplicationAddress", ctypes.c_void_p),
                ("lpMaximumApplicationAddress", ctypes.c_void_p),
                ("dwActiveProcessorMask", ctypes.POINTER(wintypes.DWORD)),
                ("dwNumberOfProcessors", wintypes.DWORD),
                ("dwProcessorType", wintypes.DWORD),
                ("dwAllocationGranularity", wintypes.DWORD),
                ("wProcessorLevel", wintypes.WORD),
                ("wProcessorRevision", wintypes.WORD),
            ]
        
        si = SYSTEM_INFO()
        self.kernel32.GetSystemInfo(ctypes.byref(si))
        
        return {
            "处理器数量": si.dwNumberOfProcessors,
            "页面大小": si.dwPageSize,
            "分配粒度": si.dwAllocationGranularity,
            "处理器架构": si.wProcessorArchitecture
        }
    
    def _get_unix_info(self):
        """获取 Unix 系统信息"""
        # 获取进程 ID
        pid = self.libc.getpid()
        
        # 获取用户 ID
        uid = self.libc.getuid()
        
        return {
            "进程ID": pid,
            "用户ID": uid,
            "系统": self.system
        }
    
    def allocate_memory(self, size):
        """分配内存"""
        if self.system == "Windows":
            # Windows VirtualAlloc
            MEM_COMMIT = 0x1000
            MEM_RESERVE = 0x2000
            PAGE_READWRITE = 0x04
            
            addr = self.kernel32.VirtualAlloc(
                None, size, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE
            )
            return addr
        else:
            # Unix mmap
            PROT_READ = 1
            PROT_WRITE = 2
            MAP_PRIVATE = 2
            MAP_ANONYMOUS = 0x20
            
            addr = self.libc.mmap(
                None, size, PROT_READ | PROT_WRITE, 
                MAP_PRIVATE | MAP_ANONYMOUS, -1, 0
            )
            return addr
    
    def free_memory(self, addr, size=0):
        """释放内存"""
        if self.system == "Windows":
            MEM_RELEASE = 0x8000
            self.kernel32.VirtualFree(addr, 0, MEM_RELEASE)
        else:
            self.libc.munmap(addr, size)

# 使用示例
sys_interface = SystemInterface()
info = sys_interface.get_system_info()
print("系统信息:")
for key, value in info.items():
    print(f"  {key}: {value}")

# 内存操作示例
addr = sys_interface.allocate_memory(4096)
print(f"分配内存地址: 0x{addr:x}")

# 写入数据
data = b"Hello, Memory!"
ctypes.memmove(addr, data, len(data))

# 读取数据
buffer = ctypes.create_string_buffer(len(data))
ctypes.memmove(buffer, addr, len(data))
print(f"读取数据: {buffer.value}")

# 释放内存
sys_interface.free_memory(addr, 4096)
```

---

## 网络编程

### urllib3 高级用法

> 参考：[[urllib3 使用]]

```python
import urllib3
import json
import ssl
from urllib3.util.retry import Retry
from urllib3.util.timeout import Timeout

class AdvancedHTTPClient:
    def __init__(self, proxy_url=None, verify_ssl=True):
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]
        )
        
        # 配置超时
        timeout = Timeout(connect=10.0, read=30.0)
        
        # 创建连接池
        if proxy_url:
            self.http = urllib3.ProxyManager(
                proxy_url,
                timeout=timeout,
                retries=retry_strategy,
                cert_reqs='CERT_REQUIRED' if verify_ssl else 'CERT_NONE'
            )
        else:
            self.http = urllib3.PoolManager(
                timeout=timeout,
                retries=retry_strategy,
                cert_reqs='CERT_REQUIRED' if verify_ssl else 'CERT_NONE'
            )
        
        # 默认请求头
        self.default_headers = {
            'User-Agent': 'AdvancedHTTPClient/1.0',
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip, deflate'
        }
    
    def request(self, method, url, **kwargs):
        """发送 HTTP 请求"""
        headers = self.default_headers.copy()
        headers.update(kwargs.pop('headers', {}))
        
        try:
            response = self.http.request(
                method, url, headers=headers, **kwargs
            )
            return self._process_response(response)
        except urllib3.exceptions.MaxRetryError as e:
            raise Exception(f"请求失败: {e}")
    
    def _process_response(self, response):
        """处理响应"""
        result = {
            'status': response.status,
            'headers': dict(response.headers),
            'data': response.data
        }
        
        # 尝试解析 JSON
        content_type = response.headers.get('content-type', '')
        if 'application/json' in content_type:
            try:
                result['json'] = json.loads(response.data.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                pass
        
        return result
    
    def get(self, url, params=None, **kwargs):
        """GET 请求"""
        if params:
            url += '?' + urllib3.request.urlencode(params)
        return self.request('GET', url, **kwargs)
    
    def post(self, url, data=None, json_data=None, **kwargs):
        """POST 请求"""
        if json_data:
            kwargs['body'] = json.dumps(json_data)
            kwargs.setdefault('headers', {})['Content-Type'] = 'application/json'
        elif data:
            kwargs['body'] = data
        
        return self.request('POST', url, **kwargs)
    
    def upload_file(self, url, file_path, field_name='file'):
        """上传文件"""
        with open(file_path, 'rb') as f:
            fields = {field_name: (file_path, f.read())}
            
        response = self.http.request(
            'POST', url,
            fields=fields,
            headers=self.default_headers
        )
        return self._process_response(response)

# 使用示例
client = AdvancedHTTPClient()

# GET 请求
response = client.get('https://httpbin.org/get', params={'key': 'value'})
print(f"状态码: {response['status']}")
if 'json' in response:
    print(f"响应数据: {response['json']}")

# POST 请求
post_response = client.post(
    'https://httpbin.org/post',
    json_data={'message': 'Hello, World!'}
)
print(f"POST 响应: {post_response['json']}")
```

---

## 🔗 相关链接

- [[编程语言与框架]]
- [[开发工具详细指南]]
- [[系统管理与运维]]
- [[AI 辅助开发]]

---

*最后更新：2025-06-16*
