# Python 多进程与多线程

## 概述

<GeminiOptimizationFrom> Note/Python/多线程or多进程.md </GeminiOptimizationFrom>

Python 提供了多种并发编程的方式，包括多线程、多进程和子进程。理解这些不同的并发模式对于编写高效的 Python 程序至关重要。

## 多线程 `threading`

### 基本用法

在 Python 中启动一个新的线程：

```python
import time
from threading import Thread

t = Thread(target=time.sleep, args=(5, ))
t.start()
```

### 线程特性

- **共享内存空间**：所有线程共享同一进程的内存
- **GIL 限制**：由于全局解释器锁（GIL），Python 线程在 CPU 密集型任务中效果有限
- **适用场景**：I/O 密集型任务，如网络请求、文件读写

### 高级线程操作

```python
import threading
import time

def worker(name, delay):
    print(f"线程 {name} 开始工作")
    time.sleep(delay)
    print(f"线程 {name} 完成工作")

# 创建多个线程
threads = []
for i in range(3):
    t = threading.Thread(target=worker, args=(f"Worker-{i}", i+1))
    t.start()
    threads.append(t)

# 等待所有线程完成
for t in threads:
    t.join()
```

## 多进程 `multiprocessing`

### 基本进程操作

启动进程与阻塞进程：

```python
import time
from multiprocessing import Process

process_list = []
for task in tasks:
    p = Process(target=run_task, args=(task, ))
    p.start()
    process_list.append(p)
    time.sleep(2)

for p in process_list:
    p.join()
```

### 进程池 `Pool`

启动进程池，控制同时使用的资源：

```python
import time
from multiprocessing import Pool

num_process = 3

with Pool(num_process) as pool:
    for delay in [1,2,3,4,5]:
        res = pool.apply_async(time.sleep, (delay, ))
    pool.close()
    pool.join()
```

### 进程特性

- **独立内存空间**：每个进程有独立的内存空间
- **真正并行**：不受 GIL 限制，可以真正并行执行
- **适用场景**：CPU 密集型任务，需要真正并行计算的场景

### 进程间通信

```python
from multiprocessing import Process, Queue, Pipe

# 使用队列
def worker_with_queue(q):
    while True:
        item = q.get()
        if item is None:
            break
        print(f"处理项目: {item}")

q = Queue()
p = Process(target=worker_with_queue, args=(q,))
p.start()

# 发送数据
for i in range(5):
    q.put(f"任务-{i}")
q.put(None)  # 结束信号

p.join()
```

## 子进程 `subprocess`

### 基本用法

创建一个子进程：

```python
import os
import subprocess
import sys

args = ["ping", "-c", "10", "-i", "0.5", "*******"]

process = subprocess.Popen(
    # 参数列表
    # 如果 shell=True,  传入一个 str 类型的命令
    # 如果 shell=False, 传入一个 list[str] 类型的命令
    args=args,
    # 是否在新建终端中执行命令
    # 如果 shell=True, 则无法获取执行命令的 pid
    shell=False,
    # stderr 与 stdout 的写入位置
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    # 选择工作目录
    cwd="/home/<USER>/",
    # 设置运行的环境变量
    env=os.environ,
)

print(process.pid)

while True:
    c = process.stdout.read(1)
    if c:
        c = c.decode("UTF-8")
        print(c, end="")
        sys.stdout.flush()
    else:
        break
```

### subprocess 参数详解

- **args**: 命令参数列表或字符串
- **shell**: 是否在 shell 中执行
- **stdout/stderr**: 标准输出和错误输出的重定向
- **cwd**: 工作目录
- **env**: 环境变量

### 简化的 subprocess 操作

```python
import subprocess

# 简单执行命令
result = subprocess.run(['ls', '-l'], capture_output=True, text=True)
print(result.stdout)

# 检查命令执行状态
try:
    subprocess.run(['false'], check=True)
except subprocess.CalledProcessError as e:
    print(f"命令执行失败: {e}")
```

## 性能对比与选择

### 使用场景对比

| 并发类型 | 适用场景 | 优点 | 缺点 |
|----------|----------|------|------|
| 多线程 | I/O 密集型任务 | 内存共享，切换开销小 | 受 GIL 限制，不适合 CPU 密集型 |
| 多进程 | CPU 密集型任务 | 真正并行，不受 GIL 限制 | 内存开销大，进程间通信复杂 |
| 子进程 | 调用外部程序 | 隔离性好，可调用任何程序 | 开销最大，通信通过管道 |

### 选择建议

1. **I/O 密集型任务**：优先选择多线程
2. **CPU 密集型任务**：选择多进程
3. **调用外部程序**：使用 subprocess
4. **混合场景**：考虑异步编程（asyncio）

## 同步原语

### 线程同步

```python
import threading

# 锁
lock = threading.Lock()
with lock:
    # 临界区代码
    pass

# 条件变量
condition = threading.Condition()
with condition:
    condition.wait()  # 等待条件
    condition.notify()  # 通知等待的线程

# 信号量
semaphore = threading.Semaphore(2)  # 最多允许2个线程
with semaphore:
    # 受限制的代码段
    pass
```

### 进程同步

```python
from multiprocessing import Lock, Semaphore, Event

# 进程锁
lock = Lock()
with lock:
    # 临界区代码
    pass

# 进程事件
event = Event()
event.set()    # 设置事件
event.wait()   # 等待事件
event.clear()  # 清除事件
```

## 最佳实践

### 1. 资源管理

```python
# 使用上下文管理器
with Pool(4) as pool:
    results = pool.map(worker_function, data_list)

# 显式关闭资源
pool = Pool(4)
try:
    results = pool.map(worker_function, data_list)
finally:
    pool.close()
    pool.join()
```

### 2. 异常处理

```python
def safe_worker(data):
    try:
        return process_data(data)
    except Exception as e:
        print(f"处理数据时出错: {e}")
        return None
```

### 3. 性能监控

```python
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

start_time = time.time()
with ThreadPoolExecutor(max_workers=4) as executor:
    futures = [executor.submit(worker, data) for data in data_list]
    for future in as_completed(futures):
        result = future.result()
        # 处理结果

end_time = time.time()
print(f"总耗时: {end_time - start_time:.2f} 秒")
```

## 相关主题

- [[Python_Logging]] - Python 日志系统
- [[Python_Flask]] - Web 框架中的并发
- [[Python_Selenium]] - 自动化测试中的并发
- [[Python_OpenCV]] - 图像处理中的并行计算
