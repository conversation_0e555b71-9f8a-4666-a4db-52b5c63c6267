# QEMU Plugin 插桩原理

---



> https://blog.csdn.net/JaCenz/article/details/125302647
>
> 



1. 将目标架构指令翻译为`QEMU TCG`

    `accel/tcg/translator.c:translator_loop` 函数, 输入一个`tb(TranslationBlock)`, 然后在一个大的 while 循环中逐指令的翻译指令. 其中包含两个关键的插件函数: `plugin_gen_tb_start`和`plugin_gen_insn_start` 以及配对的两个函数 `plugin_gen_insn_end` 与 `plugin_gen_tb_end` 

    

2. QEMU 插件中, 支持对 **基本块翻译时插桩** `qemu_plugin_register_vcpu_tb_trans_cb` 以及**基本块执行前插桩** `qemu_plugin_register_vcpu_tb_exec_cb`. 其中:

    `qemu_plugin_register_vcpu_tb_trans_cb` 函数定义如下:

    ```c
    void qemu_plugin_register_vcpu_tb_exec_cb(struct qemu_plugin_tb *tb,
                                              qemu_plugin_vcpu_udata_cb_t cb,
                                              enum qemu_plugin_cb_flags flags,
                                              void *udata)
    {
        if (!tb->mem_only) {
            plugin_register_dyn_cb__udata(&tb->cbs[PLUGIN_CB_REGULAR],
                                          cb, flags, udata);
        }
    }
    ```

    

    `qemu_plugin_register_vcpu_tb_exec_cb` 函数定义如下: 

    ```c
    void qemu_plugin_register_vcpu_insn_exec_cb(struct qemu_plugin_insn *insn,
                                                qemu_plugin_vcpu_udata_cb_t cb,
                                                enum qemu_plugin_cb_flags flags,
                                                void *udata)
    {
        if (!insn->mem_only) {
            plugin_register_dyn_cb__udata(&insn->cbs[PLUGIN_CB_INSN][PLUGIN_CB_REGULAR],
                                          cb, flags, udata);
        }
    }
    ```

    二者同时调用了关键函数 `plugin_register_dyn_cb__udata`, 定义如下:

    ```c
    void plugin_register_dyn_cb__udata(GArray **arr,
                                       qemu_plugin_vcpu_udata_cb_t cb,
                                       enum qemu_plugin_cb_flags flags,
                                       void *udata)
    {
        struct qemu_plugin_dyn_cb *dyn_cb = plugin_get_dyn_cb(arr);
    
        dyn_cb->userp = udata;
        /* Note flags are discarded as unused. */
        dyn_cb->f.vcpu_udata = cb;
        dyn_cb->type = PLUGIN_CB_REGULAR;
    }
    ```

    该函数的功能为, 从输入的`GArray **arr` 中获取一个空的 `qemu_plugin_dyn_cb` 插件动态回调结构体指针, 然后将该回调设置为当前传入的回调函数 `qemu_plugin_vcpu_udata_cb_t cb`

    那么关键的`GArray **arr`结构体是什么? 设置的回调函数又是合适调用的呢? 

3. `plugin_gen_insn_start` 函数的实现如下:

    ```c
    void plugin_gen_insn_start(CPUState *cpu, const DisasContextBase *db)
    {
        struct qemu_plugin_tb *ptb = tcg_ctx->plugin_tb;
        struct qemu_plugin_insn *pinsn;
    
        pinsn = qemu_plugin_tb_insn_get(ptb, db->pc_next);
        tcg_ctx->plugin_insn = pinsn;
        plugin_gen_empty_callback(PLUGIN_GEN_FROM_INSN);
    
        /*
         * Detect page crossing to get the new host address.
         * Note that we skip this when haddr1 == NULL, e.g. when we're
         * fetching instructions from a region not backed by RAM.
         */
        if (ptb->haddr1 == NULL) {
            pinsn->haddr = NULL;
        } else if (is_same_page(db, db->pc_next)) {
            pinsn->haddr = ptb->haddr1 + pinsn->vaddr - ptb->vaddr;
        } else {
            if (ptb->vaddr2 == -1) {
                ptb->vaddr2 = TARGET_PAGE_ALIGN(db->pc_first);
                get_page_addr_code_hostp(cpu->env_ptr, ptb->vaddr2, &ptb->haddr2);
            }
            pinsn->haddr = ptb->haddr2 + pinsn->vaddr - ptb->vaddr2;
        }
    }
    ```

    大概的过程为, 从当前**TCG线程** 的TCG 上下文`tcg_ctx`中, 获取`插件TB qemu_plugin_tb`, 然后再从插件的基本快中生成一个指定指令的`插件指令 qemu_plugin_insn`, 该结构体的定义如下:

    ```c
    struct qemu_plugin_insn {
        GByteArray *data;
        uint64_t vaddr;
        void *haddr;
        GArray *cbs[PLUGIN_N_CB_TYPES][PLUGIN_N_CB_SUBTYPES];
        bool calls_helpers;
    
        /* if set, the instruction calls helpers that might access guest memory */
        bool mem_helper;
    
        bool mem_only;
    };
    ```

    其中 ` GArray *cbs[PLUGIN_N_CB_TYPES][PLUGIN_N_CB_SUBTYPES]; ` 即为 ` plugin_register_dyn_cb__udata` 传入的 `arr`; 那么 `tcg_ctx->plugin_insn` 是如何添加到`tb`中的呢?  `qemu_plugin_tb_insn_get`在创建`qemu_plugin_insn` 时会自动添加到`tb`中;

    

    ~~在 TCG Translator 执行 `disas_insn`函数时, 会调用`accel/tcg/translator.c:plugin_insn_append`函数, 此函数会将当前 TCG 上下文中的`tcg_ctx->plugin_insn` 添加到 GArray 中~~



4. QEMU Plugin 中的函数是如何调用的呢? 一个动态的调用栈如下:

    ```GCC Backtrace
    libins.so!vcpu_insn_exec_before(unsigned int cpu_index, void * udata) (\home\lizhenghao\Workspace\qemu-tcg-plugin\contrib\plugins\ins.c:77)
    code_gen_buffer (未知源:0)
    cpu_tb_exec(CPUState * cpu, TranslationBlock * itb, int * tb_exit) (\home\lizhenghao\Workspace\qemu-tcg-plugin\accel\tcg\cpu-exec.c:470)
    cpu_loop_exec_tb(CPUState * cpu, TranslationBlock * tb, vaddr pc, TranslationBlock ** last_tb, int * tb_exit) (\home\lizhenghao\Workspace\qemu-tcg-plugin\accel\tcg\cpu-exec.c:932)
    cpu_exec_loop(CPUState * cpu, SyncClocks * sc) (\home\lizhenghao\Workspace\qemu-tcg-plugin\accel\tcg\cpu-exec.c:1053)
    cpu_exec_setjmp(CPUState * cpu, SyncClocks * sc) (\home\lizhenghao\Workspace\qemu-tcg-plugin\accel\tcg\cpu-exec.c:1070)
    cpu_exec(CPUState * cpu) (\home\lizhenghao\Workspace\qemu-tcg-plugin\accel\tcg\cpu-exec.c:1096)
    tcg_cpus_exec(CPUState * cpu) (\home\lizhenghao\Workspace\qemu-tcg-plugin\accel\tcg\tcg-accel-ops.c:75)
    rr_cpu_thread_fn(void * arg) (\home\lizhenghao\Workspace\qemu-tcg-plugin\accel\tcg\tcg-accel-ops-rr.c:261)
    qemu_thread_start(void * args) (\home\lizhenghao\Workspace\qemu-tcg-plugin\util\qemu-thread-posix.c:541)
    libpthread.so.0!start_thread (未知源:0)
    libc.so.6!clone (未知源:0)
    ```

    简而言之, 在 ` cpu_tb_exec` 执行 tb 时, 会通过`tcg/tci.c:tcg_qemu_tb_exec` 调用 JIT 生成的代码动态的调用插件中的函数. 







