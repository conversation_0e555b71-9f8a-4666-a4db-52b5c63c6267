# VMWare

---



### 强制关闭目录下的所有虚拟机

```bash
:: 关闭命令的回显, 即不在控制台中打印当前执行的命令本身
@echo off

:: 改变控制台的目录到当前 bat 文件所在的目录
::     /d               表示强制切换盘符, Windows 正常状态下 cd 命令不能够切换盘符
::     %~dp0     获取当前 bat 文件的路径, 其中 `%0` 指的是批处理文件本身，`~d` 获取驱动器字母，`~p` 获取路径
cd /d %~dp0

:: 遍历当前目录中的所有 *.vmx 文件, 并进行相应的操作
::      /R                   表示递归遍历目录以及子目录
::      %%G            表示当前正在处理的文件
::      in (*.vmx)   通过文件通配符匹配符合条件的路径
for /R %%G in (*.vmx) do (
	echo Stopping  "%%G"
    "C:\Program Files (x86)\VMware\VMware Workstation\vmrun.exe" stop "%%G" hard
    if ERRORLEVEL 1 (
        echo Error stopping VM: "%%G"
    ) else (
        echo Stop "%%G" Successfully
    )
    echo.
)

echo.
echo.

pause

```

