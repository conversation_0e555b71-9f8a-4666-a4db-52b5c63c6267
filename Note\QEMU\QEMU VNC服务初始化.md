# QEMU VNC

---



```c
static int vnc_display_listen(VncDisplay *vd,
                              SocketAddressList *saddr_list,
                              SocketAddressList *wsaddr_list,
                              Error **errp);
```



```shell
-vnc :24,websocket=15901 
```





其中, `wsaddr_list` 存储了 `websocket` 的列表

> http://vnc.maikebuke.com:80/vnc?host=*************&port=15901&encrypt=0&reconnect=1&reconnect_delay=600&path=

