# WAJI 项目：自定义堆分配器识别与利用

> What Allocator Just In-use? - 自定义堆分配器的自动化识别与漏洞利用系统

## 📋 目录

- [[#项目概述]]
- [[#技术架构]]
- [[#核心技术]]
- [[#实现细节]]
- [[#相关研究]]

---

## 项目概述

### 研究背景

在现代应用程序中，各种各样的 **Dynamic Memory Allocator**（动态内存分配器，DMA）被广泛使用。不同种类的应用程序对 DMA 有着不同的需求：

- **内存密集型程序**：希望分配器越快越好
- **安全敏感应用**：希望即使代码中存在内存破坏漏洞，堆分配器也能确保程序控制流不被篡改

开发人员在选择堆分配器时，需要在**安全性与性能之间找平衡点**，因此大量专业应用程序都没有使用系统内置的堆分配器（如 ptmalloc/glibc），而是使用了 **Custom Memory Allocator**（自定义堆管理器，CMA）。

### 核心问题

与标准库中的分配器不同，CMA 的实现多种多样，这使得我们在分析目标应用时遇到了极大的挑战：

1. **识别困难**：通常只能获取没有符号的二进制文件，从中找到堆漏洞需要大量人工介入
2. **利用复杂**：即使发现了内存破坏漏洞，CMA 程序的利用方式也与标准库不同
3. **分析耗时**：需要人工理解内存管理过程，通过大量调试才能完成漏洞利用

### 解决方案

WAJI 系统能够：

1. **不依赖符号**从二进制程序中识别出 CMA 相关函数
2. **通过破坏后执行**的方法识别 CMA 元数据中的管理字段含义
3. **根据上述信息**为内存破坏漏洞计算目标堆布局
4. **基于堆风水求解器**完成 proof-of-concept (POC) 样本的生成
5. **实现 CMA 程序的漏洞自动利用**

---

## 技术架构

### 系统设计概览

```mermaid
graph TB
    A[二进制程序] --> B[CMA 函数识别]
    B --> C[恢复-执行验证]
    C --> D[元数据结构分析]
    D --> E[破坏-执行分析]
    E --> F[字段语义识别]
    F --> G[目标布局生成]
    G --> H[MAZE 求解器]
    H --> I[POC 生成]
    
    subgraph "CMA 建模"
        B
        C
        D
    end
    
    subgraph "元数据分析"
        E
        F
    end
    
    subgraph "漏洞利用"
        G
        H
        I
    end
```

### 核心贡献

1. **新的识别验证方法**：设计了一种新方法对二进制程序中的函数进行识别并验证，确认其是否为 Allocator
2. **堆元数据建模**：对堆的元数据进行了建模，能够恢复未知 Allocator 中的大部分与利用相关的字段
3. **自动化漏洞利用**：对堆元数据破坏相关的漏洞利用方式进行了建模，修改了 MAZE 原型系统，实现元数据破坏漏洞的 AEG

---

## 核心技术

### 1. CMA 建模

#### 堆操作建模

**基本操作类型**：
- `malloc`：内存分配
- `free`：内存释放  
- `realloc`：内存重新分配
- `calloc`：清零分配

**动态特征识别**：
- **控制流特征**：函数调用模式分析
- **数据流特征**：内存访问模式识别
- **Trace 分析**：从执行轨迹中筛选 CMA 函数

#### CMA 验证机制

**恢复-执行机制**：
针对候选的 CMA 函数 `f`，在 `f` 第一次调用的时刻创建快照，通过强制执行技术验证 `f` 函数的功能是否符合预期。

```c
// 验证伪代码
snapshot_state = create_snapshot_at_first_call(candidate_function);
for (test_case in validation_tests) {
    restore_snapshot(snapshot_state);
    result = force_execute(candidate_function, test_case.input);
    if (!validate_allocator_behavior(result, test_case.expected)) {
        return INVALID_ALLOCATOR;
    }
}
return VALID_ALLOCATOR;
```

### 2. 堆元数据结构分析

#### 元数据建模

**通用元数据结构**：
```c
struct heap_metadata {
    size_t size;           // 块大小
    size_t prev_size;      // 前一块大小
    uint32_t flags;        // 状态标志
    void *next;            // 下一块指针
    void *prev;            // 前一块指针
    uint32_t checksum;     // 完整性校验
    // ... 其他字段
};
```

#### 破坏-执行机制

**字段功能识别**：
破坏 CMA 头部与尾部的字段，然后进行特定的内存操作，通过观察后续的控制流与数据流行为，判断该字段的功能。

```python
def analyze_metadata_field(field_offset, field_size):
    # 1. 创建快照
    snapshot = create_snapshot()
    
    # 2. 破坏指定字段
    corrupt_field(metadata_addr + field_offset, field_size)
    
    # 3. 执行内存操作
    operations = ['malloc', 'free', 'realloc']
    for op in operations:
        try:
            result = execute_operation(op)
            behavior = analyze_behavior(result)
            field_semantics = infer_semantics(behavior)
        except Exception as e:
            # 分析异常类型推断字段功能
            field_semantics = analyze_exception(e)
    
    # 4. 恢复快照
    restore_snapshot(snapshot)
    
    return field_semantics
```

### 3. 漏洞利用自动化

#### Knowledge-based Generator

根据现有的内存破坏能力以及 Metadata 中的字段，计算出能够破坏的字段，并提供给 MAZE 求解。

**目标布局生成**：
```python
class TargetLayoutGenerator:
    def __init__(self, cma_model, vulnerability):
        self.cma_model = cma_model
        self.vulnerability = vulnerability
    
    def generate_target_layout(self):
        # 分析当前堆布局
        current_layout = self.analyze_current_layout()
        
        # 识别可破坏的元数据字段
        corruptible_fields = self.identify_corruptible_fields()
        
        # 计算目标布局
        target_layout = self.calculate_target_layout(
            current_layout, corruptible_fields
        )
        
        return target_layout
    
    def identify_corruptible_fields(self):
        fields = []
        for field in self.cma_model.metadata_fields:
            if self.can_corrupt_field(field):
                fields.append(field)
        return fields
    
    def can_corrupt_field(self, field):
        # 基于漏洞类型判断是否能破坏该字段
        if self.vulnerability.type == "buffer_overflow":
            return field.offset >= self.vulnerability.overflow_start
        elif self.vulnerability.type == "use_after_free":
            return field.is_pointer_field
        return False
```

#### MAZE 集成

**修改后的 MAZE 系统**：
```python
class ModifiedMAZE:
    def __init__(self, target_layout, cma_constraints):
        self.target_layout = target_layout
        self.cma_constraints = cma_constraints
    
    def solve_heap_layout(self):
        # 1. 初始化约束求解器
        solver = ConstraintSolver()
        
        # 2. 添加 CMA 特定约束
        for constraint in self.cma_constraints:
            solver.add_constraint(constraint)
        
        # 3. 添加目标布局约束
        solver.add_target_layout(self.target_layout)
        
        # 4. 求解
        solution = solver.solve()
        
        # 5. 生成堆风水序列
        heap_feng_shui = self.generate_heap_operations(solution)
        
        return heap_feng_shui
    
    def generate_poc(self, heap_feng_shui):
        poc = POCGenerator()
        poc.add_heap_setup(heap_feng_shui)
        poc.add_vulnerability_trigger()
        poc.add_payload()
        return poc.generate()
```

---

## 实现细节

### QEMU 集成

> 参考：[[QEMU Edit]]、[[WAJI 关键 HOOK 点]]

**关键 Hook 点**：
```c
// 内存分配 Hook
void hook_malloc(target_ulong addr, target_ulong size) {
    // 记录分配信息
    allocation_record_t record = {
        .addr = addr,
        .size = size,
        .timestamp = get_timestamp(),
        .call_stack = capture_call_stack()
    };
    
    // 分析元数据
    analyze_metadata(addr - METADATA_OFFSET, size);
    
    // 更新堆布局模型
    update_heap_model(&record);
}

// 内存释放 Hook
void hook_free(target_ulong addr) {
    // 查找对应的分配记录
    allocation_record_t *record = find_allocation(addr);
    if (record) {
        // 分析释放行为
        analyze_free_behavior(record);
        
        // 更新堆模型
        update_heap_model_on_free(record);
    }
}
```

### 性能基准测试

> 参考：[[mimalloc-benchmark]]

**测试框架**：
```bash
# 编译不同的分配器版本
make ALLOCATOR=ptmalloc
make ALLOCATOR=mimalloc  
make ALLOCATOR=tcmalloc
make ALLOCATOR=jemalloc

# 运行基准测试
./benchmark --allocator=mimalloc --test=allocation_speed
./benchmark --allocator=mimalloc --test=fragmentation
./benchmark --allocator=mimalloc --test=memory_usage
```

---

## 相关研究

### 常见自定义堆管理器

> 参考：[[一些常见的自定义堆管理器]]

**主要类型**：

| 分配器 | 特点 | 应用场景 |
|--------|------|----------|
| **mimalloc** | 高性能，低碎片 | 通用应用 |
| **tcmalloc** | 线程缓存，快速分配 | 多线程应用 |
| **jemalloc** | 内存分析，低碎片 | 大型应用 |
| **Hoard** | 多处理器优化 | 并行计算 |
| **nedmalloc** | Windows 优化 | Windows 应用 |

### 网络服务模型

> 参考：[[网络服务模型]]

**内存管理模式**：
- **池化分配**：预分配内存池
- **对象池**：复用对象实例
- **栈分配**：基于栈的快速分配
- **区域分配**：按区域管理内存

### 学术资源

> 参考：[[术语整理]]

**关键术语**：
- **Heap Feng Shui**：堆风水，控制堆布局的技术
- **Metadata Corruption**：元数据破坏
- **Use After Free**：释放后使用漏洞
- **Double Free**：双重释放漏洞
- **Heap Overflow**：堆溢出漏洞

---

## 🔗 相关链接

- [[二进制安全与逆向工程]]
- [[系统虚拟化与仿真]]
- [[编译器技术与程序分析]]
- [[学术资源与方法论]]

---

## 📝 项目状态

- **当前阶段**：原型系统开发
- **主要挑战**：
  - 提高 CMA 识别准确率
  - 优化元数据分析效率
  - 扩展支持的分配器类型
- **下一步计划**：
  - 完善 MAZE 集成
  - 增加更多测试用例
  - 性能优化

---

*最后更新：2025-06-16*
*项目版本：v0.1-alpha*
