# opencv-python

---





### 1. 安装

```shell
pip install opencv-python numpy
```



### 2. 基本使用

- 打开图片
- 边缘提取
- 腐蚀
- 膨胀
- 模板匹配(子图像识别)

```python
import cv2
import numpy as np

# 读取图像并提取边缘
image = cv2.imread('image.jpg', cv2.IMREAD_GRAYSCALE)


# 0~255
threshold1 = 125
threshold2 = 250
edges = cv2.Canny(image, threshold1, threshold2)


# 定义腐蚀核大小和迭代次数
kernel_size = (3, 3)
iterations = 1


# 创建腐蚀核
kernel = cv2.getStructuringElement(cv2.MORPH_RECT, kernel_size)
# 对边缘图像进行腐蚀操作
eroded_edges = cv2.erode(edges, kernel, iterations=iterations)


# 创建膨胀核
kernel = cv2.getStructuringElement(cv2.MORPH_RECT, kernel_size)
# 对图像进行膨胀操作
dilated_image = cv2.dilate(image, kernel, iterations=iterations)



template = cv2.imread('template.jpg')
# 将子图像转换为灰度图像
template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
# 使用模板匹配方法进行匹配 (注意此时两个图片都应该是灰度图像)
result = cv2.matchTemplate(edges, template_gray, cv2.TM_CCOEFF_NORMED)
# 设置匹配的阈值
threshold = 0.8
# 返回匹配结果中的位置信息
locations = np.where(result >= threshold)
# 标记匹配到的区域
for loc in zip(*locations[::-1]):
    cv2.rectangle(image, loc, (loc[0] + template.shape[1], loc[1] + template.shape[0]), (0, 255, 0), 2)

```





