# 命令行工具与脚本

> Linux 命令速查、Shell 脚本与自动化工具集

## 📋 目录

- [[#Linux 命令速查]]
- [[#文件与目录操作]]
- [[#系统管理]]
- [[#网络工具]]
- [[#开发工具]]

---

## Linux 命令速查

> 参考：[[Linux 命令速查]]

### SSH 与远程连接

```bash
# SSH 密钥生成
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub

# SSH 连接
ssh user@hostname
ssh -p 2222 user@hostname  # 指定端口
ssh -i ~/.ssh/custom_key user@hostname  # 指定密钥

# SSH 隧道
ssh -L 8080:localhost:80 user@remote_host  # 本地端口转发
ssh -R 8080:localhost:80 user@remote_host  # 远程端口转发
ssh -D 1080 user@remote_host  # SOCKS 代理
```

### 用户与权限管理

```bash
# 用户管理
sudo useradd username
sudo passwd username
sudo adduser username  # 更友好的方式

# 用户组管理
sudo usermod -aG groupname username
sudo gpasswd -a username groupname
sudo gpasswd -d username groupname

# 权限管理
chmod 755 file
chmod u+x,g+r,o-w file
chown user:group file
chown -R user:group directory/

# 查看权限
ls -la
stat file
```

### 进程管理

```bash
# 查看进程
ps aux
ps aux | grep process_name
pgrep -f process_name
pidof process_name
top
htop

# 进程控制
kill PID
kill -9 PID
killall process_name
pkill -f process_name
killall -u username  # 杀死用户所有进程

# 后台任务
nohup command &
screen -S session_name
tmux new-session -s session_name
jobs
fg %1
bg %1
disown %1
```

### 系统监控

```bash
# 内存使用
free -h
cat /proc/meminfo

# CPU 使用
ps auxw | head -1; ps auxw | sort -rn -k3 | head -10

# 磁盘使用
df -h
du -sh directory/
du -sm directory/  # 查看文件数量

# 网络连接
ss -tulpn
netstat -tulpn
lsof -i
lsof -i :80

# 系统信息
uname -a
lscpu
lsblk
lsusb
lspci
```

---

## 文件与目录操作

### 文件搜索

```bash
# 按文件名搜索
find . -name "*.txt"
find . -type f -name "pattern"
find . -type d -name "dirname"

# 按内容搜索
grep -r "pattern" /path/
grep -rnw '/path/' -e 'pattern'
grep --include=\*.{c,h} -rnw '/path/' -e "pattern"
grep --exclude=\*.o -rnw '/path/' -e "pattern"
grep --exclude-dir={dir1,dir2} -rnw '/path/' -e "pattern"

# 组合搜索
find . -name "*.log" -exec grep -l "error" {} \;
find . -type f -exec grep -sH 'text' {} \; 2>/dev/null
```

### 文件操作

```bash
# 文件查看
cat file
less file
head -n 20 file
tail -n 20 file
tail -f file  # 实时查看

# 文件编辑
sed -i 's/old/new/g' file  # 替换
sed -i '/pattern/d' file   # 删除匹配行
awk '{print $1}' file      # 提取第一列

# 文件比较
diff file1 file2
vimdiff file1 file2
cmp file1 file2
```

### 压缩与解压

```bash
# tar 压缩
tar -zcvf archive.tar.gz directory/
tar -zxvf archive.tar.gz
tar -czf archive.tar.gz -C /target/dir target_name

# 并行压缩（需要 pigz）
tar --use-compress-program="pigz -k -p8" -cvf archive.tgz directory/
tar --use-compress-program="pigz -k -p8" -xvf archive.tgz

# 其他格式
zip -r archive.zip directory/
unzip archive.zip
7z a archive.7z directory/
7z x archive.7z

# deb 包处理
sudo dpkg -i package.deb
dpkg -x package.deb extract_dir/
dpkg -e package.deb control_dir/
for file in *.deb; do dpkg-deb -x $file ${file%%.deb}; done
```

---

## 系统管理

### 服务管理

```bash
# systemctl 服务管理
sudo systemctl start service_name
sudo systemctl stop service_name
sudo systemctl restart service_name
sudo systemctl reload service_name
sudo systemctl enable service_name
sudo systemctl disable service_name

# 查看服务状态
systemctl status service_name
systemctl is-active service_name
systemctl is-enabled service_name
systemctl list-units --type=service
systemctl list-unit-files --type=service

# 查看日志
journalctl -u service_name
journalctl -f  # 实时查看
journalctl --since "2023-01-01" --until "2023-01-02"
```

### 磁盘管理

```bash
# 磁盘信息
lsblk
fdisk -l
parted -l

# 挂载管理
mount /dev/sdb1 /mnt/usb
umount /mnt/usb
mount -a  # 挂载 fstab 中的所有

# 磁盘扩容
sudo parted /dev/sda
(parted) p
(parted) resizepart <partition_number>
(parted) q
sudo resize2fs /dev/sda1

# RAM 磁盘
mkdir /mnt/ramdisk
mount -t tmpfs -o size=256M tmpfs /mnt/ramdisk
# 自动挂载：编辑 /etc/fstab
# tmpfs /mnt/ramdisk tmpfs defaults,size=5G 0 0
```

### 网络配置

```bash
# 网络接口
ip addr show
ip link show
ifconfig

# 路由管理
ip route show
route -n
ip route add default via ***********

# 网桥配置
sudo ip link add br0 type bridge
sudo ip tuntap add dev tap0 mode tap
sudo ip link set dev tap0 master br0
sudo ip link set dev br0 up
sudo ifconfig br0 ***********00/24
```

---

## 网络工具

### 网络诊断

```bash
# 连通性测试
ping hostname
ping -c 4 hostname
traceroute hostname
mtr hostname  # 持续的 traceroute

# 端口扫描
nmap hostname
nmap -p 80,443 hostname
nmap -sS hostname  # SYN 扫描

# DNS 查询
nslookup hostname
dig hostname
dig @******* hostname
host hostname
```

### 网络监控

```bash
# 网络连接
ss -tulpn
netstat -tulpn
lsof -i
lsof -i :80

# 网络统计
ss -s
netstat -s
iftop  # 实时网络流量
nethogs  # 按进程显示网络使用

# 抓包分析
sudo tcpdump -i eth0 -w capture.pcap
sudo tcpdump -i eth0 host ***********
sudo tcpdump -i eth0 port 80
sudo tcpdump -r capture.pcap
```

### 文件传输

```bash
# scp 文件传输
scp file user@host:/path/
scp -r directory/ user@host:/path/
scp user@host:/path/file ./

# rsync 同步
rsync -av source/ destination/
rsync -av --progress source/ user@host:destination/
rsync -av --delete source/ destination/  # 删除目标中多余文件

# wget/curl 下载
wget https://example.com/file
wget -c https://example.com/file  # 断点续传
curl -O https://example.com/file
curl -L https://example.com/file  # 跟随重定向
```

---

## 开发工具

### Git 操作

```bash
# 基本操作
git clone https://github.com/user/repo.git
git add .
git commit -m "commit message"
git push origin main
git pull origin main

# 分支管理
git branch
git branch new_branch
git checkout new_branch
git checkout -b new_branch
git merge branch_name
git branch -d branch_name

# 历史查看
git log --oneline
git log --graph --oneline --all
git show commit_hash
git diff
git diff --cached

# 远程管理
git remote -v
git remote add origin https://github.com/user/repo.git
git fetch origin
git push -u origin main
```

### 编译工具

```bash
# GCC 编译
gcc -o program source.c
gcc -Wall -Wextra -O2 -o program source.c
gcc -g -o program source.c  # 调试信息

# 查看编译信息
gcc -v -E - < /dev/null  # 查看预定义宏
gcc -print-search-dirs   # 查看搜索路径
ldd program             # 查看动态链接库

# Make 构建
make
make clean
make install
make -j$(nproc)  # 并行编译
```

### 调试工具

```bash
# GDB 调试
gdb ./program
gdb -p PID
gdb --args ./program arg1 arg2

# 系统调用追踪
strace ./program
strace -f -o trace.log ./program
strace -e trace=open,read,write ./program

# 性能分析
perf record ./program
perf report
time ./program
/usr/bin/time -v ./program
```

---

## Shell 脚本示例

### 系统监控脚本

```bash
#!/bin/bash
# 系统监控脚本

LOG_FILE="/var/log/system_monitor.log"
THRESHOLD_CPU=80
THRESHOLD_MEM=80
THRESHOLD_DISK=90

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

check_cpu() {
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    if (( $(echo "$CPU_USAGE > $THRESHOLD_CPU" | bc -l) )); then
        log_message "WARNING: High CPU usage: ${CPU_USAGE}%"
    fi
}

check_memory() {
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ "$MEM_USAGE" -gt "$THRESHOLD_MEM" ]; then
        log_message "WARNING: High memory usage: ${MEM_USAGE}%"
    fi
}

check_disk() {
    while read output; do
        usage=$(echo $output | awk '{print $5}' | cut -d'%' -f1)
        partition=$(echo $output | awk '{print $6}')
        if [ $usage -gt $THRESHOLD_DISK ]; then
            log_message "WARNING: High disk usage on $partition: ${usage}%"
        fi
    done <<< "$(df -h | grep -vE '^Filesystem|tmpfs|cdrom')"
}

main() {
    log_message "Starting system monitoring"
    check_cpu
    check_memory
    check_disk
    log_message "System monitoring completed"
}

main "$@"
```

### 自动备份脚本

```bash
#!/bin/bash
# 自动备份脚本

SOURCE_DIR="/home/<USER>/important_data"
BACKUP_DIR="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="backup_${DATE}.tar.gz"
RETENTION_DAYS=7

create_backup() {
    echo "Creating backup: $BACKUP_NAME"
    tar -czf "${BACKUP_DIR}/${BACKUP_NAME}" -C "$(dirname $SOURCE_DIR)" "$(basename $SOURCE_DIR)"
    
    if [ $? -eq 0 ]; then
        echo "Backup created successfully"
    else
        echo "Backup failed"
        exit 1
    fi
}

cleanup_old_backups() {
    echo "Cleaning up backups older than $RETENTION_DAYS days"
    find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete
}

main() {
    mkdir -p "$BACKUP_DIR"
    create_backup
    cleanup_old_backups
    echo "Backup process completed"
}

main "$@"
```

---

## 🔗 相关链接

- [[系统管理与运维]]
- [[调试工具集合]]
- [[网络与安全工具]]
- [[开发工具链]]

---

*最后更新：2025-06-16*
