# Create directory structure for Obsidian migration

$dirs = @(
    "Obsidian/10_Software_Security/02_Binary_Exploitation_Pwn/Core_Concepts",
    "Obsidian/10_Software_Security/02_Binary_Exploitation_Pwn/Techniques", 
    "Obsidian/10_Software_Security/02_Binary_Exploitation_Pwn/CTF_Challenges",
    "Obsidian/10_Software_Security/02_Binary_Exploitation_Pwn/Environment_Setup",
    "Obsidian/10_Software_Security/03_Dynamic_Analysis",
    "Obsidian/10_Software_Security/04_Reverse_Engineering/IDA_Pro",
    "Obsidian/10_Software_Security/05_Code_Audit_and_Sanitizers",
    "Obsidian/20_Development_and_Tools/01_Python/Libraries",
    "Obsidian/20_Development_and_Tools/01_Python/Internal_Mechanisms",
    "Obsidian/20_Development_and_Tools/02_LLVM/Clang",
    "Obsidian/20_Development_and_Tools/02_LLVM/LLVM_Pass",
    "Obsidian/20_Development_and_Tools/03_QEMU/Internal_Principles",
    "Obsidian/20_Development_and_Tools/03_QEMU/Usage",
    "Obsidian/20_Development_and_Tools/03_QEMU/Customization",
    "Obsidian/20_Development_and_Tools/04_DevOps_and_Management/Docker",
    "Obsidian/20_Development_and_Tools/04_DevOps_and_Management/Git",
    "Obsidian/20_Development_and_Tools/04_DevOps_and_Management/NixOS",
    "Obsidian/20_Development_and_Tools/04_DevOps_and_Management/System_Administration",
    "Obsidian/20_Development_and_Tools/05_General_Tools",
    "Obsidian/20_Development_and_Tools/06_Linux_CLI",
    "Obsidian/20_Development_and_Tools/07_Web_Development/JavaScript",
    "Obsidian/20_Development_and_Tools/08_Databases",
    "Obsidian/20_Development_and_Tools/09_Embedded_and_IoT",
    "Obsidian/20_Development_and_Tools/10_Operating_Systems/Windows",
    "Obsidian/20_Development_and_Tools/10_Operating_Systems/Java",
    "Obsidian/30_Research_and_Projects/01_Project_WAJI",
    "Obsidian/30_Research_and_Projects/02_Project_Nuki",
    "Obsidian/30_Research_and_Projects/03_General_Research",
    "Obsidian/30_Research_and_Projects/04_LLM_Prompt_Engineering",
    "Obsidian/40_Miscellaneous",
    "Obsidian/50_Journal"
)

foreach ($dir in $dirs) {
    New-Item -ItemType Directory -Path $dir -Force
    Write-Host "Created: $dir"
}

Write-Host "Directory structure created successfully!"
