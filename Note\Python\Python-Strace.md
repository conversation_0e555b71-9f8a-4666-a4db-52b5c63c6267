# Strace With Python







```python


import re
import subprocess

def run_strace(cmd: str, ptn: re.Pattern, event: callable) -> bool:
    args = list(filter(lambda x: bool(x), cmd.split()))
    # 运行strace并捕获其输出
    process = subprocess.Popen(
        ['strace', '-f', '-e', 'trace=all', '-s', '4096', '-v', '--'] + args, 
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )
    # 按行读取子进程的标准输出和标准错误输出
    for line in iter(process.stderr.readline, b''):
        if ptn.match(line):
            event(process, line)
    try:
        process.stdout.close()
        # process.stderr.close()
        process.wait()
    except Exception as e:
        print(e)
    return True

def execute_script(p: subprocess.Popen, line: bytes) -> None:
    # 这里是您希望在监测到特定行为时执行的脚本代码
    print(line)
    ptn = re.compile(b"\[pid (\d+)\] (\w+)\((.*?)\) += (-?\d+?) ?(.*?)\\n")
    try:
        tmp = ptn.findall(line)
        print(tmp)
        p.kill()
    except Exception as e:
        print(e)
        p.kill()
    checker = subprocess.Popen(
        ["ls", "-lh", f"/proc/{pid}/fd/"],
        stdout=subprocess.PIPE,
    )
    result = checker.stdout.read()
    print(result)
    return

# 调用示例
cmd = "./configure --enable-debug --enable-waji --enable-plugins"
run_strace(
    cmd=cmd,
    ptn=re.compile(b"open"),
    event=execute_script,
)

```



