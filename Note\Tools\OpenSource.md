# 开源软件

---





| 软件名称       | 说明                                                   | 开源地址                                       | 附录 |
| -------------- | ------------------------------------------------------ | ---------------------------------------------- | ---- |
| Clutch         | 一个IOS可执行文件打包器                                | https://github.com/KJCracks/Clutch             |      |
| 洛雪音乐       | 音乐聚合搜索播放器                                     | https://github.com/lyswhut/lx-music-desktop    |      |
| 知了音乐       | PWA播放器，自己部署服务端                              | https://github.com/mebtte/cicada               |      |
| gdb gui        | 一个web前端的GDB调试器                                 | https://github.com/cs01/gdbgui                 |      |
| clumsy         | Windows网络包恶化工具                                  | https://github.com/jagt/clumsy                 |      |
| Sandboxie      | Windows沙箱工具                                        | https://github.com/sandboxie-plus/Sandboxie    |      |
| Interception   | 一个I/O设备的虚拟控制驱动                              | https://github.com/oblitum/Interception        |      |
| PowerToys      | 微软出品的Windows窗口加强套件                          | https://github.com/microsoft/PowerToys         |      |
| DynamoRIO      | 开源动态分析插桩平台                                   | https://github.com/DynamoRIO/dynamorio         |      |
| AFL++          | 模糊测试工具                                           | https://github.com/AFLplusplus/AFLplusplus     |      |
| panda          | 基于QEMU的动态分析框架                                 | https://github.com/panda-re/panda              |      |
| IDA Tenet      | 一个IDA上的阅读trace的插件                             | https://github.com/gaasedelen/tenet            |      |
| what the fuzz  | 基于快照的Windows的模糊fuzzer                          | https://github.com/0vercl0k/wtf                |      |
| WLLVM          | 一个基于python的llvm wrapper，用于生成LLVM编译过程     | https://github.com/travitch/whole-program-llvm |      |
| Bear           | 用于记录clang的编译过程生成编译数据库                  | https://github.com/rizsotto/Bear               |      |
| fuzzing        | 谷歌的fuzz教程                                         | https://github.com/google/fuzzing              |      |
| rustdesk       | 一个rust写的远程桌面                                   | https://github.com/rustdesk/rustdesk           |      |
| nes-py         | python3写的任天堂 NES 模拟器                           | https://github.com/Kautenja/nes-py             |      |
| icecream       | python调试神器                                         | https://github.com/gruns/icecream              |      |
| viztracer      | Python代码trace分析器                                  | https://github.com/gaogaotiantian/viztracer    |      |
| rr             | rr希望取代gdb，能够记录程序异常，然后基于recording调试 | https://github.com/rr-debugger/rr              |      |
| Triton         | 动态二进制分析库，提供python api                       | https://github.com/JonathanSalwan/Triton       |      |
| DecompilerMC   | Minecraft的JDK反编译工具                               | https://github.com/hube12/DecompilerMC         |      |
| TrafficMonitor | Windows下的网速监控器，支持硬件状态监控                | https://github.com/zhongyang219/TrafficMonitor |      |
| pvztools       | PvZ原版修改器                                          | https://github.com/lmintlcx/pvztools           |      |
| QEMU           | QEMU虚拟机                                             | https://github.com/qemu/qemu                   |      |
| v86            | 在浏览器中模拟x86架构处理器                            | https://github.com/copy/v86                    |      |
| aria2          | 轻量级多线程下载器                                     | https://github.com/aria2/aria2                 |      |
| Motrix         | 一个基于aria2的下载器                                  | https://github.com/agalwood/Motrix             |      |
| PicGo          | 基于electron的图片上传/图床管理工具                    | https://github.com/Molunerfinn/PicGo           |      |
| frp            | 一个反向端口代理服务器                                 | https://github.com/fatedier/frp                |      |
| VLC            | 开源的视频播放器                                       | https://www.videolan.org/vlc/                  |      |
| ffmpeg         | 开源的音视频处理框架                                   | https://git.ffmpeg.org/ffmpeg.git              |      |
| PuTTY          | 开源的SSH与telnet客户端                                | https://git.tartarus.org/simon/putty.git       |      |
| WinSCP         | 开源的ftp，ssh，webdav客户端                           | https://github.com/winscp/winscp               |      |
| CopyQ          | 跨平台剪切板记录工具                                   | https://github.com/hluk/CopyQ                  |      |
| Zotero         | 论文阅读器                                             | https://github.com/zotero/zotero               |      |
| Joplin         | 全平台的笔记todo同步器                                 | https://github.com/laurent22/joplin            |      |
| ImHex          | 开源的二进制编辑器                                     | https://github.com/WerWolv/ImHex               |      |
| Numba          | 开源的Python与Numpy的JIT编译器                         | http://numba.pydata.org/                       |      |

