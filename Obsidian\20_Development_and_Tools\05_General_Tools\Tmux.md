# Tmux - Terminal Multiplexer

## 概述

<GeminiOptimizationFrom> Note/Tools/Tmux.md </GeminiOptimizationFrom>

`tmux` 是一个终端多路复用器（Terminal MUltipleXer），这意味着它允许你在一个终端窗口中打开多个会话，每个会话都有自己的窗口和面板。`tmux` 提供了丰富的命令和快捷键，供你管理和切换这些会话、窗口和面板。

## 配置

`tmux` 的配置可以通过修改 `~/.tmux.conf` 文件来进行，你可以在这个文件中设置你喜欢的颜色主题、增加或修改快捷键等。

## 基本概念

### 层次结构

- **会话（Session）**：每个 `tmux` 进程都有一个或多个会话，每个会话都是独立的，可以有自己的窗口和名字
- **窗口（Window）**：窗口就像一个标签页，每个窗口都运行在指定的会话中。一个会话可以有多个窗口，每个窗口都有一个索引号和（可选的）名字
- **面板（Pane）**：面板是窗口中的一个子窗口，你可以在一个窗口中分割出多个面板

### 架构图示

```
Session
├── Window 0
│   ├── Pane 0
│   ├── Pane 1
│   └── Pane 2
├── Window 1
│   ├── Pane 0
│   └── Pane 1
└── Window 2
    └── Pane 0
```

## 常用命令

### 会话管理

```bash
# 新建会话
tmux
tmux new

# 新建有名字的会话
tmux new-session -s session_name

# 连接到会话
tmux attach          # 连接到上一次的会话
tmux a               # 简写形式
tmux attach -t session_name  # 连接到指定名字的会话

# 列出所有会话
tmux ls

# 杀死会话
tmux kill-session -t session_name
```

### 窗口和面板操作

```bash
# 新建窗口
tmux new-window -t session_name:1 'command'

# 分割窗口
tmux split-window -v    # 垂直分割
tmux split-window -h    # 水平分割
```

## 常见参数含义

| 参数 | 含义 |
|------|------|
| `-c` | 让新创建的窗口在指定的目录中打开 |
| `-n` | 定义一个窗口的名字 |
| `-d` | 使 `tmux` 在后台运行 |
| `-t` | 指定目标窗口，用于挂载或发送键 |
| `-s` | 为一个新会话指定名称 |
| `-v` | 垂直分割窗口 |
| `-h` | 水平分割窗口 |

## 常用快捷键

`tmux` 的操作基本都是通过按下 `Ctrl-b`（前缀键）来进行的，以下是一些常用的快捷键：

### 窗口操作

| 快捷键 | 功能 |
|--------|------|
| `Ctrl-b c` | 新建一个窗口 |
| `Ctrl-b n` | 切换到下一个窗口 |
| `Ctrl-b l` | 切换到最后一个窗口 |
| `Ctrl-b p` | 切换到上一个窗口 |
| `Ctrl-b 0-9` | 切换到对应编号的窗口 |

### 面板操作

| 快捷键 | 功能 |
|--------|------|
| `Ctrl-b "` | 垂直分割当前窗口创建新面板 |
| `Ctrl-b %` | 水平分割当前窗口创建新面板 |
| `Ctrl-b o` | 切换到下一个面板 |
| `Ctrl-b ;` | 切换到上一个面板 |
| `Ctrl-b 方向键` | 在面板间移动 |
| `Ctrl-b x` | 关闭当前面板 |

### 会话操作

| 快捷键 | 功能 |
|--------|------|
| `Ctrl-b d` | 分离当前会话 |
| `Ctrl-b s` | 列出所有会话 |
| `Ctrl-b $` | 重命名当前会话 |

## 实用功能

### 1. 在外界 Shell 中创建 Tmux Session 并运行多个命令

```shell
# 创建会话并在多个窗口中运行不同命令
tmux new-session -s mysession -d 'ping 8.8.8.8'
tmux new-window  -t mysession:1  'ping 223.5.5.5'
tmux new-window  -t mysession:2  'top'

# 连接到会话
tmux a -t mysession

# 使用 CTRL+B n(下一个 Window)/p(上一个 Window)/<number>(指定编号的窗口) 切换 Window

# 清理会话
tmux kill-session -t mysession
```

### 2. 获取 tmux 指定 pane 的内容

#### 命令行方式

```shell
# 获取指定面板内容
tmux capture-pane -t fuzz_16_databank -p
tmux capture-pane -t fuzz_16_databank:0 -p
```

**参数说明**：
- `-t`：指定会话名、窗口索引和面板索引
- `-p`：打印面板的内容

#### Python 脚本方式

```python
import subprocess

def get_tmux_pane_content(session_name, window_index, pane_index):
    # 构建 tmux 命令
    cmd = ["tmux", "capture-pane", "-t", f"{session_name}:{window_index}.{pane_index}", "-p"]
    
    # 执行命令并捕获输出
    try:
        output = subprocess.check_output(cmd, text=True)
        return output
    except subprocess.CalledProcessError as e:
        print(f"Error capturing tmux pane: {e}")
        return None

# 示例使用
session_name = "my_session"
window_index = 0
pane_index = 1

content = get_tmux_pane_content(session_name, window_index, pane_index)
if content:
    print("Pane Content:\n", content)
```

## 高级配置

### 基本配置文件 (~/.tmux.conf)

```bash
# 设置前缀键为 Ctrl-a
set -g prefix C-a
unbind C-b
bind C-a send-prefix

# 启用鼠标支持
set -g mouse on

# 设置窗口和面板索引从 1 开始
set -g base-index 1
setw -g pane-base-index 1

# 快速重载配置文件
bind r source-file ~/.tmux.conf \; display "Config reloaded!"

# 分割窗口的快捷键
bind | split-window -h
bind - split-window -v

# 面板间移动的快捷键
bind h select-pane -L
bind j select-pane -D
bind k select-pane -U
bind l select-pane -R
```

## 使用场景

### 1. 开发环境

```bash
# 创建开发会话
tmux new-session -s dev -d
tmux new-window -t dev:1 -n "editor" "vim"
tmux new-window -t dev:2 -n "server" "npm start"
tmux new-window -t dev:3 -n "logs" "tail -f app.log"
tmux attach -t dev
```

### 2. 服务器管理

```bash
# 创建监控会话
tmux new-session -s monitor -d
tmux new-window -t monitor:1 -n "htop" "htop"
tmux new-window -t monitor:2 -n "logs" "journalctl -f"
tmux new-window -t monitor:3 -n "network" "iftop"
```

### 3. 长时间任务

```bash
# 运行长时间任务
tmux new-session -s backup -d "rsync -av /home/<USER>/ /backup/"
# 分离会话，任务继续在后台运行
# 稍后重新连接查看进度
tmux attach -t backup
```

## 故障排除

### 常见问题

1. **会话丢失**：检查 tmux 服务是否运行
2. **快捷键不响应**：检查前缀键配置
3. **颜色显示异常**：设置正确的 TERM 环境变量

### 调试命令

```bash
# 查看 tmux 信息
tmux info

# 查看所有键绑定
tmux list-keys

# 查看所有命令
tmux list-commands
```

## 相关主题

- [[Strace]] - 系统调用跟踪工具
- [[FFmpeg]] - 多媒体处理工具
- [[Python_多进程与多线程]] - Python 并发编程
- [[Linux_命令速查]] - Linux 命令参考
