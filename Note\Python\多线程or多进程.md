# 多线程与多进程

---



## 多线程 `threading`

在 Python 中启动一个新的线程

```python
import time
from threading import Thread

t = Thread(target=time.sleep, args=(5, ))
t.start()
```





## 多进程 `multiprocessing`

启动进程与阻塞进程

```python
import time
from multiprocessing import Process

process_list = []
for task in tasks:
    p = Process(target=run_task, args=(task, ))
    p.start()
    process_list.append(p)
    time.sleep(2)

for p in process_list:
    p.join()			
```



启动线程池, 控制同时使用的资源

```python
import time
from multiprocessing import Pool

num_process = 3

with Pool(num_process) as pool:
    for delay in [1,2,3,4,5]:
        res = pool.apply_async(time.sleep, (delay, ))
    pool.close()
    pool.join()
```



## 子任务 `subprocess`

创建一个子任务, 

```python
import os
import subprocess

args = ["ping", "-c", "10", "-i", "0.5", "*******"]

process = subprocess.Popen(
    # 参数列表
    # 如果 shell=True,  传入一个 str 类型的命令
    # 如果 shell=False, 传入一个 list[str] 类型的命令
    args=args,
    # 是否在新建终端中执行命令
    # 如果 shell=True, 则无法获取执行命令的 pid
    shell=False,
    # stderr 与 stdout 的写入位置
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    # 选择工作目录
    cwd="/home/<USER>/",
    # 设置运行的环境变量
    env=os.environ,
)

print(process.pid)

while True:
    c = process.stdout.read(1)
    if c:
        c = c.decode("UTF-8")
        print(c, end="")
        sys.stdout.flush()
    else:
        break
```













