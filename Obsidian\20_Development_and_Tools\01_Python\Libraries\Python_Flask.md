# Python Flask

## 概述

<GeminiOptimizationFrom> Note/Python/Flask.md </GeminiOptimizationFrom>

Flask 是一个轻量级的 Python Web 框架，基于 Werkzeug WSGI 工具包和 Jinja2 模板引擎。它设计简洁，易于扩展，是构建 Web 应用程序的优秀选择。

## 安装依赖

```shell
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple ddddocr==1.4.7 flask==2.1.0 flask-cors==3.0.10
```

## 基础应用示例

### 完整示例代码

```python
import os
import re
import sys
import ddddocr
from base64 import b64decode
from flask_cors import cross_origin
from flask import Flask, request, Response

ocr = ddddocr.DdddOcr(show_ad=False)

app = Flask(__name__)

@app.before_request
def flask_before_request():
    headers = dict(request.headers)
    print(headers)
    print("Flask Before Request Hook")

@app.after_request
def flask_after_request(response: Response):
    print("Flask After Request Hook")
    print(type(response))
    sys.stdout.flush()
    return response

@app.route("/", methods=["GET"])
def index():
    return "Hello World!"

JS_BASE64_PATTERN = re.compile(r"^data:[\w\d]+/[\w\d]+;base64,(.+)$")

@app.route("/captcha", methods=["POST"])
@cross_origin()
def handle_captcha():
    # 检查域名是否来自ucas
    assert "ucas.ac.cn" in request.headers.get("Origin")
    # 获取数据
    data = request.get_json()
    assert isinstance(data, dict)
    image_base64 = data.get("image")
    assert re.match(JS_BASE64_PATTERN, image_base64)
    image_base64 = re.findall(JS_BASE64_PATTERN, image_base64)[0]
    image = b64decode(image_base64)
    # ddddocr分类
    captcha_texts = ocr.classification(image)
    resp = {"code": 0, "data": captcha_texts}
    return resp

if __name__ == "__main__":
    app.run("0.0.0.0", "8000", debug=True)
```

## 核心概念

### 1. Flask 应用实例

```python
app = Flask(__name__)
```

创建 Flask 应用实例，`__name__` 参数帮助 Flask 确定应用的根路径。

### 2. 路由装饰器

```python
@app.route("/", methods=["GET"])
def index():
    return "Hello World!"
```

- `@app.route()`: 定义 URL 路由
- `methods`: 指定允许的 HTTP 方法
- 函数返回值作为 HTTP 响应

### 3. 请求钩子

#### before_request
```python
@app.before_request
def flask_before_request():
    headers = dict(request.headers)
    print(headers)
    print("Flask Before Request Hook")
```

在每个请求处理之前执行，常用于：
- 身份验证
- 日志记录
- 请求预处理

#### after_request
```python
@app.after_request
def flask_after_request(response: Response):
    print("Flask After Request Hook")
    print(type(response))
    sys.stdout.flush()
    return response
```

在每个请求处理之后执行，常用于：
- 响应修改
- 日志记录
- 清理工作

### 4. 跨域支持

```python
from flask_cors import cross_origin

@app.route("/captcha", methods=["POST"])
@cross_origin()
def handle_captcha():
    # 处理跨域请求
    pass
```

使用 `flask-cors` 扩展处理跨域请求。

## 请求处理

### 1. 获取请求数据

```python
# 获取 JSON 数据
data = request.get_json()

# 获取请求头
origin = request.headers.get("Origin")

# 获取表单数据
form_data = request.form.get("key")

# 获取 URL 参数
param = request.args.get("param")
```

### 2. 数据验证

```python
# 类型检查
assert isinstance(data, dict)

# 正则表达式验证
JS_BASE64_PATTERN = re.compile(r"^data:[\w\d]+/[\w\d]+;base64,(.+)$")
assert re.match(JS_BASE64_PATTERN, image_base64)

# 域名验证
assert "ucas.ac.cn" in request.headers.get("Origin")
```

## 实际应用场景

### 验证码识别服务

示例中展示了一个验证码识别服务：

1. **接收 Base64 图像数据**
2. **验证请求来源域名**
3. **解码图像数据**
4. **使用 ddddocr 进行 OCR 识别**
5. **返回识别结果**

### 安全考虑

1. **域名验证**: 检查请求来源
2. **数据格式验证**: 使用正则表达式验证数据格式
3. **类型检查**: 确保数据类型正确
4. **异常处理**: 使用 assert 进行快速验证

## 配置和部署

### 开发环境

```python
if __name__ == "__main__":
    app.run("0.0.0.0", "8000", debug=True)
```

- `host="0.0.0.0"`: 监听所有网络接口
- `port="8000"`: 指定端口
- `debug=True`: 启用调试模式

### 生产环境

```python
# 使用 WSGI 服务器
from waitress import serve
serve(app, host='0.0.0.0', port=8000)
```

## 最佳实践

### 1. 项目结构

```
project/
├── app.py          # 主应用文件
├── config.py       # 配置文件
├── requirements.txt # 依赖列表
├── templates/      # 模板目录
├── static/         # 静态文件
└── blueprints/     # 蓝图模块
```

### 2. 配置管理

```python
import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

app.config.from_object(Config)
```

### 3. 错误处理

```python
@app.errorhandler(404)
def not_found(error):
    return {"error": "Not found"}, 404

@app.errorhandler(500)
def internal_error(error):
    return {"error": "Internal server error"}, 500
```

## 相关主题

- [[Python_多进程与多线程]] - Python 并发编程
- [[Python_Logging]] - Python 日志系统
- [[HTML_CSS代码片段]] - 前端开发
- [[JS_Fetch_API]] - 前端 API 调用
