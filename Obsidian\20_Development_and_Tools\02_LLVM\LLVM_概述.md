# LLVM 概述

## 概述

<GeminiOptimizationFrom> Note/LLVM/LLVMPass-01_关于LLVM.md </GeminiOptimizationFrom>

LLVM 是一个现代的**可重用编译器**（reusable compiler）以及**工具链**（toolchain）技术集合。尽管名称为 Low Level Virtual Machine（低层次虚拟机），但它已经和虚拟机没有任何关系。

## 重要资源

- **官方网站**: https://llvm.org/
- **官方文档**: https://llvm.org/docs/
- **GitHub 仓库**: https://github.com/llvm/llvm-project
- **Gitee 镜像**: https://gitee.com/mirrors/llvm-project

## LLVM 子项目

### 核心组件

| 项目名称 | 说明 |
| -------- | ---- |
| **The LLVM Core libraries** | LLVM 核心库提供了独立于源码与目标架构的现代化优化器（optimizer），这些库围绕着 LLVM 中间语言（LLVM Intermediate Representation）代码进行构建 |
| **Clang** | Clang 是"LLVM 原生 C/C++/Objective-C"编译器。其中包含了"[Clang 静态分析器](https://clang-analyzer.llvm.org/)"与"[clang-tidy](https://clang.llvm.org/extra/clang-tidy/)"用于自动发现代码中的 bug。也可以使用 Clang 前端库，对 C/C++ 代码进行解析 |

### 调试和运行时

| 项目名称 | 说明 |
| -------- | ---- |
| **The LLDB project** | LLDB 是一个高性能调试器（Debugger），建立在 LLVM 与 Clang 之上。同时使用了 Clang AST、表达式语法分析器（Parser）、LLVM JIT（运行时）、LLVM disassembler 等，比 GDB 快得多 |
| **compiler-rt** | 提供了高性能的底层代码生成器支持组件，同时提供了一些运行时测试工具（AddressSanitizer, ThreadSanitizer, MemorySanitizer, DataFlowSanitizer） |

### 标准库和语言支持

| 项目名称 | 说明 |
| -------- | ---- |
| **The libc++ and libc++ ABI projects** | 提供了与标准库中一致性的高性能实现，对 C++11 和 C++14 全面支持 |
| **libclc** | 实现一个 OpenCL 标准库 |
| **OpenMP** | 并行编程支持 |

### 专用工具

| 项目名称 | 说明 |
| -------- | ---- |
| **MLIR** | 多层次中间表示（Multi-Level Intermediate Representation） |
| **The polly project** | 高级循环和数据局部性优化 |
| **klee** | 实现了一个"符号虚拟机"（symbolic virtual machine），即符号执行技术。Klee 能够在检测到错误时生成对应的测试用例 |
| **LLD** | 是一个新的链接器（Linker），是系统默认链接器的替代品，速度会快很多 |
| **BOLT** | 是一个"链接后端优化器"（post-link optimizer） |

## LLVM 架构特点

### 1. 模块化设计

LLVM 采用模块化设计，各个组件可以独立使用：
- **前端**：负责将源代码转换为 LLVM IR
- **优化器**：对 LLVM IR 进行各种优化
- **后端**：将 LLVM IR 转换为目标机器代码

### 2. 中间表示（IR）

LLVM IR 是 LLVM 的核心：
- **语言无关**：支持多种编程语言
- **架构无关**：支持多种目标架构
- **可读性强**：类似汇编语言，便于理解和调试

### 3. 优化能力

LLVM 提供了强大的优化能力：
- **编译时优化**：传统的编译器优化
- **链接时优化**：跨模块优化
- **运行时优化**：JIT 编译优化

## 主要应用场景

### 1. 编译器开发

- **新语言前端**：为新编程语言开发编译器前端
- **DSL 编译器**：领域特定语言的编译器
- **代码转换工具**：源到源的代码转换

### 2. 代码分析

- **静态分析**：使用 Clang 静态分析器
- **代码质量检查**：使用 clang-tidy
- **安全分析**：漏洞检测和安全审计

### 3. 性能优化

- **编译器优化**：各种编译时优化
- **运行时优化**：JIT 编译和动态优化
- **链接时优化**：LTO（Link Time Optimization）

### 4. 研究和教学

- **编译器研究**：编译器技术研究平台
- **程序分析**：程序分析技术研究
- **教学工具**：编译原理教学

## 开发生态

### 1. 社区支持

- **活跃社区**：大量开发者贡献
- **丰富文档**：详细的官方文档
- **定期发布**：稳定的发布周期

### 2. 工业应用

- **Apple**：Xcode 和 Swift 编译器
- **Google**：Android NDK 和 Chrome
- **Intel**：ICC 编译器
- **NVIDIA**：CUDA 编译器

### 3. 学术研究

- **顶级会议**：LLVM 开发者会议
- **研究论文**：大量学术研究基于 LLVM
- **开源项目**：众多基于 LLVM 的开源项目

## 学习路径

### 1. 基础知识

- **编译原理**：理解编译器基本概念
- **LLVM IR**：学习 LLVM 中间表示
- **工具使用**：熟悉 LLVM 工具链

### 2. 实践项目

- **简单 Pass**：编写 LLVM Pass
- **前端开发**：为简单语言开发前端
- **优化技术**：实现特定优化

### 3. 高级应用

- **JIT 编译**：运行时代码生成
- **静态分析**：程序分析工具开发
- **DSL 设计**：领域特定语言设计

## 相关主题

- [[LLVM_安装]] - LLVM 安装和配置
- [[Clang_组件]] - Clang 编译器详解
- [[Pass入门]] - LLVM Pass 开发入门
- [[LLVM_常见陷阱]] - LLVM 开发注意事项
