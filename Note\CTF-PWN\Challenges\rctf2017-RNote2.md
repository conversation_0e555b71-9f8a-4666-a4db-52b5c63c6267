# RCTF 2017 - RNote2

---



- 题目描述
    - https://ctftime.org/task/4181
- Binary 以及 Glibc 下载
    - https://github.com/DhavalKapil/ctf-writeups/tree/master/rctf-2017/rnote2
- 参考的中文 Writeup
    - https://blog.csdn.net/qq_29343201/article/details/72642581

- CTF 常见利用技巧
    - https://www.bupaqiang.com/webaq/1694.html

### glibc 实现的基本信息

**realloc**

> https://linux.die.net/man/3/realloc



**strncat**

> https://linux.die.net/man/3/strncat
>
> The strncat() function is similar, except that: it will use at most n bytes from src;

```c
char *strncat(char *dest, const char *src, size_t n);
```

也就是说, `strncat` 的 `n` 是指至多从 `src` 拷贝 `n` 字节, 而不是字符串的总长度为 `n` 字节. 



### 基本描述

```
welcome to RNote service!
***********************
1.Add new note
2.Delete a note
3.List all note
4.Edit a note
5.Expand a note
6.Exit
***********************
Your choice:
```

1. 创建一个新 Note
    - 会要求输入一个 size
    - malloc(0x28), malloc(size)
2. 删除 LinkedList 链上指定索引的 Note
    - free(size), free(0x28)
3. 列出 LinkedList 链上的所有 Note 信息
4. 当`editFlag ==0`时, 编辑笔记, 并且将 editFlag 设置为 1
    - 直接使用 read(0, buf, 1) 读取并拷贝
5. 当`editFlag ==0`时, 扩展笔记, 并且将 editFlag 设置为 1
    - 修改后的长度不能够超过256
    - 使用`realloc`分配
    - 使用`strncat`函数扩展字符串
6. 退出



其中, NoteStruct 的结构如下:

```c
struct rhNoteStruct {
    size_t editFlag;
    size_t length;
    struct rhNoteStruct * next;
    struct rhNoteStruct * prev;
    char * content;
}
```

其中, 填充内容的函数采用单字节读取 `stdin` 填充, 因此可以用于输入非 '\0' 结尾字符串:

```c
__int64 __fastcall utilReadString(char *a1, signed int a2)
{
  char buf; // [rsp+1Bh] [rbp-5h] BYREF
  int i; // [rsp+1Ch] [rbp-4h]

  for ( i = 0; i < a2; ++i )
  {
    if ( read(0, &buf, 1uLL) <= 0 )
      break;
    a1[i] = buf;
    if ( a1[i] == '\n' )
      break;
  }
  return (unsigned int)i;
}
```



### 漏洞描述

该样本的检查较为全面, 在分配释放前后都有长度以及使用检查, 很难造成 UAF 等攻击. 

然而, 在 `5.expandNote` 函数内, 使用 `realloc` 扩展内存, 并且使用 `strncat` 函数拼接内存会造成堆溢出. 具体构造溢出的步骤如下:

1. s1 = malloc(6); memset(s1, 'A', 6); // 此时字符串的内容为 "AAAAAA"
2. free(s1);
3. s2 = malloc(3); memset(s2, 'B', 3); // 此时会 reuse s1 chunk, 字符串的内容为 "BBBAAA"
4. realloc(s2, 7); strncat(s2, "CCCC", 7-3); // 此时会在 s1 chunk 后追加, 内容为 "BBBAAACCCC\0"

至此, 发生了堆溢出行为. 



### 利用思路

**1. libc 地址/top_chunk地址信息泄露**

`ListAllNotes (Choice 3)` 函数中使用了 `printf` 函数输出  `content` 指针指向的 Chunk 的内容. 

针对一个 `Small Bin Chunk` **P**, 当我们 `free(P)`时, 其 `fd` 与 `bk` 指针会被修改. 此时, 如果控制 fd 指向 `top_chunk`, 然后再 `content = malloc(SIZE)`, 此时不向 `Content` 中填充任何内容, 即可通过 `printf` 函数打印出 `fd` 指针的内容. 

要想控制 P 的 fd 指向 top_chunk, 只需要保证以下两个条件:

- P 是程序启动后第一个被 Free 的 Chunk, 否则该指针会指向其他堆上的地址. ( Free时会将当前的Chunk添加到 Unsorted Bins 链表的第一个位置 )
- `nextchunk = chunk_at_offset(p, size);` 物理后邻接的 `nextchunk`  处于 inuse 状态, 否则可能触发 top_chunk 合并

此时直接 printf reused chunk 即可打印出 `top_chunk` 地址

```c
a = malloc(0x100);
keep = malloc(0x100);
free(a);
b = malloc(0x100);

printf("%s", b); // leak
```





**2. 堆溢出, 构造 Overlay Chunk**

| Bin Type  | User Data Size Range (Arch 32)     | Chunk Size Range (Arch 32) | User Data Size Range (Arch 64)         | Chunk Size Range (Arch 64) |
| --------- | ---------------------------------- | -------------------------- | -------------------------------------- | -------------------------- |
| Fast Bin  | Default: [8, 64]; Maximum: [8, 80] |                            | Default: [16, 128]; Maximum: [16, 160] |                            |
| Small Bin | [16, 504]; [0x10, 0x01F8]          |                            | [32, 1016]; [0x20, 0x03F8]             |                            |

对 64 位系统而言: 

- bins[0]: Unsorted Bins List
- bins[1-63]: Small Bins List, 从 1~63 直接每个 bin list 的大小相差 16 字节[0x10, 0x20, 0x30, ...]
- bins[64-253]: Large Bins List



该题目中, AddNote(size) 操作相当于以下两次内存分配操作. 在 64 位下, `0x28 in Range(0x0, 0x80)`, 属于 Fast Bins :

- malloc(0x28)
- malloc(size)





**3. 修改 Overlay Chunk 的 NoteStruct 的 content 指针, 使其为 &__realloc_hook**

**4. 通过 Edit 修改 Overlay Chunk 的 content 内容, 即修改原本的 _realloc_hook 函数指针为 system 的函数指针 **

**5. 调用 realloc 函数, 触发system(原本的 _realloc_hook 指针)函数**













**ptmalloc off-by-NULL**

!!!! 以下内容仅针对 glibc-2.29 版本以前 !!!!

默认情况下, `PTMalloc Chunk` Header 中仅包含 `size` 字段, 且最后一位为 `P(PREV_INUSE)` 标志位. 可以通过修改此标志位将该 Chunk 的 PREV

> https://sourceware.org/git/?p=glibc.git;a=blob;f=malloc/malloc.c;h=d20d5955db4d814b73a5b1829d1bc7874c94024d;hb=ab30899d880f9741a409cbc0d7a28399bdac21bf#l1182

```
    chunk-> +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
            |             Size of previous chunk, if allocated            | |
            +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
            |             Size of chunk, in bytes                       |M|P|
      mem-> +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
            |             User data starts here...                          .
            .                                                               .
            .             (malloc_usable_size() bytes)                      .
            .                                                               |
nextchunk-> +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
            |             Size of chunk                                     |
            +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

当 Off-by-NULL 发生时, 可以清空末尾的 `P(PREV_INUSE)`以及 `M(IS_MMAPPED)*` 标志位



> Free Chunk 时的前向合并与后向合并
>
> https://sourceware.org/git/?p=glibc.git;a=blob;f=malloc/malloc.c;h=d20d5955db4d814b73a5b1829d1bc7874c94024d;hb=ab30899d880f9741a409cbc0d7a28399bdac21bf#l4001
>
> https://www.anquanke.com/post/id/208407

#### 

当 `free(p)` 发生时

#### A.1 前向合并

```c
// 1. 根据 P 的地址以及 P 的 SIZE 获取物理后邻接的 nextchunk(p->bk)
size = chunksize (p);
nextchunk = chunk_at_offset(p, size);
// 2. 获取 nextchunk 是否 inuse (p->bk->bk->inuse)
nextsize = chunksize(nextchunk);
nextinuse = inuse_bit_at_offset(nextchunk, nextsize);
// 3. 若 nextinuse 为假, 即 nextchunk 未被使用(为 free 状态)
/* consolidate forward */
if (!nextinuse) {
    // 4. 从 av 中取出 nextchunk, 并拓展当前 Chunk(P) 的 SIZE (合并 Chunk)
    unlink(av, nextchunk, bck, fwd);
    size += nextsize;
} else
    // 5. 如果 nextchunk 在使用中, 则清除其 INUSE 标志位
    clear_inuse_bit_at_offset(nextchunk, 0);
```

针对当前释放的 Chunk P, 通过 `P+chunksize(P)` 获取 nextchunk. 如果 nextchunk->PREV_INUSE==0, 即 Chunk P 处于 Free 状态, 则从 





#### A.2 后向合并

```c
// 1. 如果 P->fd 处于未使用(free)的状态
/* consolidate backward */
if (!prev_inuse(p)) {
    // 2. 获取物理前邻接的上一个 Chunk 的大小 p->prev_size
	prevsize = p->prev_size;
    // 3. 扩展当前的 SIZE, 与前邻接的 free chunk 合并
	size += prevsize;
    // 4. 更新 P 指针到前邻接 Chunk 位置
	p = chunk_at_offset(p, -((long) prevsize));
    // 5. 从 av 中取出 P
	unlink(av, p, bck, fwd);
}
```















