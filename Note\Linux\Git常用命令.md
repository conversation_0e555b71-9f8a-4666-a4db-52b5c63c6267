# Git 相关

---



配置用户名和邮箱

```shell
git config --global user.name "l<PERSON><PERSON><PERSON><PERSON>-docker"
git config --global user.email "<EMAIL>"
```



配置文件比较器(git difftool)

```shell
git config --global diff.tool vscode  #设置之后运行的名字
git config --global difftool.vscode.cmd 'code --wait --diff $LOCAL $REMOTE'
git config --global -e # 进行查看对比，看看是否添加正确
git difftool 6bb4a8a47a43f35a345f107227fcd6abed59e62c # 使用
```





取消追踪指定文件

```shell
git rm --cached '*.pyc'
```





克隆完整的仓库

```shell
# 根据项目名称创建项目目录
mkdir <repository-name> && cd <repository-name>
# 通过 `--mirror` 选项克隆包含所有分支的镜像
git clone --mirror <repository-url> .git
# 也可以使用下述 `--bare` 方法克隆
# git clone --bare <repository-url> .git
# `--mirror` 克隆的是裸仓库(bare), 将其变为常规仓库
git config --bool core.bare false
# 重新设置 GIT 的 HEAD 指针
git reset --hard
# 查看所有分支
git branch
```





https://colab.research.google.com/github/facefusion/facefusion-colab/blob/master/facefusion.ipynb





### 在 dev 分支上往 main 分支合并

1. 暂存未提交的更改： 首先，使用 `git stash` 将你在 `dev` 中未提交的更改暂存起来，以免影响合并过程：

```bash
(git dev) git stash
```
2. 切换到 `main`： 然后，切换到 `main`：

```bash
(git dev) git checkout main
```
3. 合并 `dev` 中的提交到 `main`： 将 `dev` 的提交合并到 `main` 中，但此时 `dev` 中未提交的代码不会影响这个合并：

```bash
(git main) git merge dev
```
4. 切换回 `dev`： 合并完成后，切换回 `dev`：

```bash
(git main) git checkout dev
```
5. 恢复未提交的更改： 使用 `git stash pop` 恢复之前暂存的未提交的更改：	

```bash
(git dev) git stash pop
```
这样，你的提交代码会被合并到 `main`，而未提交的代码仍然保留在 `dev` 中。



### Git 在 dev 开发合并到 main 分支， 并且只合并一次提交

```
# Step 01. 在 main 分支创建出 dev 分支
​```
git branch <dev>
git checkout <dev>
​```

# Step 02. 在 <dev> 分支工作, 修改代码
​```
# 提交一些文件
touch feature_01.txt
git add * && git commit -m "Adding Feature 01"
touch feature_02.txt
git add * && git commit -m "Adding Feature 02"
​```

# Step 03. 在 <dev> 分支 rebase 到主分支
​```
git checkout <dev>
git rebase -i master
​```
此时会弹出一个文本编辑器, 将分支节点保留为 pick, 其他提交修改为 squash
修改完后关闭编辑器
​```
# 如果冲突
git rebase --continue
​```

# Step 04. 在主分支中合并 <dev> 分支
​```
git checkout main
git merge <dev>
​```

# Step 05. 查看悬空分支
​```
git fsck --unreachable --no-reflogs
​```
```









