# 常用样式

---





### CSS 相对宽度 根据宽度动态计算高度 矩形

```html
<body>
  <div class="square">
    <img class="square-img" src="https://www.bilibili.com/favicon.ico" alt="">
  </div>
</body>
```

```css
.square {
	position: relative;
	width: 20%;
	padding-top: 20%;
	height: 0;
	background-color: #f8f8f8;
	border-radius: 10%;
	border: 2px solid black;
	overflow: hidden;
	margin: 5vh auto;
}

.square-img {
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	position: absolute;
}
```

https://codepen.io/iscas/pen/XWPNgbX



