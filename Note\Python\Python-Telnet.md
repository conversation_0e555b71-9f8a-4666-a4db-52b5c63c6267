# Python Telnet

---





```python

import time
import telnetlib

# 连接到Telnet服务器
HOST = "127.0.0.1"  # 服务器IP地址
PORT = 5769  # 服务器端口号

tn = telnetlib.Telnet()

command = "loadvm static_alloc\n"
    
while True:
    try:
        tn.open(HOST, PORT)
    except Exception as e:
        time.sleep(1)
        print("Trying reconnect...")
        continue

    # 检查连接状态
    if tn.sock is not None:
        output = tn.read_until(b"(qemu)", timeout=5)
        print(output)
        tn.write(command.encode("ASCII"))
        output = tn.read_until(b"(qemu)", timeout=5)
        print(output)

    while True:
        try:
            tn.write("help\n".encode("ASCII"))
            time.sleep(1)
        except:
            break
    # 关闭Telnet连接
    tn.close()

```

