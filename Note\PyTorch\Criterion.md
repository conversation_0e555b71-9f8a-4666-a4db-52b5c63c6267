# PyTorch Loss Function

---



### 基本定义

- N - Batch Size
- C - Number of classes
- T - Input sequence length (Time step count)
- S - Target sequence length of longest target in batch (padding length)



### CrossEntropyLoss 

**交叉熵**

- 输入: (N, C); 其中每个元素是对应类别的预测值 (logits)
- 目标: (N, ); 其中每个元素是预期的(真实的)类别索引, 范围是(0, C-1)
    - 或, 目标: (N, C), 其中每个元素是预期的 class probabilities
- 输出: 标量/数值; 表示平均的损失值

```python
import torch
import torch.nn as nn

criterion = nn.CrossEntropyLoss()

input  = torch.tensor([[2.5, 0.3, 0.2], [1.2, 1.3, 1.8]], requires_grad=True)	# logits
target = torch.tensor([0, 2], dtype=torch.long)		# 每个样本的真实类别(标签值)

loss = criterion(input, target)
print(f"Loss: {loss.item()}")
```



### CTCLoss

> https://pytorch.org/docs/stable/generated/torch.nn.CTCLoss.html

**目的**:





**Parameters**

- blank: 指定空白标签
- reduction: Literal['none', 'mean', 'sum'], 指定输出损失值的**归约**方式
- zero_infinity: 是否将无限损失和关联的梯度归零. 当输入太短无法与目标对齐时, 损失值可能会变成 Infinite 



**Shape**

- `log_probs`: (T, N, C), 即 (时间步, 批大小, 类别大小). 其中每个元素是对应类别的概率

- `targets`: (N, S), 即 (批大小, 最长的期望输出长度). 其中每个元素是对应的类别索引

- `input_lengths`: (N, ), 即(批大小, ). 其中每个元素表示神经网络输出的长度(即<=T). 可以直接填充 T

- `target_lengths`: (N), 即(批大小, ). 其中每个元素为真实标签序列的长度, 每个值都应该 <=S

    > 请注意, 这里的 input 指的是 loss_function 的input, 而不是神经网络的 input. 
    >
    > 所以其**实际上对应神经网络的输出**

- `output`: 标量/数值, 如果 `reduction in ['mean', 'sum']`; (N,) 如果 `reduction='none'`

```python
T = 50
C = 20
N = 16
S = 30

input = torch.randn(T, N, C).log_softmax(dim=2).detach().requires_grad_()
target = torch.randint(low=1, high=C, size=(N, S), dtype=torch.long)
input_lengths = torch.full(size=(N,), fill_value=T, dtype=torch.long)
S_min = 10 # 构造一个期望输出长度, 用于表示目标输出长度从 10~30 不等
target_lengths = torch.randint(low=S_min, high=S, size=(N,), dtype=torch.long)

criterion = nn.CTCLoss()
loss = criterion(input, target, input_lengths, target_lengths)
loss.backward()
```





































