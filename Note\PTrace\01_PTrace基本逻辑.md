





1. 声明 `ptrace` 捕获

    使用 `PTRACE_TRACEME` 声明子进程需要被父进程捕获

    ```c
    if (ptrace(PTRACE_TRACEME, 0, NULL, NULL) == -1) {
        perror("[RH]: Error in ptrace traceme. ");
        exit(EXIT_FAILURE);
    }
    ```

    

2. 父进程事件捕获

    - 通常而言, 需要在父进程中使用 `wait` 函数等待子进程的状态
    - 默认情况下, 子进程可以通过发送信号触发 `wait` 函数

    ```c
    raise(SIGSTOP);  // #include <signal.h>
    ```

    - 如果需要捕获系统调用事件, 则需要在 `wait` 前注册 `PTRACE_SYSCALL` 监听

    ```c
    ptrace(PTRACE_SYSCALL, pid, NULL, NULL);
    ```

    

3. 继续子进程

    - 默认情况下,  可以使用 `PTRACE_CONT` 继续一个被 `SIGSTOP` 中断的进程

        ```c
        if (ptrace(PTRACE_CONT, pid, NULL, NULL) == -1) {
            perror("ptrace continue1");
            exit(EXIT_FAILURE);
        }
        ```

    - 特别要注意的是, `PTRACE_SYSCALL` 相当于以下操作:

        - 继续子进程
        - 在子进程未来**调用系统调用**以及**系统调用返回**时生成信号

4. 系统调用

    - `x86 `架构中, 系统调用的处理依赖 `RAX` 寄存器, 分为两种:

        - `ORIG_RAX`: `syscall` 指令执行**前**的 `RAX` 寄存器值
        - `RAX`: `syscall` 指令执行**后**的 `RAX` 寄存器值

    - 一个有效的方法判断当前是在执行前还是执行后, 可以通过判断执行后的 RAX 寄存器值. 

        - 若其为 `-ENOSYS(-38)`, 则说明当前是**系统调用发生前**

    - 每次处理系统调用逻辑是, 子进程都会中断, 此时需要再次调用 `PTRACE_SYSCALL` 继续

        ```c
        ptrace(PTRACE_SYSCALL, pid, NULL, NULL);
        ```

    - **注意!!!** 请确保每次 `PTRACE_SYSCALL` 时子进程都处于中断状态, 否则会不起作用.