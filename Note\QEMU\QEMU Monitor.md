# QEMU Monitor

---



- QEMU Machine Protocol (*QMP*)
    - https://wiki.qemu.org/Documentation/QMP
- Human Monitor Interface (HMP)
    - https://wiki.qemu.org/ToDo/HMP



在源码中 `monitor/hmp.c` 位置.

其中, 各个命令的声明与注册位于 `./hmp-commands.hx` 中, 该文件会在 Build 的过程中生成 `build/hmp-commands.h`, 进一步的, 该头文件会在 `monitor/hmp-target.c` 中通过下面的方式引入:

```c
/* Please update hmp-commands.hx when adding or changing commands */
static HMPCommand hmp_info_cmds[] = {
#include "hmp-commands-info.h"
    { NULL, NULL, },
};
```

通过上述方式加载函数列表后, 最终通过下面的方式解析对应的命令:

```c
void handle_hmp_command(MonitorHMP *mon, const char *cmdline) {
    ...
    cmd = monitor_parse_command(mon, cmdline, &cmdline, hmp_cmds);
    ...
}
```

会通过下面的方式产生实际调用:

```c
static void handle_hmp_command_exec(Monitor *mon,
                                    const HMPCommand *cmd,
                                    QDict *qdict)
{
    if (cmd->cmd_info_hrt) {
        hmp_info_human_readable_text(mon,
                                     cmd->cmd_info_hrt);
    } else {
        cmd->cmd(mon, qdict);
    }
}
```





