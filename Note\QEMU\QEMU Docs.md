

> MMIO https://www.qemu.org/docs/master/devel/memory.html#mmio-operations
>
> QEMU Plugin ISCAS Analysis https://blog.csdn.net/JaCenz/article/details/125302647
>
> ISCAS QEMU TCG Plugin https://www.bilibili.com/video/BV1TA411q7aB
>
> QEMU Helper Functions http://brieflyx.me/2020/qemu/qemu-new-helper/
>
> QEMU TCG Explain http://www.serversan.org/thread-46-1-1.html
>
> QEMU Trace https://qemu-project.gitlab.io/qemu/devel/tracing.html
>
> Future Panda/QEMU https://github.com/AndrewFasano/futurepanda
>
> (Panda) Stronger QEMU Plugin https://github.com/qemu/qemu/commit/72c661a7f141ab41fbce5e95eb3593b69f40e246
>
> Discuss of TCG Memory Access https://gitlab.com/qemu-project/qemu/-/issues/1719
>
> 





### QEMU 依赖

```shell
sudo apt-get install python3-pip
sudo apt-get install ninja-build 
sudo apt-get install meson 
sudo apt-get install pkg-config 
sudo apt-get install libglib2.0-dev
sudo apt-get install libpixman-1-dev 
# flex bison
```

