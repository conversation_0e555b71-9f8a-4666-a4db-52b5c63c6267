

# Linux 命令速查

---





### SSH 生成新的密钥

---

```shell
ssh-keygen -t rsa -b 4096
chmod 600 id_rsa
chmod 644 id_rsa.pub
```





### 64 位 Linux 安装 32 位包

---

```shell
sudo dpkg --add-architecture i386
sudo apt-get update
# sudo apt-get install libgnutls28-dev:i386
# sudo apt-get install gcc-multilib g++-multilib
sudo apt-get install <包名称>:i386
```



### 杀死所有名字中包含特定字符串的进程

---

```shell
# 杀死所有指定用户的进程
killall -u <username>
# 杀死所有启动命令中包含 "vscode-server" 的进程
ps aux | grep vscode-server | awk '{print $2}' | xargs sudo kill -9
pkill -9 -f vscode-server
```



### 查看文件二进制内容

```shell
xxd <>.bin
```





### 创建新用户

---

在Linux上创建新用户，需要使用以下命令：

1. useradd：用于添加新用户，并创建用户主目录和默认的系统文件。
      例如：sudo useradd username

2. passwd：用于为用户设置密码。
      例如：sudo passwd username

3. usermod：用于修改用户信息，如用户组、主目录等。
      例如：sudo usermod -aG groupname username （将用户添加到指定用户组）

4. id：用于查看用户的UID和GID。
      例如：id username

5. chown: 用于改变文件或目录权限

      例如： chown <user>:<group> /home/<USER>

<OR>

1. adduser：是一个更友好的用户创建命令，它会提供交互式的方式设置用户信息和密码。
      例如：sudo adduser username

需要注意的是，创建用户需要root权限才能执行。







## 压缩与解压缩

### `deb`包

```shell
# 安装一个 dpkg 包
sudo dpkg -i <name>.deb
# 导出数据信息
dpkg -x <name>.deb <path>/
# 导出控制信息
dpkg -e <name>.deb <path>/
# 解压当前目录下所有的 deb 包
for file in *.deb; do dpkg-deb -x $file ${file%%.deb}; done
```

### 压缩tar包

```bash
# 压缩目录
tar -zcvf <dest>.tar.gz <src>/
# 解压目录
tar -zxvf <name>.tar.gz

# 压缩相对目录
tar -czf <>.tar.gz -C ${TARGET_DIR} ${TARGET_NAME}
```

| flag | 含义                             | 是否必选     |
| ---- | -------------------------------- | ------------ |
| -f   | 指定档案名字, 必须是最后一个参数 | 必选         |
| -c   | 建立压缩档案                     | cxtru 五选一 |
| -x   | 解压                             | cxtru 五选一 |
| -t   | 查看文件内容                     | cxtru 五选一 |
| -z   | 有gzip属性的                     |              |
| -j   | 有bz2属性的                      |              |
| -v   | 控制台中显示过程                 |              |
| -O   | 将文件解开到标准输出             |              |



## 开发相关

```shell
# 查看动态链接库
ldd `which wget` | grep -E "tls|ssl|nss|nspr"
```





### gdb attach 入某名称的进程

```shell
gdb -ex "set confirm off" attach $(ps aux | grep 'afl-fuzz-llvm' | awk 'NR==1{print $2}')
# grep 搜索包含指定字符串的行
# awk 是文本处理语言, 可以处理非常复杂的文本格式. 
#     格式为 awk '匹配模式 {命中行为}'
#     NR==1 表示匹配输入文件的第一行内容
#     {print $2} 表示获取输入中的第二个参数($0表示当前行本身), ps 命令中第二个参数为`进程id`
#     可以使用 `-F,` 指定分隔符为 ",", 或使用 `-F'|'` 指定分隔符为 "|"

# 也可以不使用 grep 直接使用 awk完成指定进程的筛选
# ps aux | awk '/afl-fuzz-llvm/ {lineno++; if(lineno==1) print $2}'
```





## 搜索

**搜索文件名, 使用`find`命令**

| 选项        | 含义                                   |
| ----------- | -------------------------------------- |
| find <PATH> | 搜索的起始目录                         |
| -type       | 搜索的类型, f:文件, d:目录, l:符号链接 |
| -name       | 搜索的pattern                          |



```bash
find . -name "foo*"
```

其他

```bash
find ./ -name "pattern"
find . -type f -name "*.conf"
find . -print | grep -i foo
find / -type f -exec grep -sH 'text-to-find-here' {} \; 2>/dev/null
```





**根据`pattern`搜索文件内容, 使用`grep`命令**

[man grep](https://ss64.com/bash/grep.html)

[Stackoverflow](https://stackoverflow.com/questions/16956810/how-to-find-all-files-containing-specific-text-string-on-linux)

| 选项          | 含义                   |
| ------------- | ---------------------- |
| -r, -R        | 递归搜索               |
| -n            | 显示行号               |
| -w            | 匹配整个单词           |
| -l            | 只给出匹配文件的文件名 |
| -e            | 搜索过程中使用的模式   |
| --exclude     | 添加排除项             |
| --exclude-dir | 添加排除目录           |
| --include     | 只搜索include的项目    |



```bash
# 基本搜索
grep -rnw '/path/to/somewhere/' -e 'pattern'
# 只搜索.c和.h文件
grep --include=\*.{c,h} -rnw '/path/to/somewhere/' -e "pattern"
# 排除.o文件
grep --exclude=\*.o -rnw '/path/to/somewhere/' -e "pattern"
# 排除目录
grep --exclude-dir={dir1,dir2,*.dst} -rnw '/path/to/search/' -e "pattern"
```



## 查看资源占用



| 功能                      | 命令                                             | 说明                          |
| ------------------------- | ------------------------------------------------ | ----------------------------- |
| 查看内存占用              | free -h                                          | Mem: 物理内存；Swap：交换内存 |
| 查看CPU占用最多的10个进程 | ps auxw\|head -1;ps auxw\|sort -rn -k3\|head -10 | -k3 按照第三列排序            |
| 查看系统资源占用          | htop                                             | sudo apt install htop         |
| 查看磁盘占用              | df -h                                            | -hl查看剩余空间               |
| 查看目录大小              | du -sh <dir>                                     |                               |
| 查看目录中的文件数        | du -sm <dir>                                     |                               |
| 查看磁盘IO使用            | iostat -d -x -k 1 10                             |                               |



Python中可以使用`psutil`模块进行查看:

`pip install psutil`

```python
import os
import psutil
pid = os.getpid()
p = psutil.Process(pid)
# 一些使用
p.pid
p.name()
p.cpu_percent()
p.memory_info()
p.memory_percent()
p.cmdline()
```



## 文本快速替换

```
# 将文件中的http替换为https
# "s@<reg_source>@<str_target>g"
sed -i "s@http@https@g" <file>
```



### 将一片内存映射为硬盘

---



要创建一个RAM磁盘，您可以使用以下命令：

```shell
mkdir /mnt/ramdisk
mount -t tmpfs -o size=256M tmpfs /mnt/ramdisk
# 取消挂载
sudo umount /mnt/ramdisk
```

在上述命令中，第一个命令创建一个名为“ramdisk”的目录，用于将RAM磁盘挂载。 第二个命令创建了一个256MB的RAM磁盘，具有tmpfs文件系统，并将其挂载到/mnt/ramdisk目录中。

请注意，RAM磁盘通常只在重新启动后才会消失。



自动挂载一个临时文件系统

```shell
# 1. 编辑 /etc/fstab 配置
vi /etc/fstab
# 添加以下内容:
# tmpfs                                     /mnt/ramdisk    tmpfs   defaults,size=5G  0     0

# 2. 创建目录, 并确保目录本身不可被修改
mkdir -p ramdisk
chmod 777 /mnt/ramdisk/
chattr +i /mnt/ramdisk/

# 3. 挂载目录, 并查看是否成功挂载
mount -a
df -h
# tmpfs           5.0G     0  5.0G   0% /mnt/ramdisk
```





## Linux 磁盘扩容

1. 确保虚拟机的状态不是**链接**而是完整的镜像
2. 在虚拟机中扩展磁盘空间，参考如下：

<img src="https://qiniu.maikebuke.com/202301091733664.png" alt="image-2023010917332456" style="zoom:50%;" />

<img src="https://qiniu.maikebuke.com/202301091735792.png" alt="image-20230109173517721" style="zoom: 50%;" />

3. 相关命令

```bash
# 查看系统分区情况
$ df -h
# 查看具体的磁盘情况
$ sudo fdisk -l
# 对物理磁盘 /dev/sda 进行分区
$ sudo parted /dev/sda
    (parted) p
    # 此处填写上面输出的编号
    (parted) resizepart <id>
    # 标准输入 "yes"
    (parted) q
# 这里需要填写`fdisk -l`命令中查看到的 Linux File System sda编号
$ sudo resize2fs /dev/<sda_id>
# 再次查看分区容量
$ df -h
```



## 记录运行的命令

`strace -f -o my_script.strace -e trace=process my_script.sh`





### SHELL 处理 JSON 数据

解析当前目录下所有的 `JSON` 文件, 提取其中的 `count` 字段, 并将结果进行输出. 

```shell
total=0; for file in *.json; do count=$(jq -r '.count' "$file"); total=$((total+count)); done; echo $total
```





### 关闭 ASLR

```shell
echo 'kernel.randomize_va_space = 0' >> /etc/sysctl.conf
reboot
# 检查是否开启了 ASLR, 如果是 0 则为关闭
cat /proc/sys/kernel/randomize_va_space
```



### 卸载挂载的软盘

如果开机出现`blk_update_request: I/O error, dev df0, sector 0`类似的提示, 是由于`/dev/fd0`挂在了不存在的软盘导致的, 可以编辑`/etc/fstab` 文件, 将其中的加载软盘的代码注释.





### 提高进程优先级

在Linux中，进程的优先级（又称niceness）的范围是-20到19，其中-20表示最高优先级，19表示最低优先级。进程的默认优先级通常为0。如果需要提高某个进程的优先级，可以使用以下方法：

1. 启动进程时设置进程的优先级, 使用 `nice` 命令

```bash
# 设置进程的nice值为-10
nice -n -10 ./my_program
```



2. 修改已经运行的进程的优先级, 使用 `renice` 命令

```bash
# 将进程的优先级提高到-10
renice -n -10 <pid>
```



3. 设置用户的线程优先级

```bash
sudo renice -n -10 -u <用户名>
```



### Linux 添加虚拟网卡与配置网桥

```bash
#/bin/sh
sudo modprobe tun tap
sudo ip link add br0 type bridge
sudo ip tuntap add dev tap0 mode tap
sudo ip tuntap add dev tap1 mode tap
sudo ip link set dev tap0 master br0
sudo ip link set dev tap1 master br0
sudo ip link set dev eno2 master br0
sudo ip link set dev br0 up


sudo ip address delete ************* dev eno2
sudo ip address add ************* dev br0
sudo ifconfig br0 ***************/24
sudo ifconfig tap0 up
sudo ifconfig tap1 up
sudo ip link set dev tap0 up
sudo ip link set dev tap1 up
```



### 查看所有的管理员

```shell
getent group sudo
```



### 编译汇编代码

```assembly
; nasm -f elf64 get_cpu_info.S -o get_cpu_info.o 
; ld -m elf_x86_64 get_cpu_info.o -o get_cpu_info.elf

section .data
    my_symbol_output db 'Manufacturer: ', 0 ; 预留输出字符串

section .bss
    manufacturer resb 16 ; 分配12字节存储制造商信息+4字节存储回车

section .text
    global _start

_start:
    ; 获取CPU制造商信息
    mov eax, 0 ; cpuid的0号功能：获取制造商ID
    cpuid
    ; ebx, edx, ecx包含制造商ID
    mov [manufacturer], ebx
    mov [manufacturer+4], edx
    mov [manufacturer+8], ecx
    mov eax, 0x00000D0A
    mov [manufacturer+12], eax

    ; 准备输出制造商信息
    mov rax, 1 ; 系统调用号：sys_write
    mov rdi, 1 ; 文件描述符：1代表标准输出
    mov rsi, my_symbol_output ; 输出字符串的地址
    mov rdx, 14 ; 输出字符串的长度
    syscall ; 执行系统调用

    ; 输出制造商ID
    mov rax, 1 ; 系统调用号：sys_write
    mov rdi, 1 ; 文件描述符：1代表标准输出
    mov rsi, manufacturer ; 制造商ID的地址
    mov rdx, 16 ; 制造商ID的长度
    syscall ; 执行系统调用

    ; 退出程序
    mov rax, 60 ; 系统调用号：sys_exit
    xor rdi, rdi ; 退出状态码
    syscall ; 执行系统调用
```

```bash
nasm -f elf32 test.S -o test.o   
ld -m elf_i386 test.o -o test.elf
./test.elf  
```





### 删除无法通过 shell 输入文件名的文件

**使用 inode 号码删除**

每个文件在Linux中都有一个唯一的inode号码`(索引节点, i节点)`，它可以用于标识文件。如果无法根据文件名删除文件，可以使用文件的inode号码。要查找文件的inode号码，请使用以下命令：

```bash
$ ls -li
# 31472500 -rw-rw-r-- 1 <USER> <GROUP>    0 May  27 03:32 ''$'\001\240''7@؁'$'\001''@8'
# 第一列的数字就是文件的 inode 号码, 通过以下命令查看文件然后删除
$ find . -inum [inode number]
$ find . -inum [inode number] -delete
```



### 创建虚拟内存 Swap Space

```shell
# https://docs.oracle.com/cd/E24457_01/html/E21988/giprn.html
# 创建一个存放 swapfile 的目录
sudo mkdir /swapfile && cd /swapfile/
# 在选定的目录中创建 Swap Space, 新的交换区大小为 30G
sudo dd if=/dev/zero of=<swapfile> bs=1024 count=30000000
# 初始化指定的 Swap Space
sudo mkswap -f <swapfile>
# 使用 swapon 启用新的 Swap Space
sudo swapon <swapfile>
# 查看交换空间是否添加成功
swapon -s

# 取消挂载 Swap Space
swapoff <swapfile>
```



### Ubunut 通过 PPA 安装高版本 NodeJS

```shell
curl -sL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get update
sudo apt-get install nodejs
node -v
```



### Ubuntu 忘记管理员密码强制修改

```shell
# Step 1: 进入 GRUB 引导, 并且选择 `recovery mode` 启动系统, 在 Vmware 中即为启动时按住 F2
```

<img src="https://qiniu.maikebuke.com/20231023155910.png" width="75%" />

```shell
# Step 2: 进入系统后, 在 `recovery menu` 中选择 `root`, 进入管理员模式
```

<img src="https://qiniu.maikebuke.com/20231023155947.png" width="75%" />

```shell
# Step 3: `recovery menu` 默认是 read-only 模式, 重新挂载磁盘为 read-write 模式 (这一步系统会卡死几秒钟)
mount -o remount,rw /
# Step 4: 使用 passwd 命令修改用户的密码
passwd ubuntu
```



### objdump

将二进制程序导出为汇编代码:

```shell
objdump -S ./elf_file > elf.s
```





### 查看某个头文件的依赖



```
pacman -F /usr/include/asm/unistd.h
# 陈宇 我这里不起作用
```





### 查询标准库函数原型

`man` 命令用于显示手册页，手册页分为不同的章节，每个章节涵盖不同类型的信息。`man 3` 中的 `3` 指的是第三章节，这一章节包含的是库函数（Library functions）的文档。以下是各章节的简单介绍：

1. **第1章**：用户命令（User Commands）
2. **第2章**：系统调用（System Calls）
3. **第3章**：库函数（Library Functions）
4. **第4章**：设备文件和特殊文件（Special Files and Drivers）
5. **第5章**：文件格式和约定（File Formats and Conventions）
6. **第6章**：游戏和趣味（Games and Screensavers）
7. **第7章**：杂项（Miscellaneous），例如宏包、协议、标准等
8. **第8章**：系统管理命令（System Administration Commands）和守护进程（Daemon）

使用 `man 3` 可以指定查看库函数的手册页。例如：

```bash
man 3 free
```

这条命令会显示 `free` 函数在第三章节中的手册页，提供函数的原型、描述、参数、返回值和其他相关信息。







### 查看指定进程的内存映射

```shell
pmap -X `pidof python3`
cat /proc/`pidof python3`/maps
```





### Linux查看所有活动的网络连接

```shell
sudo ss -tunap
```



### Linux 并行压缩

```bash
# 并行压缩
tar --use-compress-program="pigz -k -p100" -cvf onezibo_1219.tgz onezibo/*
# 并行解压
tar --use-compress-program="pigz -k -p8" -xvf output.tgz
```



### Linux 拷贝进度

```shell
rsync -ah --progress <source> <dest>
```



































